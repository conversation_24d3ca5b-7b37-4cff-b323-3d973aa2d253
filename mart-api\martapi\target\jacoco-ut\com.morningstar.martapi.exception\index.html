<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.morningstar.martapi.exception</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <span class="el_package">com.morningstar.martapi.exception</span></div><h1>com.morningstar.martapi.exception</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,283 of 1,283</td><td class="ctr2">0%</td><td class="bar">42 of 42</td><td class="ctr2">0%</td><td class="ctr1">81</td><td class="ctr2">81</td><td class="ctr1">274</td><td class="ctr2">274</td><td class="ctr1">60</td><td class="ctr2">60</td><td class="ctr1">15</td><td class="ctr2">15</td></tr></tfoot><tbody><tr><td id="a1"><a href="HoldingExceptionHandler.html" class="el_class">HoldingExceptionHandler</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="386" alt="386"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="22" alt="22"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">26</td><td class="ctr2" id="g0">26</td><td class="ctr1" id="h0">80</td><td class="ctr2" id="i0">80</td><td class="ctr1" id="j0">15</td><td class="ctr2" id="k0">15</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a11"><a href="TsExceptionHandler.html" class="el_class">TsExceptionHandler</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="77" height="10" title="248" alt="248"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="43" height="10" title="8" alt="8"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f2">12</td><td class="ctr2" id="g2">12</td><td class="ctr1" id="h1">51</td><td class="ctr2" id="i1">51</td><td class="ctr1" id="j2">8</td><td class="ctr2" id="k2">8</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a0"><a href="GridViewExceptionHandler.html" class="el_class">GridViewExceptionHandler</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="72" height="10" title="233" alt="233"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">12</td><td class="ctr2" id="g3">12</td><td class="ctr1" id="h2">49</td><td class="ctr2" id="i2">49</td><td class="ctr1" id="j1">12</td><td class="ctr2" id="k1">12</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a14"><a href="WebExceptionHandler.html" class="el_class">WebExceptionHandler</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="72" height="10" title="232" alt="232"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="65" height="10" title="12" alt="12"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">14</td><td class="ctr2" id="g1">14</td><td class="ctr1" id="h3">42</td><td class="ctr2" id="i3">42</td><td class="ctr1" id="j3">8</td><td class="ctr2" id="k3">8</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a7"><a href="LicenseExceptionHandler.html" class="el_class">LicenseExceptionHandler</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="38" height="10" title="124" alt="124"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">6</td><td class="ctr2" id="g4">6</td><td class="ctr1" id="h4">26</td><td class="ctr2" id="i4">26</td><td class="ctr1" id="j4">6</td><td class="ctr2" id="k4">6</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a8"><a href="TranscriptApiException.html" class="el_class">TranscriptApiException</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="11" alt="11"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h5">4</td><td class="ctr2" id="i5">4</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td><td class="ctr1" id="l5">1</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a6"><a href="JsonDownloadException.html" class="el_class">JsonDownloadException</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="9" alt="9"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h6">4</td><td class="ctr2" id="i6">4</td><td class="ctr1" id="j5">2</td><td class="ctr2" id="k5">2</td><td class="ctr1" id="l6">1</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a2"><a href="HoldingValidationException.html" class="el_class">HoldingValidationException</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="8" alt="8"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">3</td><td class="ctr2" id="i7">3</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td><td class="ctr1" id="l7">1</td><td class="ctr2" id="m7">1</td></tr><tr><td id="a13"><a href="ValidationException.html" class="el_class">ValidationException</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="8" alt="8"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">3</td><td class="ctr2" id="i8">3</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td><td class="ctr1" id="l8">1</td><td class="ctr2" id="m8">1</td></tr><tr><td id="a12"><a href="TsGridViewValidationException.html" class="el_class">TsGridViewValidationException</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">2</td><td class="ctr2" id="i9">2</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td><td class="ctr1" id="l9">1</td><td class="ctr2" id="m9">1</td></tr><tr><td id="a9"><a href="TsCacheApiValidationException.html" class="el_class">TsCacheApiValidationException</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">2</td><td class="ctr2" id="i10">2</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td><td class="ctr1" id="l10">1</td><td class="ctr2" id="m10">1</td></tr><tr><td id="a4"><a href="IndexAPIException.html" class="el_class">IndexAPIException</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">2</td><td class="ctr2" id="i11">2</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td><td class="ctr1" id="l11">1</td><td class="ctr2" id="m11">1</td></tr><tr><td id="a10"><a href="TsCacheProtobufValidationException.html" class="el_class">TsCacheProtobufValidationException</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">2</td><td class="ctr2" id="i12">2</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td><td class="ctr1" id="l12">1</td><td class="ctr2" id="m12">1</td></tr><tr><td id="a3"><a href="IllegalGzipRequestException.html" class="el_class">IllegalGzipRequestException</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">2</td><td class="ctr2" id="i13">2</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td><td class="ctr1" id="l13">1</td><td class="ctr2" id="m13">1</td></tr><tr><td id="a5"><a href="InvestmentApiValidationException.html" class="el_class">InvestmentApiValidationException</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">2</td><td class="ctr2" id="i14">2</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td><td class="ctr1" id="l14">1</td><td class="ctr2" id="m14">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>