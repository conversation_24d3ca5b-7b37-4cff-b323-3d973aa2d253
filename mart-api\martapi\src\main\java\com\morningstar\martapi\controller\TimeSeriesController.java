package com.morningstar.martapi.controller;

import static com.morningstar.martapi.config.Constant.LOG_REQUEST_PAYLOAD_LIMIT_MILLISECONDS;
import static com.morningstar.martapi.config.Constant.RESPONSE;
import static com.morningstar.martapi.config.Constant.RESPONSE_ERROR;
import static com.morningstar.martapi.config.Constant.SUCCESSFUL_RESPONSE;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.EVENT_DESCRIPTION;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.EVENT_TYPE;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.EXCEPTION_TYPE;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.EXECUTE_TIME;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.PRODUCT_ID;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.REQUEST_PARAM;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.REQUEST_PAYLOAD;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.REQUEST_TYPE;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.USER_ID;

import com.morningstar.martapi.exception.TsCacheApiValidationException;
import com.morningstar.martapi.exception.TsCacheProtobufValidationException;
import com.morningstar.martapi.exception.TsGridViewValidationException;
import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.martgateway.domains.timeseries.entity.TSResponse;
import com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf;
import com.morningstar.martgateway.infrastructures.log.LogAttribute;
import com.morningstar.martgateway.infrastructures.log.LogEntity;
import com.morningstar.martgateway.infrastructures.log.LogEntry;
import com.morningstar.martgateway.infrastructures.log.LogHelper;
import com.morningstar.martgateway.interfaces.MartGateway;
import com.morningstar.martcommon.entity.result.request.MartRequest;
import com.morningstar.martgateway.util.JsonUtils;
import com.morningstar.martgateway.util.JwtUtil;
import io.swagger.annotations.ApiParam;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping(value = {"/v1/time-series-data/investments/{investment-ids}", "/investment-api/v1/time-series-data/investments/{investment-ids}"},
        produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE})
public class TimeSeriesController {

    private final MartGateway<TSResponse, MartRequest> tsOldRspGateway;
    private final MartGateway<InvestmentResponse, MartRequest> tsNewRspGateway;

    private final RequestValidationHandler<HeadersAndParams, MartRequest> validator;

    private static final String REQUEST_TYPE_VALUE = "tscache";

    @Autowired
    public TimeSeriesController(@Qualifier("tsOldRspGateway") MartGateway<TSResponse, MartRequest> tsOldRspGateway,
            @Qualifier("tsNewRspGateway") MartGateway<InvestmentResponse, MartRequest> tsNewRspGateway,
            @Qualifier("timeSeriesApiValidator") RequestValidationHandler<HeadersAndParams,MartRequest> validator) {
        this.tsOldRspGateway = tsOldRspGateway;
        this.tsNewRspGateway = tsNewRspGateway;
        this.validator = validator;
    }


    private void validateRequest(MartRequest martRequest, HeadersAndParams headersAndParams) {
        validator.validateHeadersAndParams(headersAndParams);
        validator.validateRequestBody(martRequest);
    }

    @GetMapping(value = "")
    public Mono<InvestmentResponse> getInvestmentTSData(
            @ApiParam(example = "0P00000AWG,0P00000AYI,0P00000AZ0")
            @PathVariable("investment-ids") String investmentIds,
            @ApiParam(example = "119114,119115")
            @RequestParam(value = "dataPoints", required = false) String dataPoints, //check will be handled by the validateRequest below
            @ApiParam(example = "2022-10-31")
            @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate,
            @ApiParam(example = "2022-11-30")
            @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate,
            @ApiParam(example = "EUR")
            @RequestParam(value = "currency", required = false, defaultValue = "") String currency,
            @ApiParam(example = "USD")
            @RequestParam(value = "preCurrency", required = false, defaultValue = "") String preCurrency,
            @ApiParam(example = "false")
            @RequestParam(value = "readCache", required = false, defaultValue = "") String readCache,
            @RequestParam(value= "dateFormat", required = false, defaultValue = "") String dateFormat,
            @RequestParam(value= "decimalFormat", required = false, defaultValue = "") String decimalFormat,
            @RequestParam(value= "extendPerformance", required = false, defaultValue = "") String extendPerformance,
            @RequestParam(value= "postTax", required = false, defaultValue = "") String postTax,
            @RequestParam(value= "useRequireId", required = false, defaultValue = "false") String useRequireId,
            @RequestParam(value= "useCase", required = false, defaultValue = "feed") String useCase,
            @RequestParam(value= "useNewCCS", required = false, defaultValue = "false") String useNewCCS,
            @RequestHeader(value = "X-Api-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "X-API-TraceId", required = false, defaultValue = "") String traceId,
            @RequestHeader(value = "X-API-UserId", required = false, defaultValue = "") String headerUserId,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String token,
            @RequestHeader(value= "checkEntitlement", required = false, defaultValue = "true") String checkEntitlement,
            @RequestHeader(value= "X-API-Entitlement-Product", required = false, defaultValue = "") String entitlementProductId,
            ServerHttpRequest request
    ) {
        String reqId = StringUtils.isNotEmpty(requestId) ? requestId : UUID.randomUUID().toString();
        long startTime = System.currentTimeMillis();

        String userId = getUserIdFromToken(headerUserId, token);
        String configId = getConfigIdFromToken(token);

        List<String> dps = (StringUtils.isNotEmpty(dataPoints)) ? new ArrayList<>(Arrays.asList(dataPoints.split(","))) : Collections.emptyList();
        MartRequest martRequest = MartRequest.builder()
                .currency(currency)
                .dps(dps)
                .ids(new ArrayList<>(Arrays.asList(investmentIds.split(","))))
                .startDate(startDate)
                .endDate(endDate)
                .preCurrency(preCurrency)
                .readCache(readCache)
                .productId(productId)
                .entitlementProductId(entitlementProductId)
                .requestId(requestId)
                .userId(userId)
                .dateFormat(dateFormat)
                .decimalFormat(decimalFormat)
                .extendedPerformance(extendPerformance)
                .postTax(postTax)
                .useRequireId(Boolean.parseBoolean(useRequireId))
                .checkEntitlement(Boolean.parseBoolean(checkEntitlement))
                .useCase(useCase)
                .useNewCCS(Boolean.parseBoolean(useNewCCS))
                .configId(configId)
                .build();

        HeadersAndParams headersAndParams = HeadersAndParams.builder()
                .authorizationToken(token)
                .productId(productId)
                .requestId(requestId)
                .build();
        try {
            validateRequest(martRequest, headersAndParams);
        } catch (ValidationException e) {
            throw new TsGridViewValidationException(e.getStatus());
        }

        return tsNewRspGateway.asyncRetrieveSecurities(martRequest)
                .doOnEach(LogHelper.logOnNext(list -> {
                    MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), reqId);
                    long executionTime = System.currentTimeMillis() - startTime;
                    List<LogEntity> logEntities = Stream.of(
                            new LogEntity(EVENT_TYPE, RESPONSE),
                            new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                            new LogEntity(PRODUCT_ID, productId),
                            new LogEntity(USER_ID, userId),
                            new LogEntity(REQUEST_PARAM, martRequest.getRequestParam(request.getURI().getPath(), request.getURI().getQuery())),
                            new LogEntity(EXECUTE_TIME, executionTime),
                            new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE)
                    ).collect(Collectors.toCollection(ArrayList::new));
                    addRequestPayload(martRequest, executionTime, logEntities);
                    LogEntry.info(logEntities.toArray(LogEntity[]::new));
                })).doOnEach(LogHelper.logOnError(e -> {
                    MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), reqId);
                    LogEntry.error(
                            new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                            new LogEntity(EVENT_DESCRIPTION, e),
                            new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
                            new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(martRequest)),
                            new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime),
                            new LogEntity(EXCEPTION_TYPE, e.getClass()),
                            new LogEntity(PRODUCT_ID, productId),
                            new LogEntity(USER_ID, userId),
                            new LogEntity(REQUEST_PARAM, martRequest.getRequestParam(request.getURI().getPath(), request.getURI().getQuery()))
                    );
                }));
    }
    @GetMapping(value = "", params = "format=1", produces = "application/json")
    public Mono<TSResponse> retrieveTimeSeriesData(
            @ApiParam(example = "0P00000AWG,0P00000AYI,0P00000AZ0")
            @PathVariable("investment-ids") String investmentIds,
            @ApiParam(example = "119114,119115")
            @RequestParam(value = "dataPoints", required = false) String dataPoints, //check will be handled by the validateRequest below
            @ApiParam(example = "2022-10-31")
            @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate,
            @ApiParam(example = "2022-11-30")
            @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate,
            @ApiParam(example = "EUR")
            @RequestParam(value = "currency", required = false, defaultValue = "") String currency,
            @ApiParam(example = "USD")
            @RequestParam(value = "preCurrency", required = false, defaultValue = "") String preCurrency,
            @ApiParam(example = "false")
            @RequestParam(value = "readCache", required = false, defaultValue = "") String readCache,
            @RequestParam(value= "dateFormat", required = false, defaultValue = "") String dateFormat,
            @RequestParam(value= "decimalFormat", required = false, defaultValue = "") String decimalFormat,
            @RequestParam(value= "extendPerformance", required = false, defaultValue = "") String extendPerformance,
            @RequestParam(value= "postTax", required = false, defaultValue = "") String postTax,
            @RequestParam(value= "useRequireId", required = false, defaultValue = "false") String useRequireId,
            @RequestParam(value= "useCase", required = false, defaultValue = "feed") String useCase,
            @RequestParam(value= "useNewCCS", required = false, defaultValue = "false") String useNewCCS,
            @RequestHeader(value = "X-Api-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "X-API-TraceId", required = false, defaultValue = "") String traceId,
            @RequestHeader(value = "X-API-UserId", required = false, defaultValue = "") String headerUserId,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String token,
            @RequestHeader(value= "checkEntitlement", required = false, defaultValue = "true") String checkEntitlement,
            @RequestHeader(value= "X-API-Entitlement-Product", required = false, defaultValue = "") String entitlementProductId,
            ServerHttpRequest request
    ) {
        String reqId = StringUtils.isNotEmpty(requestId) ? requestId : UUID.randomUUID().toString();
        long startTime = System.currentTimeMillis();

        String userId = getUserIdFromToken(headerUserId, token);
        String configId = getConfigIdFromToken(token);

        List<String> dps = (StringUtils.isNotEmpty(dataPoints)) ? new ArrayList<>(Arrays.asList(dataPoints.split(","))) : Collections.emptyList();
        MartRequest martRequest = MartRequest.builder()
                .currency(currency)
                .dps(dps)
                .ids(new ArrayList<>(Arrays.asList(investmentIds.split(","))))
                .startDate(startDate)
                .endDate(endDate)
                .preCurrency(preCurrency)
                .readCache(readCache)
                .productId(productId)
                .entitlementProductId(entitlementProductId)
                .requestId(reqId)
                .userId(userId)
                .dateFormat(dateFormat)
                .decimalFormat(decimalFormat)
                .extendedPerformance(extendPerformance)
                .postTax(postTax)
                .useRequireId(Boolean.parseBoolean(useRequireId))
                .checkEntitlement(Boolean.parseBoolean(checkEntitlement))
                .useCase(useCase)
                .useNewCCS(Boolean.parseBoolean(useNewCCS))
                .configId(configId)
                .build();

        HeadersAndParams headersAndParams = HeadersAndParams.builder()
                .authorizationToken(token)
                .productId(productId)
                .requestId(requestId)
                .build();
        try {
            validateRequest(martRequest, headersAndParams);
        } catch (ValidationException e) {
            throw new TsCacheApiValidationException(e.getStatus());
        }

        return tsOldRspGateway.asyncRetrieveSecurities(martRequest)
                .doOnEach(LogHelper.logOnNext(list -> {
                    MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), reqId);
                    long executionTime = System.currentTimeMillis() - startTime;
                    List<LogEntity> logEntities = Stream.of(
                            new LogEntity(EVENT_TYPE, RESPONSE),
                            new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                            new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
                            new LogEntity(PRODUCT_ID, productId),
                            new LogEntity(USER_ID, userId),
                            new LogEntity(REQUEST_PARAM, martRequest.getRequestParam(request.getURI().getPath(), request.getURI().getQuery())),
                            new LogEntity(EXECUTE_TIME, executionTime)
                    ).collect(Collectors.toCollection(ArrayList::new));
                    addRequestPayload(martRequest, executionTime, logEntities);
                    LogEntry.info(logEntities.toArray(LogEntity[]::new));
                })).doOnEach(LogHelper.logOnError(e -> {
                    MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), reqId);
                    LogEntry.error(
                            new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                            new LogEntity(EVENT_DESCRIPTION, e),
                            new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
                            new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(martRequest)),
                            new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime),
                            new LogEntity(EXCEPTION_TYPE, e.getClass()),
                            new LogEntity(PRODUCT_ID, productId),
                            new LogEntity(USER_ID, userId),
                            new LogEntity(REQUEST_PARAM, martRequest.getRequestParam(request.getURI().getPath(), request.getURI().getQuery()))
                    );
                }));
    }

    private static String getUserIdFromToken(String headerUserId, String token) {
       return StringUtils.defaultIfEmpty(JwtUtil.getFieldValue(token, "https://morningstar.com/mstar_id"), headerUserId);
    }

    private static String getConfigIdFromToken(String token) {
        return JwtUtil.getFieldValue(token, "https://morningstar.com/config_id");
    }

    @GetMapping(value = "", params = "format=0", produces = "application/x-protobuf")
    public Mono<TsCacheDataForProtoBuf.TimeSeriesDatas> retrieveTimeSeriesDataAsProtobuf(
            @ApiParam(example = "0P00000AWG,0P00000AYI,0P00000AZ0")
            @PathVariable("investment-ids") String investmentIds,
            @ApiParam(example = "119114,119115")
            @RequestParam(value = "dataPoints", required = false) String dataPoints, //check will be handled by the validateRequest below
            @ApiParam(example = "2022-10-31")
            @RequestParam(value = "startDate", required = false, defaultValue = "") String startDate,
            @ApiParam(example = "2022-11-30")
            @RequestParam(value = "endDate", required = false, defaultValue = "") String endDate,
            @ApiParam(example = "EUR")
            @RequestParam(value = "currency", required = false, defaultValue = "") String currency,
            @ApiParam(example = "USD")
            @RequestParam(value = "preCurrency", required = false, defaultValue = "") String preCurrency,
            @ApiParam(example = "false")
            @RequestParam(value = "readCache", required = false, defaultValue = "") String readCache,
            @RequestParam(value= "dateFormat", required = false, defaultValue = "") String dateFormat,
            @RequestParam(value= "decimalFormat", required = false, defaultValue = "") String decimalFormat,
            @RequestParam(value= "extendPerformance", required = false, defaultValue = "") String extendPerformance,
            @RequestParam(value= "postTax", required = false, defaultValue = "") String postTax,
            @RequestParam(value= "useRequireId", required = false, defaultValue = "false") String useRequireId,
            @RequestParam(value= "useCase", required = false, defaultValue = "feed") String useCase,
            @RequestParam(value= "useNewCCS", required = false, defaultValue = "false") String useNewCCS,
            @RequestHeader(value = "X-Api-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "X-API-TraceId", required = false, defaultValue = "") String traceId,
            @RequestHeader(value = "X-API-UserId", required = false, defaultValue = "") String headerUserId,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String token,
            @RequestHeader(value= "checkEntitlement", required = false, defaultValue = "true") String checkEntitlement,
            @RequestHeader(value= "X-API-Entitlement-Product", required = false, defaultValue = "") String entitlementProductId,
            ServerHttpRequest request
    ) {
        String reqId = StringUtils.isNotEmpty(requestId) ? requestId : UUID.randomUUID().toString();
        long startTime = System.currentTimeMillis();

        String userId = getUserIdFromToken(headerUserId, token);
        String configId = getConfigIdFromToken(token);

        List<String> dps = (StringUtils.isNotEmpty(dataPoints)) ? new ArrayList<>(Arrays.asList(dataPoints.split(","))) : Collections.emptyList();
        MartRequest martRequest = MartRequest.builder()
                .currency(currency)
                .dps(dps)
                .ids(new ArrayList<>(Arrays.asList(investmentIds.split(","))))
                .startDate(startDate)
                .endDate(endDate)
                .preCurrency(preCurrency)
                .readCache(readCache)
                .productId(productId)
                .entitlementProductId(entitlementProductId)
                .requestId(reqId)
                .userId(userId)
                .dateFormat(dateFormat)
                .decimalFormat(decimalFormat)
                .extendedPerformance(extendPerformance)
                .postTax(postTax)
                .useRequireId("true".equalsIgnoreCase(useRequireId))
                .checkEntitlement(Boolean.parseBoolean(checkEntitlement))
                .useCase(useCase)
                .useNewCCS(Boolean.parseBoolean(useNewCCS))
                .configId(configId)
                .build();

        HeadersAndParams headersAndParams = HeadersAndParams.builder()
                .authorizationToken(token)
                .productId(productId)
                .requestId(requestId)
                .build();

        try {
            validateRequest(martRequest, headersAndParams);
        } catch (ValidationException e) {
            throw new TsCacheProtobufValidationException(e.getStatus());
        }

        return tsOldRspGateway.asyncRetrieveSecurities(martRequest).map(TSResponse::toProtobuf)
                .doOnEach(LogHelper.logOnNext(list -> {
                    MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), reqId);
                    long executionTime = System.currentTimeMillis() - startTime;
                    List<LogEntity> logEntities = Stream.of(
                            new LogEntity(EVENT_TYPE, RESPONSE),
                            new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                            new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
                            new LogEntity(PRODUCT_ID, productId),
                            new LogEntity(USER_ID, userId),
                            new LogEntity(REQUEST_PARAM, martRequest.getRequestParam(request.getURI().getPath(), request.getURI().getQuery())),
                            new LogEntity(EXECUTE_TIME, executionTime),
                            new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE)
                    ).collect(Collectors.toCollection(ArrayList::new));
                    addRequestPayload(martRequest, executionTime, logEntities);
                    LogEntry.info(logEntities.toArray(LogEntity[]::new));
                })).doOnEach(LogHelper.logOnError(e -> {
                    MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), reqId);
                    LogEntry.error(
                            new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                            new LogEntity(EVENT_DESCRIPTION, e),
                            new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
                            new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(martRequest)),
                            new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime),
                            new LogEntity(EXCEPTION_TYPE, e.getClass()),
                            new LogEntity(PRODUCT_ID, productId),
                            new LogEntity(USER_ID, userId),
                            new LogEntity(REQUEST_PARAM, martRequest.getRequestParam(request.getURI().getPath(), request.getURI().getQuery()))
                    );
                }));
    }

    private void addRequestPayload(MartRequest martRequest, long executionTime, List<LogEntity> logEntities) {
        if (executionTime > LOG_REQUEST_PAYLOAD_LIMIT_MILLISECONDS) {
            logEntities.add(new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(martRequest)));
        }
    }

}
