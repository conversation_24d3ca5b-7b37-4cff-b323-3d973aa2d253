<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TsCacheDataForProtoBuf.TimeSeriesData.Builder</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <a href="index.html" class="el_package">com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf</a> &gt; <span class="el_class">TsCacheDataForProtoBuf.TimeSeriesData.Builder</span></div><h1>TsCacheDataForProtoBuf.TimeSeriesData.Builder</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,267 of 1,267</td><td class="ctr2">0%</td><td class="bar">147 of 147</td><td class="ctr2">0%</td><td class="ctr1">143</td><td class="ctr2">143</td><td class="ctr1">357</td><td class="ctr2">357</td><td class="ctr1">67</td><td class="ctr2">67</td></tr></tfoot><tbody><tr><td id="a49"><a href="TsCacheDataForProtoBuf.java.html#L2825" class="el_method">mergeFrom(TsCacheDataForProtoBuf.TimeSeriesData)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="146" alt="146"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="22" alt="22"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">12</td><td class="ctr2" id="g0">12</td><td class="ctr1" id="h0">38</td><td class="ctr2" id="i0">38</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a47"><a href="TsCacheDataForProtoBuf.java.html#L2890" class="el_method">mergeFrom(CodedInputStream, ExtensionRegistryLite)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="83" height="10" title="102" alt="102"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="81" height="10" title="15" alt="15"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">11</td><td class="ctr2" id="g1">11</td><td class="ctr1" id="h1">34</td><td class="ctr2" id="i1">34</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a10"><a href="TsCacheDataForProtoBuf.java.html#L2761" class="el_method">buildPartial0(TsCacheDataForProtoBuf.TimeSeriesData)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="49" height="10" title="60" alt="60"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="43" height="10" title="8" alt="8"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">5</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h2">16</td><td class="ctr2" id="i2">16</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a12"><a href="TsCacheDataForProtoBuf.java.html#L2703" class="el_method">clear()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="39" alt="39"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f12">2</td><td class="ctr2" id="g12">2</td><td class="ctr1" id="h3">12</td><td class="ctr2" id="i3">12</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a11"><a href="TsCacheDataForProtoBuf.java.html#L2749" class="el_method">buildPartialRepeatedFields(TsCacheDataForProtoBuf.TimeSeriesData)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="30" alt="30"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="4" alt="4"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f3">3</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h13">7</td><td class="ctr2" id="i13">7</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a63"><a href="TsCacheDataForProtoBuf.java.html#L3158" class="el_method">setValues(int, TsCacheDataForProtoBuf.TSValuePair)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="28" alt="28"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="4" alt="4"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f4">3</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h4">8</td><td class="ctr2" id="i4">8</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a37"><a href="TsCacheDataForProtoBuf.java.html#L3339" class="el_method">getValuesFieldBuilder()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="28" alt="28"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="4" alt="4"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f5">3</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h18">6</td><td class="ctr2" id="i18">6</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a2"><a href="TsCacheDataForProtoBuf.java.html#L3205" class="el_method">addValues(int, TsCacheDataForProtoBuf.TSValuePair)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="27" alt="27"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="4" alt="4"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f6">3</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h5">8</td><td class="ctr2" id="i5">8</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a4"><a href="TsCacheDataForProtoBuf.java.html#L3188" class="el_method">addValues(TsCacheDataForProtoBuf.TSValuePair)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="26" alt="26"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="4" alt="4"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f7">3</td><td class="ctr2" id="g7">3</td><td class="ctr1" id="h6">8</td><td class="ctr2" id="i6">8</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a64"><a href="TsCacheDataForProtoBuf.java.html#L3175" class="el_method">setValues(int, TsCacheDataForProtoBuf.TSValuePair.Builder)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="24" alt="24"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d13"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f13">2</td><td class="ctr2" id="g13">2</td><td class="ctr1" id="h19">6</td><td class="ctr2" id="i19">6</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a29"><a href="TsCacheDataForProtoBuf.java.html#L2964" class="el_method">getSecId()</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="23" alt="23"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="4" alt="4"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f8">3</td><td class="ctr2" id="g8">3</td><td class="ctr1" id="h7">8</td><td class="ctr2" id="i7">8</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a31"><a href="TsCacheDataForProtoBuf.java.html#L3044" class="el_method">getUniverse()</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="23" alt="23"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="4" alt="4"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f9">3</td><td class="ctr2" id="g9">3</td><td class="ctr1" id="h8">8</td><td class="ctr2" id="i8">8</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a3"><a href="TsCacheDataForProtoBuf.java.html#L3236" class="el_method">addValues(int, TsCacheDataForProtoBuf.TSValuePair.Builder)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="23" alt="23"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d14"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f14">2</td><td class="ctr2" id="g14">2</td><td class="ctr1" id="h20">6</td><td class="ctr2" id="i20">6</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a22"><a href="TsCacheDataForProtoBuf.java.html#L3364" class="el_method">getDataId()</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="23" alt="23"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="4" alt="4"/></td><td class="ctr2" id="e13">0%</td><td class="ctr1" id="f10">3</td><td class="ctr2" id="g10">3</td><td class="ctr1" id="h9">8</td><td class="ctr2" id="i9">8</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a27"><a href="TsCacheDataForProtoBuf.java.html#L3444" class="el_method">getErrorCode()</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="23" alt="23"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="4" alt="4"/></td><td class="ctr2" id="e14">0%</td><td class="ctr1" id="f11">3</td><td class="ctr2" id="g11">3</td><td class="ctr1" id="h10">8</td><td class="ctr2" id="i10">8</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a5"><a href="TsCacheDataForProtoBuf.java.html#L3222" class="el_method">addValues(TsCacheDataForProtoBuf.TSValuePair.Builder)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="22" alt="22"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d15"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e15">0%</td><td class="ctr1" id="f15">2</td><td class="ctr2" id="g15">2</td><td class="ctr1" id="h21">6</td><td class="ctr2" id="i21">6</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a19"><a href="TsCacheDataForProtoBuf.java.html#L3264" class="el_method">clearValues()</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="20" alt="20"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d16"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e16">0%</td><td class="ctr1" id="f16">2</td><td class="ctr2" id="g16">2</td><td class="ctr1" id="h22">6</td><td class="ctr2" id="i22">6</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a66"><a href="TsCacheDataForProtoBuf.java.html#L2698" class="el_method">TsCacheDataForProtoBuf.TimeSeriesData.Builder(GeneratedMessageV3.BuilderParent)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="19" alt="19"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d45"/><td class="ctr2" id="e45">n/a</td><td class="ctr1" id="f45">1</td><td class="ctr2" id="g45">1</td><td class="ctr1" id="h11">8</td><td class="ctr2" id="i11">8</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a58"><a href="TsCacheDataForProtoBuf.java.html#L3001" class="el_method">setSecId(String)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="19" alt="19"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d17"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e17">0%</td><td class="ctr1" id="f17">2</td><td class="ctr2" id="g17">2</td><td class="ctr1" id="h25">5</td><td class="ctr2" id="i25">5</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a59"><a href="TsCacheDataForProtoBuf.java.html#L3024" class="el_method">setSecIdBytes(ByteString)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="19" alt="19"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d18"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e18">0%</td><td class="ctr1" id="f18">2</td><td class="ctr2" id="g18">2</td><td class="ctr1" id="h26">5</td><td class="ctr2" id="i26">5</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a60"><a href="TsCacheDataForProtoBuf.java.html#L3081" class="el_method">setUniverse(String)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="19" alt="19"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d19"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e19">0%</td><td class="ctr1" id="f19">2</td><td class="ctr2" id="g19">2</td><td class="ctr1" id="h27">5</td><td class="ctr2" id="i27">5</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a61"><a href="TsCacheDataForProtoBuf.java.html#L3104" class="el_method">setUniverseBytes(ByteString)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="19" alt="19"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d20"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e20">0%</td><td class="ctr1" id="f20">2</td><td class="ctr2" id="g20">2</td><td class="ctr1" id="h28">5</td><td class="ctr2" id="i28">5</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a21"><a href="TsCacheDataForProtoBuf.java.html#L3114" class="el_method">ensureValuesIsMutable()</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="19" alt="19"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d21"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e21">0%</td><td class="ctr1" id="f21">2</td><td class="ctr2" id="g21">2</td><td class="ctr1" id="h34">4</td><td class="ctr2" id="i34">4</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a0"><a href="TsCacheDataForProtoBuf.java.html#L3250" class="el_method">addAllValues(Iterable)</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="19" alt="19"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d22"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e22">0%</td><td class="ctr1" id="f22">2</td><td class="ctr2" id="g22">2</td><td class="ctr1" id="h23">6</td><td class="ctr2" id="i23">6</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a51"><a href="TsCacheDataForProtoBuf.java.html#L3277" class="el_method">removeValues(int)</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="19" alt="19"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d23"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e23">0%</td><td class="ctr1" id="f23">2</td><td class="ctr2" id="g23">2</td><td class="ctr1" id="h24">6</td><td class="ctr2" id="i24">6</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a52"><a href="TsCacheDataForProtoBuf.java.html#L3401" class="el_method">setDataId(String)</a></td><td class="bar" id="b25"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="19" alt="19"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d24"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e24">0%</td><td class="ctr1" id="f24">2</td><td class="ctr2" id="g24">2</td><td class="ctr1" id="h29">5</td><td class="ctr2" id="i29">5</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a53"><a href="TsCacheDataForProtoBuf.java.html#L3424" class="el_method">setDataIdBytes(ByteString)</a></td><td class="bar" id="b26"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="19" alt="19"/></td><td class="ctr2" id="c26">0%</td><td class="bar" id="d25"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e25">0%</td><td class="ctr1" id="f25">2</td><td class="ctr2" id="g25">2</td><td class="ctr1" id="h30">5</td><td class="ctr2" id="i30">5</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a54"><a href="TsCacheDataForProtoBuf.java.html#L3481" class="el_method">setErrorCode(String)</a></td><td class="bar" id="b27"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="19" alt="19"/></td><td class="ctr2" id="c27">0%</td><td class="bar" id="d26"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e26">0%</td><td class="ctr1" id="f26">2</td><td class="ctr2" id="g26">2</td><td class="ctr1" id="h31">5</td><td class="ctr2" id="i31">5</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a55"><a href="TsCacheDataForProtoBuf.java.html#L3504" class="el_method">setErrorCodeBytes(ByteString)</a></td><td class="bar" id="b28"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="19" alt="19"/></td><td class="ctr2" id="c28">0%</td><td class="bar" id="d27"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e27">0%</td><td class="ctr1" id="f27">2</td><td class="ctr2" id="g27">2</td><td class="ctr1" id="h32">5</td><td class="ctr2" id="i32">5</td><td class="ctr1" id="j28">1</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a65"><a href="TsCacheDataForProtoBuf.java.html#L2692" class="el_method">TsCacheDataForProtoBuf.TimeSeriesData.Builder()</a></td><td class="bar" id="b29"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="18" alt="18"/></td><td class="ctr2" id="c29">0%</td><td class="bar" id="d46"/><td class="ctr2" id="e46">n/a</td><td class="ctr1" id="f46">1</td><td class="ctr2" id="g46">1</td><td class="ctr1" id="h12">8</td><td class="ctr2" id="i12">8</td><td class="ctr1" id="j29">1</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a9"><a href="TsCacheDataForProtoBuf.java.html#L2741" class="el_method">buildPartial()</a></td><td class="bar" id="b30"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="18" alt="18"/></td><td class="ctr2" id="c30">0%</td><td class="bar" id="d28"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e28">0%</td><td class="ctr1" id="f28">2</td><td class="ctr2" id="g28">2</td><td class="ctr1" id="h33">5</td><td class="ctr2" id="i33">5</td><td class="ctr1" id="j30">1</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a30"><a href="TsCacheDataForProtoBuf.java.html#L2983" class="el_method">getSecIdBytes()</a></td><td class="bar" id="b31"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="18" alt="18"/></td><td class="ctr2" id="c31">0%</td><td class="bar" id="d29"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e29">0%</td><td class="ctr1" id="f29">2</td><td class="ctr2" id="g29">2</td><td class="ctr1" id="h14">7</td><td class="ctr2" id="i14">7</td><td class="ctr1" id="j31">1</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a32"><a href="TsCacheDataForProtoBuf.java.html#L3063" class="el_method">getUniverseBytes()</a></td><td class="bar" id="b32"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="18" alt="18"/></td><td class="ctr2" id="c32">0%</td><td class="bar" id="d30"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e30">0%</td><td class="ctr1" id="f30">2</td><td class="ctr2" id="g30">2</td><td class="ctr1" id="h15">7</td><td class="ctr2" id="i15">7</td><td class="ctr1" id="j32">1</td><td class="ctr2" id="k32">1</td></tr><tr><td id="a23"><a href="TsCacheDataForProtoBuf.java.html#L3383" class="el_method">getDataIdBytes()</a></td><td class="bar" id="b33"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="18" alt="18"/></td><td class="ctr2" id="c33">0%</td><td class="bar" id="d31"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e31">0%</td><td class="ctr1" id="f31">2</td><td class="ctr2" id="g31">2</td><td class="ctr1" id="h16">7</td><td class="ctr2" id="i16">7</td><td class="ctr1" id="j33">1</td><td class="ctr2" id="k33">1</td></tr><tr><td id="a28"><a href="TsCacheDataForProtoBuf.java.html#L3463" class="el_method">getErrorCodeBytes()</a></td><td class="bar" id="b34"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="18" alt="18"/></td><td class="ctr2" id="c34">0%</td><td class="bar" id="d32"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e32">0%</td><td class="ctr1" id="f32">2</td><td class="ctr2" id="g32">2</td><td class="ctr1" id="h17">7</td><td class="ctr2" id="i17">7</td><td class="ctr1" id="j34">1</td><td class="ctr2" id="k34">1</td></tr><tr><td id="a33"><a href="TsCacheDataForProtoBuf.java.html#L3147" class="el_method">getValues(int)</a></td><td class="bar" id="b35"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="15" alt="15"/></td><td class="ctr2" id="c35">0%</td><td class="bar" id="d33"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e33">0%</td><td class="ctr1" id="f33">2</td><td class="ctr2" id="g33">2</td><td class="ctr1" id="h41">3</td><td class="ctr2" id="i41">3</td><td class="ctr1" id="j35">1</td><td class="ctr2" id="k35">1</td></tr><tr><td id="a39"><a href="TsCacheDataForProtoBuf.java.html#L3298" class="el_method">getValuesOrBuilder(int)</a></td><td class="bar" id="b36"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="15" alt="15"/></td><td class="ctr2" id="c36">0%</td><td class="bar" id="d34"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e34">0%</td><td class="ctr1" id="f34">2</td><td class="ctr2" id="g34">2</td><td class="ctr1" id="h42">3</td><td class="ctr2" id="i42">3</td><td class="ctr1" id="j36">1</td><td class="ctr2" id="k36">1</td></tr><tr><td id="a48"><a href="TsCacheDataForProtoBuf.java.html#L2816" class="el_method">mergeFrom(Message)</a></td><td class="bar" id="b37"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="14" alt="14"/></td><td class="ctr2" id="c37">0%</td><td class="bar" id="d35"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e35">0%</td><td class="ctr1" id="f35">2</td><td class="ctr2" id="g35">2</td><td class="ctr1" id="h35">4</td><td class="ctr2" id="i35">4</td><td class="ctr1" id="j37">1</td><td class="ctr2" id="k37">1</td></tr><tr><td id="a17"><a href="TsCacheDataForProtoBuf.java.html#L3012" class="el_method">clearSecId()</a></td><td class="bar" id="b38"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="14" alt="14"/></td><td class="ctr2" id="c38">0%</td><td class="bar" id="d47"/><td class="ctr2" id="e47">n/a</td><td class="ctr1" id="f47">1</td><td class="ctr2" id="g47">1</td><td class="ctr1" id="h36">4</td><td class="ctr2" id="i36">4</td><td class="ctr1" id="j38">1</td><td class="ctr2" id="k38">1</td></tr><tr><td id="a18"><a href="TsCacheDataForProtoBuf.java.html#L3092" class="el_method">clearUniverse()</a></td><td class="bar" id="b39"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="14" alt="14"/></td><td class="ctr2" id="c39">0%</td><td class="bar" id="d48"/><td class="ctr2" id="e48">n/a</td><td class="ctr1" id="f48">1</td><td class="ctr2" id="g48">1</td><td class="ctr1" id="h37">4</td><td class="ctr2" id="i37">4</td><td class="ctr1" id="j39">1</td><td class="ctr2" id="k39">1</td></tr><tr><td id="a13"><a href="TsCacheDataForProtoBuf.java.html#L3412" class="el_method">clearDataId()</a></td><td class="bar" id="b40"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="14" alt="14"/></td><td class="ctr2" id="c40">0%</td><td class="bar" id="d49"/><td class="ctr2" id="e49">n/a</td><td class="ctr1" id="f49">1</td><td class="ctr2" id="g49">1</td><td class="ctr1" id="h38">4</td><td class="ctr2" id="i38">4</td><td class="ctr1" id="j40">1</td><td class="ctr2" id="k40">1</td></tr><tr><td id="a14"><a href="TsCacheDataForProtoBuf.java.html#L3492" class="el_method">clearErrorCode()</a></td><td class="bar" id="b41"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="14" alt="14"/></td><td class="ctr2" id="c41">0%</td><td class="bar" id="d50"/><td class="ctr2" id="e50">n/a</td><td class="ctr1" id="f50">1</td><td class="ctr2" id="g50">1</td><td class="ctr1" id="h39">4</td><td class="ctr2" id="i39">4</td><td class="ctr1" id="j41">1</td><td class="ctr2" id="k41">1</td></tr><tr><td id="a8"><a href="TsCacheDataForProtoBuf.java.html#L2732" class="el_method">build()</a></td><td class="bar" id="b42"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="11" alt="11"/></td><td class="ctr2" id="c42">0%</td><td class="bar" id="d36"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e36">0%</td><td class="ctr1" id="f36">2</td><td class="ctr2" id="g36">2</td><td class="ctr1" id="h40">4</td><td class="ctr2" id="i40">4</td><td class="ctr1" id="j42">1</td><td class="ctr2" id="k42">1</td></tr><tr><td id="a38"><a href="TsCacheDataForProtoBuf.java.html#L3127" class="el_method">getValuesList()</a></td><td class="bar" id="b43"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="11" alt="11"/></td><td class="ctr2" id="c43">0%</td><td class="bar" id="d37"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e37">0%</td><td class="ctr1" id="f37">2</td><td class="ctr2" id="g37">2</td><td class="ctr1" id="h43">3</td><td class="ctr2" id="i43">3</td><td class="ctr1" id="j43">1</td><td class="ctr2" id="k43">1</td></tr><tr><td id="a36"><a href="TsCacheDataForProtoBuf.java.html#L3137" class="el_method">getValuesCount()</a></td><td class="bar" id="b44"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="11" alt="11"/></td><td class="ctr2" id="c44">0%</td><td class="bar" id="d38"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e38">0%</td><td class="ctr1" id="f38">2</td><td class="ctr2" id="g38">2</td><td class="ctr1" id="h44">3</td><td class="ctr2" id="i44">3</td><td class="ctr1" id="j44">1</td><td class="ctr2" id="k44">1</td></tr><tr><td id="a40"><a href="TsCacheDataForProtoBuf.java.html#L3308" class="el_method">getValuesOrBuilderList()</a></td><td class="bar" id="b45"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="11" alt="11"/></td><td class="ctr2" id="c45">0%</td><td class="bar" id="d39"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e39">0%</td><td class="ctr1" id="f39">2</td><td class="ctr2" id="g39">2</td><td class="ctr1" id="h45">3</td><td class="ctr2" id="i45">3</td><td class="ctr1" id="j45">1</td><td class="ctr2" id="k45">1</td></tr><tr><td id="a43"><a href="TsCacheDataForProtoBuf.java.html#L2957" class="el_method">hasSecId()</a></td><td class="bar" id="b46"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="9" alt="9"/></td><td class="ctr2" id="c46">0%</td><td class="bar" id="d40"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e40">0%</td><td class="ctr1" id="f40">2</td><td class="ctr2" id="g40">2</td><td class="ctr1" id="h50">1</td><td class="ctr2" id="i50">1</td><td class="ctr1" id="j46">1</td><td class="ctr2" id="k46">1</td></tr><tr><td id="a44"><a href="TsCacheDataForProtoBuf.java.html#L3037" class="el_method">hasUniverse()</a></td><td class="bar" id="b47"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="9" alt="9"/></td><td class="ctr2" id="c47">0%</td><td class="bar" id="d41"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e41">0%</td><td class="ctr1" id="f41">2</td><td class="ctr2" id="g41">2</td><td class="ctr1" id="h51">1</td><td class="ctr2" id="i51">1</td><td class="ctr1" id="j47">1</td><td class="ctr2" id="k47">1</td></tr><tr><td id="a41"><a href="TsCacheDataForProtoBuf.java.html#L3357" class="el_method">hasDataId()</a></td><td class="bar" id="b48"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="9" alt="9"/></td><td class="ctr2" id="c48">0%</td><td class="bar" id="d42"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e42">0%</td><td class="ctr1" id="f42">2</td><td class="ctr2" id="g42">2</td><td class="ctr1" id="h52">1</td><td class="ctr2" id="i52">1</td><td class="ctr1" id="j48">1</td><td class="ctr2" id="k48">1</td></tr><tr><td id="a42"><a href="TsCacheDataForProtoBuf.java.html#L3437" class="el_method">hasErrorCode()</a></td><td class="bar" id="b49"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="9" alt="9"/></td><td class="ctr2" id="c49">0%</td><td class="bar" id="d43"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e43">0%</td><td class="ctr1" id="f43">2</td><td class="ctr2" id="g43">2</td><td class="ctr1" id="h53">1</td><td class="ctr2" id="i53">1</td><td class="ctr1" id="j49">1</td><td class="ctr2" id="k49">1</td></tr><tr><td id="a57"><a href="TsCacheDataForProtoBuf.java.html#L2806" class="el_method">setRepeatedField(Descriptors.FieldDescriptor, int, Object)</a></td><td class="bar" id="b50"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="7" alt="7"/></td><td class="ctr2" id="c50">0%</td><td class="bar" id="d51"/><td class="ctr2" id="e51">n/a</td><td class="ctr1" id="f51">1</td><td class="ctr2" id="g51">1</td><td class="ctr1" id="h54">1</td><td class="ctr2" id="i54">1</td><td class="ctr1" id="j50">1</td><td class="ctr2" id="k50">1</td></tr><tr><td id="a46"><a href="TsCacheDataForProtoBuf.java.html#L2879" class="el_method">isInitialized()</a></td><td class="bar" id="b51"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="7" alt="7"/></td><td class="ctr2" id="c51">0%</td><td class="bar" id="d44"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/></td><td class="ctr2" id="e44">0%</td><td class="ctr1" id="f44">2</td><td class="ctr2" id="g44">2</td><td class="ctr1" id="h46">3</td><td class="ctr2" id="i46">3</td><td class="ctr1" id="j51">1</td><td class="ctr2" id="k51">1</td></tr><tr><td id="a7"><a href="TsCacheDataForProtoBuf.java.html#L3326" class="el_method">addValuesBuilder(int)</a></td><td class="bar" id="b52"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="7" alt="7"/></td><td class="ctr2" id="c52">0%</td><td class="bar" id="d52"/><td class="ctr2" id="e52">n/a</td><td class="ctr1" id="f52">1</td><td class="ctr2" id="g52">1</td><td class="ctr1" id="h47">2</td><td class="ctr2" id="i47">2</td><td class="ctr1" id="j52">1</td><td class="ctr2" id="k52">1</td></tr><tr><td id="a56"><a href="TsCacheDataForProtoBuf.java.html#L2790" class="el_method">setField(Descriptors.FieldDescriptor, Object)</a></td><td class="bar" id="b53"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="6" alt="6"/></td><td class="ctr2" id="c53">0%</td><td class="bar" id="d53"/><td class="ctr2" id="e53">n/a</td><td class="ctr1" id="f53">1</td><td class="ctr2" id="g53">1</td><td class="ctr1" id="h55">1</td><td class="ctr2" id="i55">1</td><td class="ctr1" id="j53">1</td><td class="ctr2" id="k53">1</td></tr><tr><td id="a1"><a href="TsCacheDataForProtoBuf.java.html#L2812" class="el_method">addRepeatedField(Descriptors.FieldDescriptor, Object)</a></td><td class="bar" id="b54"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="6" alt="6"/></td><td class="ctr2" id="c54">0%</td><td class="bar" id="d54"/><td class="ctr2" id="e54">n/a</td><td class="ctr1" id="f54">1</td><td class="ctr2" id="g54">1</td><td class="ctr1" id="h56">1</td><td class="ctr2" id="i56">1</td><td class="ctr1" id="j54">1</td><td class="ctr2" id="k54">1</td></tr><tr><td id="a34"><a href="TsCacheDataForProtoBuf.java.html#L3291" class="el_method">getValuesBuilder(int)</a></td><td class="bar" id="b55"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="6" alt="6"/></td><td class="ctr2" id="c55">0%</td><td class="bar" id="d55"/><td class="ctr2" id="e55">n/a</td><td class="ctr1" id="f55">1</td><td class="ctr2" id="g55">1</td><td class="ctr1" id="h57">1</td><td class="ctr2" id="i57">1</td><td class="ctr1" id="j55">1</td><td class="ctr2" id="k55">1</td></tr><tr><td id="a6"><a href="TsCacheDataForProtoBuf.java.html#L3318" class="el_method">addValuesBuilder()</a></td><td class="bar" id="b56"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="6" alt="6"/></td><td class="ctr2" id="c56">0%</td><td class="bar" id="d56"/><td class="ctr2" id="e56">n/a</td><td class="ctr1" id="f56">1</td><td class="ctr2" id="g56">1</td><td class="ctr1" id="h48">2</td><td class="ctr2" id="i48">2</td><td class="ctr1" id="j56">1</td><td class="ctr2" id="k56">1</td></tr><tr><td id="a45"><a href="TsCacheDataForProtoBuf.java.html#L2686" class="el_method">internalGetFieldAccessorTable()</a></td><td class="bar" id="b57"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c57">0%</td><td class="bar" id="d57"/><td class="ctr2" id="e57">n/a</td><td class="ctr1" id="f57">1</td><td class="ctr2" id="g57">1</td><td class="ctr1" id="h49">2</td><td class="ctr2" id="i49">2</td><td class="ctr1" id="j57">1</td><td class="ctr2" id="k57">1</td></tr><tr><td id="a15"><a href="TsCacheDataForProtoBuf.java.html#L2795" class="el_method">clearField(Descriptors.FieldDescriptor)</a></td><td class="bar" id="b58"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c58">0%</td><td class="bar" id="d58"/><td class="ctr2" id="e58">n/a</td><td class="ctr1" id="f58">1</td><td class="ctr2" id="g58">1</td><td class="ctr1" id="h58">1</td><td class="ctr2" id="i58">1</td><td class="ctr1" id="j58">1</td><td class="ctr2" id="k58">1</td></tr><tr><td id="a16"><a href="TsCacheDataForProtoBuf.java.html#L2800" class="el_method">clearOneof(Descriptors.OneofDescriptor)</a></td><td class="bar" id="b59"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c59">0%</td><td class="bar" id="d59"/><td class="ctr2" id="e59">n/a</td><td class="ctr1" id="f59">1</td><td class="ctr2" id="g59">1</td><td class="ctr1" id="h59">1</td><td class="ctr2" id="i59">1</td><td class="ctr1" id="j59">1</td><td class="ctr2" id="k59">1</td></tr><tr><td id="a62"><a href="TsCacheDataForProtoBuf.java.html#L3513" class="el_method">setUnknownFields(UnknownFieldSet)</a></td><td class="bar" id="b60"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c60">0%</td><td class="bar" id="d60"/><td class="ctr2" id="e60">n/a</td><td class="ctr1" id="f60">1</td><td class="ctr2" id="g60">1</td><td class="ctr1" id="h60">1</td><td class="ctr2" id="i60">1</td><td class="ctr1" id="j60">1</td><td class="ctr2" id="k60">1</td></tr><tr><td id="a50"><a href="TsCacheDataForProtoBuf.java.html#L3519" class="el_method">mergeUnknownFields(UnknownFieldSet)</a></td><td class="bar" id="b61"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c61">0%</td><td class="bar" id="d61"/><td class="ctr2" id="e61">n/a</td><td class="ctr1" id="f61">1</td><td class="ctr2" id="g61">1</td><td class="ctr1" id="h61">1</td><td class="ctr2" id="i61">1</td><td class="ctr1" id="j61">1</td><td class="ctr2" id="k61">1</td></tr><tr><td id="a20"><a href="TsCacheDataForProtoBuf.java.html#L2784" class="el_method">clone()</a></td><td class="bar" id="b62"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c62">0%</td><td class="bar" id="d62"/><td class="ctr2" id="e62">n/a</td><td class="ctr1" id="f62">1</td><td class="ctr2" id="g62">1</td><td class="ctr1" id="h62">1</td><td class="ctr2" id="i62">1</td><td class="ctr1" id="j62">1</td><td class="ctr2" id="k62">1</td></tr><tr><td id="a35"><a href="TsCacheDataForProtoBuf.java.html#L3334" class="el_method">getValuesBuilderList()</a></td><td class="bar" id="b63"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c63">0%</td><td class="bar" id="d63"/><td class="ctr2" id="e63">n/a</td><td class="ctr1" id="f63">1</td><td class="ctr2" id="g63">1</td><td class="ctr1" id="h63">1</td><td class="ctr2" id="i63">1</td><td class="ctr1" id="j63">1</td><td class="ctr2" id="k63">1</td></tr><tr><td id="a25"><a href="TsCacheDataForProtoBuf.java.html#L2680" class="el_method">getDescriptor()</a></td><td class="bar" id="b64"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c64">0%</td><td class="bar" id="d64"/><td class="ctr2" id="e64">n/a</td><td class="ctr1" id="f64">1</td><td class="ctr2" id="g64">1</td><td class="ctr1" id="h64">1</td><td class="ctr2" id="i64">1</td><td class="ctr1" id="j64">1</td><td class="ctr2" id="k64">1</td></tr><tr><td id="a26"><a href="TsCacheDataForProtoBuf.java.html#L2722" class="el_method">getDescriptorForType()</a></td><td class="bar" id="b65"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c65">0%</td><td class="bar" id="d65"/><td class="ctr2" id="e65">n/a</td><td class="ctr1" id="f65">1</td><td class="ctr2" id="g65">1</td><td class="ctr1" id="h65">1</td><td class="ctr2" id="i65">1</td><td class="ctr1" id="j65">1</td><td class="ctr2" id="k65">1</td></tr><tr><td id="a24"><a href="TsCacheDataForProtoBuf.java.html#L2727" class="el_method">getDefaultInstanceForType()</a></td><td class="bar" id="b66"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c66">0%</td><td class="bar" id="d66"/><td class="ctr2" id="e66">n/a</td><td class="ctr1" id="f66">1</td><td class="ctr2" id="g66">1</td><td class="ctr1" id="h66">1</td><td class="ctr2" id="i66">1</td><td class="ctr1" id="j66">1</td><td class="ctr2" id="k66">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>