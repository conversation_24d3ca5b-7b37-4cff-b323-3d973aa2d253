<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.morningstar.martapi.grpc.TimeSeriesGrpcClientTest" time="0.006" tests="2" errors="0" skipped="2" failures="0">
  <properties/>
  <testcase name="testGetTimeSeriesDataWithMinimalRequest" classname="com.morningstar.martapi.grpc.TimeSeriesGrpcClientTest" time="0.0">
    <skipped message="Integration test - requires running server"/>
  </testcase>
  <testcase name="testGetTimeSeriesData" classname="com.morningstar.martapi.grpc.TimeSeriesGrpcClientTest" time="0.0">
    <skipped message="Integration test - requires running server"/>
  </testcase>
</testsuite>