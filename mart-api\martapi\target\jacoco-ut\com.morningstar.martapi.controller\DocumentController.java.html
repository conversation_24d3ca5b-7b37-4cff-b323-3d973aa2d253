<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DocumentController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <a href="index.source.html" class="el_package">com.morningstar.martapi.controller</a> &gt; <span class="el_source">DocumentController.java</span></div><h1>DocumentController.java</h1><pre class="source lang-java linenums">package com.morningstar.martapi.controller;

import com.morningstar.martapi.service.DocumentService;
import com.morningstar.martapi.util.InvestmentApiRequestUtil;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martcommon.entity.payload.gridview.GridviewDataPoint;
import com.morningstar.martgateway.infrastructures.log.LogAttribute;
import com.morningstar.martgateway.infrastructures.log.LogEntity;
import com.morningstar.martgateway.infrastructures.log.LogEntry;
import com.morningstar.martgateway.infrastructures.log.LogHelper;
import com.morningstar.martgateway.interfaces.model.investmentapi.Investment;
import com.morningstar.martgateway.interfaces.model.investmentapi.InvestmentApiRequest;
import java.util.ArrayList;

import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static com.morningstar.martapi.config.Constant.*;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.*;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.EXECUTE_TIME;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.REQUEST_PARAM;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.USER_ID;

@RestController
@RequestMapping(value = {&quot;/v1/documents&quot;,&quot;/investment-api/v1/documents&quot;},
        produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE})
@Validated
public class DocumentController {

    private final RequestValidationHandler&lt;HeadersAndParams,InvestmentApiRequest&gt; validator;
    private final DocumentService documentService;

    @Autowired
    public DocumentController(
<span class="nc" id="L56">            @Qualifier(&quot;investmentApiValidator&quot;) RequestValidationHandler&lt;HeadersAndParams, InvestmentApiRequest&gt; validator, DocumentService documentService) {</span>
<span class="nc" id="L57">        this.validator = validator;</span>
<span class="nc" id="L58">        this.documentService = documentService;</span>
<span class="nc" id="L59">    }</span>

    @GetMapping(value = &quot;&quot;, consumes = MediaType.APPLICATION_JSON_VALUE)
    public Mono&lt;ResponseEntity&lt;InputStreamResource&gt;&gt; getData(
            @RequestParam(value = &quot;performanceId&quot;) String performanceId,
            @RequestParam(value = &quot;documentType&quot;) String documentType,
            @RequestParam(value = &quot;documentId&quot;) String documentId,
            @RequestParam(value = &quot;readCache&quot;, required = false, defaultValue = &quot;&quot;) String readCache,
            @RequestHeader(value = &quot;X-API-UserId&quot;, required = false, defaultValue = &quot;&quot;) String headerUserId,
            @RequestHeader(value = &quot;Authorization&quot;, required = false, defaultValue = &quot;&quot;) String token,
            @RequestHeader(value = &quot;X-API-RequestId&quot;, required = false, defaultValue = &quot;&quot;) String requestId,
            @RequestHeader(value = &quot;X-Api-ProductId&quot;, required = false, defaultValue = &quot;&quot;) String productId,
            @RequestHeader(value = &quot;checkEntitlement&quot;, required = false, defaultValue = &quot;true&quot;) String checkEntitlement,
            @RequestHeader(value = &quot;X-API-SkipProxy&quot;, required = false, defaultValue = &quot;false&quot;) boolean skipProxy,
            ServerHttpRequest request) {
<span class="nc" id="L74">        long startTime = System.currentTimeMillis();</span>

<span class="nc bnc" id="L76" title="All 2 branches missed.">        if (!Objects.equals(documentType, DOCUMENT_TYPE)) {</span>
<span class="nc" id="L77">            return Mono.error(new IllegalArgumentException(&quot;Invalid documentType: Correct the documentType, example: EQ5NQ&quot;));</span>
        }

<span class="nc bnc" id="L80" title="All 2 branches missed.">        if (!isValidDocumentId(documentId)) {</span>
<span class="nc" id="L81">            return Mono.error(new IllegalArgumentException(&quot;Invalid documentId: Correct the documentId, example: 164531&quot;));</span>
        }

<span class="nc bnc" id="L84" title="All 2 branches missed.">        if (!isValidPerformanceId(performanceId)) {</span>
            // Checking format for performanceID
<span class="nc" id="L86">            return Mono.error(new IllegalArgumentException(&quot;Invalid performanceId: Correct the performanceID, example: 0P00000001&quot;));</span>
        }
<span class="nc" id="L88">        InvestmentApiRequest validatedInvestmentApiRequest = buildInvestmentApiRequest(</span>
                performanceId, documentId, token, productId, requestId, readCache, checkEntitlement, headerUserId, skipProxy
        );

<span class="nc" id="L92">        return documentService.getDocument(validatedInvestmentApiRequest, getHeadersAndParams(token, productId, requestId), startTime, documentId)</span>
<span class="nc" id="L93">                .flatMap(inputStream -&gt; Mono.just(buildResponse(inputStream, performanceId, documentType, documentId)))</span>
<span class="nc" id="L94">                .doOnEach(LogHelper.logOnNext(list -&gt; {</span>
<span class="nc" id="L95">                    MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), requestId);</span>
<span class="nc" id="L96">                    LogEntry.info(new LogEntity(EVENT_TYPE, RESPONSE),</span>
                            new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                            new LogEntity(PRODUCT_ID, productId),
<span class="nc" id="L99">                            new LogEntity(USER_ID, validatedInvestmentApiRequest.getUserId()),</span>
<span class="nc" id="L100">                            new LogEntity(REQUEST_PARAM, request.getURI()),</span>
<span class="nc" id="L101">                            new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime));</span>
<span class="nc" id="L102">                })).doOnEach(LogHelper.logOnError(e -&gt; {</span>
<span class="nc" id="L103">                    MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), requestId);</span>
<span class="nc" id="L104">                    LogEntry.error(</span>
                            new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                            new LogEntity(EVENT_DESCRIPTION, e),
<span class="nc" id="L107">                            new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime),</span>
<span class="nc" id="L108">                            new LogEntity(EXCEPTION_TYPE, e.getClass()),</span>
                            new LogEntity(PRODUCT_ID, productId),
<span class="nc" id="L110">                            new LogEntity(USER_ID, validatedInvestmentApiRequest.getUserId()),</span>
<span class="nc" id="L111">                            new LogEntity(REQUEST_PARAM, request.getURI())</span>
                    );
<span class="nc" id="L113">                }));</span>
    }

    private InvestmentApiRequest buildInvestmentApiRequest(
            String performanceId, String documentId, String token, String productId, String requestId,
            String readCache, String checkEntitlement, String headerUserId, boolean skipProxy) {

<span class="nc" id="L120">        Investment investment = new Investment(performanceId);</span>
<span class="nc" id="L121">        InvestmentApiRequest investmentApiRequest = new InvestmentApiRequest();</span>
<span class="nc" id="L122">        investmentApiRequest.setInvestments(new ArrayList&lt;&gt;(List.of(investment))); // should use mutable List</span>

<span class="nc" id="L124">        GridviewDataPoint gridviewDataPoint = new GridviewDataPoint();</span>
<span class="nc" id="L125">        gridviewDataPoint.setDataPointIds(new ArrayList&lt;&gt;(List.of(COMPANY_ID_DATAPOINT, JOB_ID_DATAPOINT, EVENT_DATETIME_DATAPOINT))); // should use mutable List</span>
<span class="nc" id="L126">        gridviewDataPoint.setEventId(Set.of(documentId));</span>

<span class="nc" id="L128">        investmentApiRequest.setDataPoints(new ArrayList&lt;&gt;(List.of(gridviewDataPoint))); // should use mutable List</span>

<span class="nc" id="L130">        HeadersAndParams headersAndParams = getHeadersAndParams(token, productId, requestId);</span>
<span class="nc" id="L131">        validateRequest(investmentApiRequest, headersAndParams);</span>

<span class="nc" id="L133">        return new InvestmentApiRequestUtil().getValidatedInvestmentApiRequest(</span>
                investmentApiRequest, token, headerUserId, productId, requestId, readCache, checkEntitlement
        );
    }

    private HeadersAndParams getHeadersAndParams(String token, String productId, String requestId) {
<span class="nc" id="L139">        return new InvestmentApiRequestUtil().getHeaderAndParams(token, productId, requestId);</span>
    }

    private ResponseEntity&lt;InputStreamResource&gt; buildResponse(InputStream inputStream, String performanceId, String documentType, String documentId) {
<span class="nc" id="L143">        String filename = String.format(&quot;%s_%s_%s.json&quot;, performanceId, documentType, documentId);</span>
<span class="nc" id="L144">        HttpHeaders headers = new HttpHeaders();</span>
<span class="nc" id="L145">        headers.add(HttpHeaders.CONTENT_DISPOSITION, &quot;attachment; filename=&quot; + filename);</span>
<span class="nc" id="L146">        headers.add(HttpHeaders.CONTENT_TYPE, &quot;application/json&quot;);</span>

        try {
<span class="nc" id="L149">            headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(inputStream.available()));</span>
<span class="nc" id="L150">        } catch (IOException e) {</span>
<span class="nc" id="L151">            throw new RuntimeException(&quot;Error calculating file size&quot;, e);</span>
<span class="nc" id="L152">        }</span>

<span class="nc" id="L154">        return new ResponseEntity&lt;&gt;(new InputStreamResource(inputStream), headers, HttpStatus.OK);</span>
    }

    private void validateRequest(InvestmentApiRequest investmentApiRequest, HeadersAndParams headersAndParams) {
<span class="nc" id="L158">        validator.validateHeadersAndParams(headersAndParams);</span>
<span class="nc" id="L159">        validator.validateRequestBody(investmentApiRequest);</span>
<span class="nc" id="L160">    }</span>

    private boolean isValidDocumentId(String documentId) {
<span class="nc bnc" id="L163" title="All 4 branches missed.">        return documentId != null &amp;&amp; !documentId.isEmpty();</span>
    }

    private boolean isValidPerformanceId(String performanceId) {
<span class="nc bnc" id="L167" title="All 4 branches missed.">        return performanceId != null &amp;&amp; performanceId.length() == 10;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>