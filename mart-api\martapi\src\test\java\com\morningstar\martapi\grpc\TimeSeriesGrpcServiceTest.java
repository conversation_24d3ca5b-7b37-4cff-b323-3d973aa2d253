package com.morningstar.martapi.grpc;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * gRPC服务集成测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class TimeSeriesGrpcServiceTest {

    @Test
    void contextLoads() {
        // 这个测试验证Spring上下文可以正常加载，包括gRPC服务
        // 如果gRPC配置有问题，这个测试会失败
    }
}
