GRO<PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,INSTRUCTION_MISSED,INSTRUCTION_COVERED,<PERSON><PERSON>CH_MISSED,<PERSON><PERSON>CH_COVERED,LINE_MISSED,LINE_COVERED,COMPLEXITY_MISSED,COMPLEXITY_COVERED,METHOD_MISSED,METHOD_COVERED
martapi,com.morningstar.martapi,MartAPIApplication,8,0,0,0,3,0,2,0,2,0
martapi,com.morningstar.martapi.repo,Dynamo<PERSON><PERSON>,32,0,0,0,13,0,4,0,4,0
martapi,com.morningstar.martapi.validator.licenseapi,LicenseAuditEntityValidator,70,0,8,0,18,0,8,0,4,0
martapi,com.morningstar.martapi.validator.licenseapi,LicenseCellEntityValidator,59,0,8,0,19,0,7,0,3,0
martapi,com.morningstar.martapi.exception,TsGridViewValidationException,4,0,0,0,2,0,1,0,1,0
martapi,com.morningstar.martapi.exception,GridViewEx<PERSON>Hand<PERSON>,233,0,0,0,49,0,12,0,12,0
martapi,com.morningstar.martapi.exception,LicenseExceptionHandler,124,0,0,0,26,0,6,0,6,0
martapi,com.morningstar.martapi.exception,TsCacheApiValidationException,4,0,0,0,2,0,1,0,1,0
martapi,com.morningstar.martapi.exception,TranscriptApiException,11,0,0,0,4,0,1,0,1,0
martapi,com.morningstar.martapi.exception,HoldingValidationException,8,0,0,0,3,0,1,0,1,0
martapi,com.morningstar.martapi.exception,ValidationException,8,0,0,0,3,0,1,0,1,0
martapi,com.morningstar.martapi.exception,HoldingExceptionHandler,386,0,22,0,80,0,26,0,15,0
martapi,com.morningstar.martapi.exception,JsonDownloadException,9,0,0,0,4,0,2,0,2,0
martapi,com.morningstar.martapi.exception,TsExceptionHandler,248,0,8,0,51,0,12,0,8,0
martapi,com.morningstar.martapi.exception,IndexAPIException,4,0,0,0,2,0,1,0,1,0
martapi,com.morningstar.martapi.exception,TsCacheProtobufValidationException,4,0,0,0,2,0,1,0,1,0
martapi,com.morningstar.martapi.exception,IllegalGzipRequestException,4,0,0,0,2,0,1,0,1,0
martapi,com.morningstar.martapi.exception,InvestmentApiValidationException,4,0,0,0,2,0,1,0,1,0
martapi,com.morningstar.martapi.exception,WebExceptionHandler,232,0,12,0,42,0,14,0,8,0
martapi,com.morningstar.martapi.validator,RequestValidationHandler,55,0,0,0,16,0,7,0,7,0
martapi,com.morningstar.martapi.validator,RequestIdValidator,26,0,4,0,8,0,5,0,3,0
martapi,com.morningstar.martapi.validator,ProductIdValidator,47,0,8,0,12,0,6,0,2,0
martapi,com.morningstar.martapi.validator,AuthTokenValidator,60,0,8,0,16,0,7,0,3,0
martapi,com.morningstar.martapi.grpc,TimeSeriesServiceGrpc.TimeSeriesServiceBlockingStub,20,0,0,0,5,0,3,0,3,0
martapi,com.morningstar.martapi.grpc,TimeSeriesGrpcService,413,0,4,0,98,0,15,0,13,0
martapi,com.morningstar.martapi.grpc,TimeSeriesServiceGrpc.TimeSeriesServiceImplBase,6,0,0,0,2,0,2,0,2,0
martapi,com.morningstar.martapi.grpc,TimeSeriesServiceGrpc.new AbstractStub.StubFactory() {...},9,0,0,0,2,0,2,0,2,0
martapi,com.morningstar.martapi.grpc,TimeSeriesServiceGrpc.new AbstractStub.StubFactory() {...},9,0,0,0,2,0,2,0,2,0
martapi,com.morningstar.martapi.grpc,TimeSeriesServiceGrpc.new AbstractStub.StubFactory() {...},9,0,0,0,2,0,2,0,2,0
martapi,com.morningstar.martapi.grpc,TimeSeriesRequest.Builder,2913,0,269,0,857,0,276,0,131,0
martapi,com.morningstar.martapi.grpc,TimeSeriesServiceGrpc.MethodHandlers,31,0,2,0,11,0,4,0,3,0
martapi,com.morningstar.martapi.grpc,TimeSeriesRequest,1997,0,210,0,527,0,180,0,75,0
martapi,com.morningstar.martapi.grpc,TimeSeriesServiceGrpc.TimeSeriesServiceMethodDescriptorSupplier,12,0,0,0,4,0,2,0,2,0
martapi,com.morningstar.martapi.grpc,TimeSeriesServiceGrpc.TimeSeriesServiceBaseDescriptorSupplier,10,0,0,0,3,0,3,0,3,0
martapi,com.morningstar.martapi.grpc,TimeSeriesServiceGrpc.TimeSeriesServiceFutureStub,20,0,0,0,5,0,3,0,3,0
martapi,com.morningstar.martapi.grpc,TimeSeriesServiceGrpc.TimeSeriesServiceStub,21,0,0,0,6,0,3,0,3,0
martapi,com.morningstar.martapi.grpc,TimeSeriesServiceProto,122,0,0,0,12,0,4,0,4,0
martapi,com.morningstar.martapi.grpc,TimeSeriesRequest.new AbstractParser() {...},36,0,0,0,12,0,2,0,2,0
martapi,com.morningstar.martapi.grpc,TimeSeriesServiceGrpc.TimeSeriesServiceFileDescriptorSupplier,3,0,0,0,1,0,1,0,1,0
martapi,com.morningstar.martapi.grpc,TimeSeriesServiceGrpc.AsyncService,4,0,0,0,2,0,1,0,1,0
martapi,com.morningstar.martapi.service,S3PreSignerProvider,23,0,0,0,7,0,3,0,3,0
martapi,com.morningstar.martapi.service,LambdaService,124,0,2,0,28,0,7,0,6,0
martapi,com.morningstar.martapi.service,DocumentService,609,0,36,0,124,0,45,0,27,0
martapi,com.morningstar.martapi.service,RedisService,86,0,0,0,8,0,5,0,5,0
martapi,com.morningstar.martapi.service,AsyncApiService,951,0,72,0,227,0,52,0,16,0
martapi,com.morningstar.martapi.service,RedisMessageListener,153,0,12,0,42,0,15,0,9,0
martapi,com.morningstar.martapi.service,RedisMessagePublisher,39,0,0,0,14,0,4,0,4,0
martapi,com.morningstar.martapi.service,S3Service,113,0,6,0,32,0,8,0,5,0
martapi,com.morningstar.martapi.service,LicenseAuditService,93,0,8,0,16,0,8,0,4,0
martapi,com.morningstar.martapi.validator.portfolioholdings,HoldingsDataPointsRequestFieldsValidator,204,0,38,0,52,0,29,0,10,0
martapi,com.morningstar.martapi.validator.portfolioholdings,IdTypeValidator,14,0,2,0,5,0,3,0,2,0
martapi,com.morningstar.martapi.validator.portfolioholdings,PortfolioSettingsValidator,417,0,84,0,98,0,57,0,14,0
martapi,com.morningstar.martapi.validator.portfolioholdings,HoldingDateTypeValidator,21,0,2,0,7,0,3,0,2,0
martapi,com.morningstar.martapi.validator.portfolioholdings,StaticDateCountValidator,30,0,4,0,8,0,4,0,2,0
martapi,com.morningstar.martapi.validator.portfolioholdings,InvestmentValidator,43,0,6,0,11,0,6,0,3,0
martapi,com.morningstar.martapi.validator.portfolioholdings,InvestmentCountValidator,16,0,2,0,4,0,3,0,2,0
martapi,com.morningstar.martapi.validator.portfolioholdings,HoldingsDataPointsCountValidator,19,0,2,0,6,0,3,0,2,0
martapi,com.morningstar.martapi.util,LoggerUtil,732,0,8,0,72,0,16,0,12,0
martapi,com.morningstar.martapi.util,AsyncGetStatusUserIdUtil,41,0,6,0,10,0,5,0,2,0
martapi,com.morningstar.martapi.util,CompressionUtils,38,0,6,0,9,0,7,0,4,0
martapi,com.morningstar.martapi.util,DateParamInputUtil,22,0,2,0,8,0,2,0,1,0
martapi,com.morningstar.martapi.util,TokenUtil,8,0,0,0,2,0,2,0,2,0
martapi,com.morningstar.martapi.util,InvestmentApiRequestUtil,85,0,2,0,19,0,6,0,5,0
martapi,com.morningstar.martapi.util,BatchSizeUtil,35,0,6,0,7,0,4,0,1,0
martapi,com.morningstar.martapi.validator.entity,DataPointConfiguration,196,0,30,0,19,0,18,0,3,0
martapi,com.morningstar.martapi.validator.tsrequest,TsMartRequestDataPointValidator,45,0,6,0,13,0,5,0,2,0
martapi,com.morningstar.martapi.validator.tsrequest,TsMartRequestDateValidator,76,0,18,0,15,0,15,0,6,0
martapi,com.morningstar.martapi.validator.tsrequest,TsMartRequestInvestmentValidator,57,0,8,0,17,0,6,0,2,0
martapi,com.morningstar.martapi.config,AsyncApiConfig,57,0,0,0,15,0,5,0,5,0
martapi,com.morningstar.martapi.config,Constant,13,0,0,0,3,0,2,0,2,0
martapi,com.morningstar.martapi.config,NettyWebServerFactoryHeaderSizeCustomizer,21,0,0,0,4,0,4,0,4,0
martapi,com.morningstar.martapi.config,WebFluxConfig,25,0,0,0,9,0,2,0,2,0
martapi,com.morningstar.martapi.config,RedisMessageConfig,91,0,0,0,17,0,3,0,3,0
martapi,com.morningstar.martapi.config,ValidatorsConfig,337,0,0,0,32,0,12,0,12,0
martapi,com.morningstar.martapi.config,SwaggerConfig,42,0,0,0,9,0,2,0,2,0
martapi,com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf,TsCacheDataForProtoBuf.TimeSeriesData,791,0,106,0,202,0,102,0,49,0
martapi,com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf,TsCacheDataForProtoBuf.TSValuePair.new AbstractParser() {...},36,0,0,0,12,0,2,0,2,0
martapi,com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf,TsCacheDataForProtoBuf.TimeSeriesDatas,528,0,62,0,132,0,73,0,42,0
martapi,com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf,TsCacheDataForProtoBuf.TimeSeriesData.new AbstractParser() {...},36,0,0,0,12,0,2,0,2,0
martapi,com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf,TsCacheDataForProtoBuf.TimeSeriesData.Builder,1267,0,147,0,357,0,143,0,67,0
martapi,com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf,TsCacheDataForProtoBuf.TimeSeriesDatas.Builder,902,0,105,0,251,0,107,0,53,0
martapi,com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf,TsCacheDataForProtoBuf.TimeSeriesDatas.new AbstractParser() {...},36,0,0,0,12,0,2,0,2,0
martapi,com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf,TsCacheDataForProtoBuf.TSValuePair,506,0,38,0,123,0,61,0,42,0
martapi,com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf,TsCacheDataForProtoBuf.TSValuePair.Builder,745,0,59,0,216,0,81,0,49,0
martapi,com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf,TsCacheDataForProtoBuf,102,0,0,0,14,0,4,0,4,0
martapi,com.morningstar.martapi.validator.investmentapi,ColumnLimitValidator,302,0,40,0,65,0,29,0,9,0
martapi,com.morningstar.martapi.validator.investmentapi,DynamicDatapointValidator,172,0,32,0,39,0,23,0,6,0
martapi,com.morningstar.martapi.validator.investmentapi,UseCaseValidator,23,0,4,0,6,0,4,0,2,0
martapi,com.morningstar.martapi.validator.investmentapi,DataPointValidator,196,0,32,0,46,0,23,0,7,0
martapi,com.morningstar.martapi.validator.investmentapi,InvestmentValidator,58,0,8,0,17,0,6,0,2,0
martapi,com.morningstar.martapi.validator.investmentapi,DateValidator,107,0,26,0,19,0,19,0,6,0
martapi,com.morningstar.martapi.validator.investmentapi,IdTypeValidator,25,0,4,0,6,0,5,0,3,0
martapi,com.morningstar.martapi.validator.delta,DeltaStartTimeValidator,49,0,12,0,12,0,10,0,4,0
martapi,com.morningstar.martapi.web,GzipServerHttpRequest,94,0,0,0,24,0,14,0,14,0
martapi,com.morningstar.martapi.validator.clearcache,DataPointValidator,34,0,6,0,8,0,5,0,2,0
martapi,com.morningstar.martapi.validator.clearcache,InvestmentValidator,34,0,6,0,8,0,5,0,2,0
martapi,com.morningstar.martapi.controller,DeltaController,35,0,0,0,10,0,2,0,2,0
martapi,com.morningstar.martapi.controller,DataPointUpdateController,21,0,0,0,7,0,2,0,2,0
martapi,com.morningstar.martapi.controller,IndexDataController,101,0,0,0,17,0,5,0,5,0
martapi,com.morningstar.martapi.controller,HealthCheckController,56,0,2,0,8,0,6,0,5,0
martapi,com.morningstar.martapi.controller,ClearCacheController,110,0,6,0,33,0,8,0,5,0
martapi,com.morningstar.martapi.controller,PortfolioHoldingsController,343,0,4,0,82,0,19,0,17,0
martapi,com.morningstar.martapi.controller,InvestmentController,58,0,0,0,15,0,3,0,3,0
martapi,com.morningstar.martapi.controller,AsyncApiController,144,0,4,0,42,0,10,0,8,0
martapi,com.morningstar.martapi.controller,SecurityController,498,0,4,0,86,0,23,0,21,0
martapi,com.morningstar.martapi.controller,DocumentController,378,0,14,0,53,0,18,0,11,0
martapi,com.morningstar.martapi.controller,DataPointController,56,0,0,0,14,0,4,0,4,0
martapi,com.morningstar.martapi.controller,TimeSeriesController,999,0,14,0,175,0,24,0,17,0
martapi,com.morningstar.martapi.controller,HoldingDataController,209,0,0,0,28,0,4,0,4,0
martapi,com.morningstar.martapi.controller,SecurityStatusController,29,0,0,0,7,0,3,0,3,0
martapi,com.morningstar.martapi.controller,LicenseAuditController,262,0,6,0,64,0,10,0,7,0
martapi,com.morningstar.martapi.exception.utils,ExceptionHandlerUtils,38,0,4,0,9,0,5,0,3,0
martapi,com.morningstar.martapi.filter,GzipDecompressionFilter,45,0,2,0,15,0,6,0,5,0
martapi,com.morningstar.martapi.filter,WebHeaderFilter,160,0,12,0,39,0,17,0,11,0
