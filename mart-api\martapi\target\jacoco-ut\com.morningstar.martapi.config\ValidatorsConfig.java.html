<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ValidatorsConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <a href="index.source.html" class="el_package">com.morningstar.martapi.config</a> &gt; <span class="el_source">ValidatorsConfig.java</span></div><h1>ValidatorsConfig.java</h1><pre class="source lang-java linenums">package com.morningstar.martapi.config;

import com.morningstar.martapi.entity.LicenseAuditEntity;
import com.morningstar.martapi.entity.LicenseCellEntity;
import com.morningstar.martapi.exception.HoldingValidationException;
import com.morningstar.martapi.exception.InvestmentApiValidationException;
import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.validator.AuthTokenValidator;
import com.morningstar.martapi.validator.ProductIdValidator;
import com.morningstar.martapi.validator.RequestIdValidator;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.Validator;
import com.morningstar.martapi.validator.delta.DeltaStartTimeValidator;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martapi.validator.investmentapi.ColumnLimitValidator;
import com.morningstar.martapi.validator.investmentapi.DataPointValidator;
import com.morningstar.martapi.validator.investmentapi.DateValidator;
import com.morningstar.martapi.validator.investmentapi.IdTypeValidator;
import com.morningstar.martapi.validator.investmentapi.InvestmentValidator;
import com.morningstar.martapi.validator.investmentapi.DynamicDatapointValidator;
import com.morningstar.martapi.validator.investmentapi.UseCaseValidator;
import com.morningstar.martapi.validator.portfolioholdings.HoldingDateTypeValidator;
import com.morningstar.martapi.validator.portfolioholdings.HoldingsDataPointsRequestFieldsValidator;
import com.morningstar.martapi.validator.portfolioholdings.HoldingsDataPointsCountValidator;
import com.morningstar.martapi.validator.portfolioholdings.InvestmentCountValidator;
import com.morningstar.martapi.validator.portfolioholdings.StaticDateCountValidator;
import com.morningstar.martapi.validator.portfolioholdings.PortfolioSettingsValidator;
import com.morningstar.martapi.validator.tsrequest.TsMartRequestDataPointValidator;
import com.morningstar.martapi.validator.tsrequest.TsMartRequestDateValidator;
import com.morningstar.martapi.validator.tsrequest.TsMartRequestInvestmentValidator;
import com.morningstar.martcommon.entity.ClearCacheRequest;
import com.morningstar.martcommon.entity.result.request.MartRequest;
import com.morningstar.martgateway.domains.entitlement.service.DataEntitlementService;
import com.morningstar.martgateway.infrastructures.config.ProductIdsRegistry;
import com.morningstar.martgateway.interfaces.model.holdingdata.HoldingDataRequest;
import com.morningstar.martgateway.interfaces.model.investmentapi.InvestmentApiRequest;
import com.morningstar.martgateway.util.EquityDatapointUtil;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Collections;
import java.util.List;

@Configuration
<span class="nc" id="L46">public class ValidatorsConfig {</span>

    @Bean(name = &quot;investmentApiValidator&quot;)
    public RequestValidationHandler&lt;HeadersAndParams,InvestmentApiRequest&gt; investmentApiValidator(
            @Qualifier(&quot;productIdsRegistry&quot;) ProductIdsRegistry productIdsRegistry
    ) {
<span class="nc" id="L52">        List&lt;Validator&lt;HeadersAndParams&gt;&gt; headerAndParamValidators = List.of(</span>
                new AuthTokenValidator(),
                new ProductIdValidator(productIdsRegistry),
                new RequestIdValidator()
        );
<span class="nc" id="L57">        List&lt;Validator&lt;InvestmentApiRequest&gt;&gt; requestBodyValidators = List.of(</span>
                new UseCaseValidator&lt;&gt;(InvestmentApiValidationException::new),
                new DataPointValidator(),
                new InvestmentValidator(),
                new DateValidator(),
                new ColumnLimitValidator(1000, new EquityDatapointUtil()),
                new IdTypeValidator(),
                new DeltaStartTimeValidator(),
                new DynamicDatapointValidator()
        );
<span class="nc" id="L67">        return new RequestValidationHandler&lt;&gt;(headerAndParamValidators, requestBodyValidators);</span>
    }

    @Bean(name = &quot;deltaDetectionApiValidator&quot;)
    public RequestValidationHandler&lt;HeadersAndParams,InvestmentApiRequest&gt; deltaDetectionApiValidator(
            @Qualifier(&quot;productIdsRegistry&quot;) ProductIdsRegistry productIdsRegistry
    ) {
<span class="nc" id="L74">        List&lt;Validator&lt;HeadersAndParams&gt;&gt; headerAndParamValidators = List.of(</span>
                new AuthTokenValidator(),
                new ProductIdValidator(productIdsRegistry),
                new RequestIdValidator()
        );
<span class="nc" id="L79">        List&lt;Validator&lt;InvestmentApiRequest&gt;&gt; requestBodyValidators = List.of(</span>
                new UseCaseValidator&lt;&gt;(InvestmentApiValidationException::new),
                new DataPointValidator(),
                new InvestmentValidator(),
                new DateValidator(),
                new DeltaStartTimeValidator()
        );
<span class="nc" id="L86">        return new RequestValidationHandler&lt;&gt;(headerAndParamValidators, requestBodyValidators);</span>
    }

    @Bean(name = &quot;asyncApiValidator&quot;)
    public RequestValidationHandler&lt;HeadersAndParams, InvestmentApiRequest&gt; asyncDataApiValidator(
            @Qualifier(&quot;productIdsRegistry&quot;) ProductIdsRegistry productIdsRegistry,
            @Qualifier(&quot;dataEntitlementService&quot;)DataEntitlementService&lt;InvestmentApiRequest&gt; dataEntitlementService
            ) {
<span class="nc" id="L94">        List&lt;Validator&lt;HeadersAndParams&gt;&gt; headerAndParamValidators = List.of(</span>
                new AuthTokenValidator(),
                new ProductIdValidator(productIdsRegistry),
                new RequestIdValidator()
        );
<span class="nc" id="L99">        List&lt;Validator&lt;InvestmentApiRequest&gt;&gt; requestBodyValidators = List.of(</span>
                new UseCaseValidator&lt;&gt;(InvestmentApiValidationException::new),
                new DataPointValidator(),
                new InvestmentValidator(),
                new DateValidator(),
                new ColumnLimitValidator(new EquityDatapointUtil()),
                new IdTypeValidator(),
                new DeltaStartTimeValidator()
        );
<span class="nc" id="L108">        return new RequestValidationHandler&lt;&gt;(headerAndParamValidators, requestBodyValidators, dataEntitlementService);</span>
    }

    @Bean(name = &quot;timeSeriesApiValidator&quot;)
    public RequestValidationHandler&lt;HeadersAndParams, MartRequest&gt; timeSeriesApiValidator(
            @Qualifier(&quot;productIdsRegistry&quot;) ProductIdsRegistry productIdsRegistry
    ) {
<span class="nc" id="L115">        List&lt;Validator&lt;HeadersAndParams&gt;&gt; headerAndParamValidators = List.of(</span>
                new AuthTokenValidator(),
                new ProductIdValidator(productIdsRegistry),
                new RequestIdValidator()
        );
<span class="nc" id="L120">        List&lt;Validator&lt;MartRequest&gt;&gt; requestBodyValidators = List.of(</span>
                new UseCaseValidator&lt;&gt;(ValidationException::new),
                new TsMartRequestDataPointValidator(),
                new TsMartRequestInvestmentValidator(),
                new TsMartRequestDateValidator()
        );
<span class="nc" id="L126">        return new RequestValidationHandler&lt;&gt;(headerAndParamValidators, requestBodyValidators);</span>
    }

    @Bean(name = &quot;holdingDateValidator&quot;)
    public RequestValidationHandler&lt;HeadersAndParams, HoldingDataRequest&gt; holdingDateValidator(
            @Qualifier(&quot;productIdsRegistry&quot;) ProductIdsRegistry productIdsRegistry
    ) {
<span class="nc" id="L133">        List&lt;Validator&lt;HeadersAndParams&gt;&gt; headerAndParamValidators = getPortfolioHoldingsHeaderAndParamValidators(productIdsRegistry);</span>
<span class="nc" id="L134">        List&lt;Validator&lt;HoldingDataRequest&gt;&gt; requestBodyValidators = List.of(</span>
                new PortfolioSettingsValidator(),
                new StaticDateCountValidator(20),
                new com.morningstar.martapi.validator.portfolioholdings.InvestmentValidator(),
                new com.morningstar.martapi.validator.portfolioholdings.IdTypeValidator()
        );
<span class="nc" id="L140">        return new RequestValidationHandler&lt;&gt;(headerAndParamValidators, requestBodyValidators);</span>
    }

    @Bean(name = &quot;holdingDataValidatorSync&quot;)
    public RequestValidationHandler&lt;HeadersAndParams, HoldingDataRequest&gt; holdingDataSyncValidator(
            @Qualifier(&quot;productIdsRegistry&quot;) ProductIdsRegistry productIdsRegistry
    ) {
<span class="nc" id="L147">        List&lt;Validator&lt;HeadersAndParams&gt;&gt; headerAndParamValidators = getPortfolioHoldingsHeaderAndParamValidators(productIdsRegistry);</span>
<span class="nc" id="L148">        List&lt;Validator&lt;HoldingDataRequest&gt;&gt; requestBodyValidators = List.of(</span>
                new UseCaseValidator&lt;&gt;(HoldingValidationException::new),
                new PortfolioSettingsValidator(),
                new HoldingDateTypeValidator(),
                new StaticDateCountValidator(1),       // for sync
                new com.morningstar.martapi.validator.portfolioholdings.InvestmentValidator(),
                new InvestmentCountValidator(),         // for sync
                new com.morningstar.martapi.validator.portfolioholdings.IdTypeValidator(),
                new HoldingsDataPointsRequestFieldsValidator(),
                new HoldingsDataPointsCountValidator()   // for sync
        );
<span class="nc" id="L159">        return new RequestValidationHandler&lt;&gt;(headerAndParamValidators, requestBodyValidators);</span>
    }

    @Bean(name = &quot;holdingDataValidatorAsync&quot;)
    public RequestValidationHandler&lt;HeadersAndParams, HoldingDataRequest&gt; holdingDataASyncValidator(
            @Qualifier(&quot;productIdsRegistry&quot;) ProductIdsRegistry productIdsRegistry,
            @Qualifier(&quot;dataEntitlementService&quot;)DataEntitlementService&lt;HoldingDataRequest&gt; dataEntitlementService
    ) {
<span class="nc" id="L167">        List&lt;Validator&lt;HeadersAndParams&gt;&gt; headerAndParamValidators = getPortfolioHoldingsHeaderAndParamValidators(productIdsRegistry);</span>
<span class="nc" id="L168">        List&lt;Validator&lt;HoldingDataRequest&gt;&gt; requestBodyValidators = List.of(</span>
                new UseCaseValidator&lt;&gt;(HoldingValidationException::new),
                new PortfolioSettingsValidator(),
                new StaticDateCountValidator(20),
                new com.morningstar.martapi.validator.portfolioholdings.InvestmentValidator(),
                new com.morningstar.martapi.validator.portfolioholdings.IdTypeValidator(),
                new HoldingsDataPointsRequestFieldsValidator()
        );
<span class="nc" id="L176">        return new RequestValidationHandler&lt;&gt;(headerAndParamValidators, requestBodyValidators, dataEntitlementService);</span>
    }

    private static List&lt;Validator&lt;HeadersAndParams&gt;&gt; getPortfolioHoldingsHeaderAndParamValidators(ProductIdsRegistry productIdsRegistry) {
<span class="nc" id="L180">        return List.of(</span>
                new AuthTokenValidator(),
                new ProductIdValidator(productIdsRegistry),
                new RequestIdValidator()
        );
    }

    @Bean(name = &quot;clearCacheValidator&quot;)
    public RequestValidationHandler&lt;HeadersAndParams, ClearCacheRequest&gt; clearCacheValidator() {
<span class="nc" id="L189">        List&lt;Validator&lt;HeadersAndParams&gt;&gt; headerAndParamValidators = List.of(new AuthTokenValidator());</span>
<span class="nc" id="L190">        List&lt;Validator&lt;ClearCacheRequest&gt;&gt; requestBodyValidators = List.of(</span>
                new com.morningstar.martapi.validator.clearcache.InvestmentValidator(),
                new com.morningstar.martapi.validator.clearcache.DataPointValidator());
<span class="nc" id="L193">        return new RequestValidationHandler&lt;&gt;(headerAndParamValidators, requestBodyValidators);</span>
    }

    @Bean(name = &quot;licenseAuditValidator&quot;)
    public RequestValidationHandler&lt;HeadersAndParams, LicenseAuditEntity&gt; licenseAuditValidator(@Qualifier(&quot;productIdsRegistry&quot;) ProductIdsRegistry productIdsRegistry) {
<span class="nc" id="L198">        List&lt;Validator&lt;HeadersAndParams&gt;&gt; headerAndParamValidators = List.of(new AuthTokenValidator(), new ProductIdValidator(productIdsRegistry));</span>
<span class="nc" id="L199">        List&lt;Validator&lt;LicenseAuditEntity&gt;&gt; requestBodyValidators = List.of(</span>
                new com.morningstar.martapi.validator.licenseapi.LicenseAuditEntityValidator());
<span class="nc" id="L201">        return new RequestValidationHandler&lt;&gt;(headerAndParamValidators, requestBodyValidators);</span>
    }

    @Bean(name = &quot;licenseCellValidator&quot;)
    public RequestValidationHandler&lt;HeadersAndParams, LicenseCellEntity&gt; licenseCellValidator(@Qualifier(&quot;productIdsRegistry&quot;) ProductIdsRegistry productIdsRegistry) {
<span class="nc" id="L206">        List&lt;Validator&lt;HeadersAndParams&gt;&gt; headerAndParamValidators = List.of(new AuthTokenValidator(), new RequestIdValidator(), new ProductIdValidator(productIdsRegistry));</span>
<span class="nc" id="L207">        List&lt;Validator&lt;LicenseCellEntity&gt;&gt; requestBodyValidators = List.of(</span>
                new com.morningstar.martapi.validator.licenseapi.LicenseCellEntityValidator());
<span class="nc" id="L209">        return new RequestValidationHandler&lt;&gt;(headerAndParamValidators, requestBodyValidators);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>