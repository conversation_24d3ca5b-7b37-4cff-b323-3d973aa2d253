package com.morningstar.martapi.controller;

import com.morningstar.martapi.util.DateParamInputUtil;
import com.morningstar.martgateway.domains.core.entity.response.MartResponse;
import com.morningstar.martgateway.infrastructures.log.LogAttribute;
import com.morningstar.martgateway.infrastructures.log.LogEntity;
import com.morningstar.martgateway.infrastructures.log.LogEntry;
import com.morningstar.martgateway.infrastructures.log.LogHelper;
import com.morningstar.martgateway.interfaces.MartGateway;
import com.morningstar.martcommon.entity.result.request.MartRequest;
import com.morningstar.martgateway.util.JsonUtils;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.morningstar.martapi.config.Constant.RESPONSE;
import static com.morningstar.martapi.config.Constant.RESPONSE_ERROR;
import static com.morningstar.martapi.config.Constant.SUCCESSFUL_RESPONSE;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.EVENT_DESCRIPTION;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.EVENT_TYPE;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.EXCEPTION_TYPE;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.EXECUTE_TIME;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.PRODUCT_ID;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.REQUEST_PARAM;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.REQUEST_PAYLOAD;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.REQUEST_TYPE;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.USER_ID;

@RestController
@RequestMapping(value = {"/v1/security-data","/investment-api/v1/security-data"},
        produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE})
public class SecurityController {

    private static final Logger log = LoggerFactory.getLogger(SecurityController.class);

    private MartGateway martGateway;

    private static final String REQUEST_TYPE_VALUE = "martapi";


    @Inject
    public SecurityController(MartGateway martGateway) {
        this.martGateway = martGateway;
    }

    @GetMapping(value = "/investment-id/{idList}")
    public Mono<MartResponse> getDatapointValues(
            @ApiParam(example = "0P00000AWG,0P00000AYI,0P00000AZ0")
            @PathVariable("idList") String idList,
            @ApiParam(example = "119114,119115")
            @RequestParam(value = "dps", required = true) String dpList,
            @ApiParam(example = "2022-10-31")
            @RequestParam(value = "start-date", required = false, defaultValue = "") String startDate,
            @ApiParam(example = "2022-11-30")
            @RequestParam(value = "end-date", required = false, defaultValue = "") String endDate,
            @ApiParam(example = "EUR")
            @RequestParam(value = "currency", required = false, defaultValue = "") String currency,
            @ApiParam(example = "USD")
            @RequestParam(value = "precurrency", required = false, defaultValue = "") String preCurrency,
            @ApiParam(example = "false")
            @RequestParam(value = "read-cache", required = false, defaultValue = "") String readCache,
            @RequestParam(value = "due-stage", required = false, defaultValue = "0") String dueStage,
            @RequestParam(value= "date-format", required = false, defaultValue = "") String dateFormat,
            @RequestParam(value= "decimal-format", required = false, defaultValue = "") String decimalFormat,
            @RequestParam(value= "post-tax", required = false, defaultValue = "") String postTax,
            @RequestParam(value= "useRequireId", required = false, defaultValue = "false") String useRequireId,
            @RequestParam(value= "useNewCCS", required = false, defaultValue = "false") String useNewCCS,
            @RequestHeader(value = "X-Api-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "X-API-UserId", required = false, defaultValue = "") String userId,
            ServerHttpRequest request
    ) {
        long startTime = System.currentTimeMillis();
        List<String> rawList = Arrays.asList(idList.split(","));
        MartRequest martRequest = MartRequest.builder()
                .currency(currency)
                .dps(Arrays.asList(dpList.split(",")))
                .ids(rawList)
                .startDate(DateParamInputUtil.formatDateParameter(startDate))
                .endDate(DateParamInputUtil.formatDateParameter(endDate))
                .preCurrency(preCurrency)
                .readCache(readCache)
                .productId(productId)
                .requestId(requestId)
                .userId(userId)
                .dueStage(dueStage)
                .dateFormat(dateFormat)
                .decimalFormat(decimalFormat)
                .postTax(postTax)
                .useRequireId("true".equalsIgnoreCase(useRequireId))
                .useNewCCS(Boolean.parseBoolean(useNewCCS))
                .build();
        return martGateway.asyncRetrieveSecurities(martRequest).doOnEach(LogHelper.logOnNext(list -> {
                    List<LogEntity> logEntities = logAccess(martRequest, request, startTime, requestId, productId, userId);
                    LogEntry.info(logEntities.toArray(LogEntity[]::new));
                })).doOnEach(LogHelper.logOnError(e -> {
                    List<LogEntity> logEntities = logError(martRequest, request, startTime, requestId, e, productId, userId);
                    LogEntry.error(logEntities.toArray(LogEntity[]::new));
                }));
    }

    @PostMapping(value = "/investment-id/", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Mono<MartResponse> getDataPointsForAsOfDate(
            @RequestBody MartRequest martRequest,
            ServerHttpRequest request) {

        long startTime = System.currentTimeMillis();
        String reqId = StringUtils.isEmpty(martRequest.getRequestId()) ? UUID.randomUUID().toString() : martRequest.getRequestId();

        return martGateway.asyncRetrieveSecurities(martRequest).doOnEach(LogHelper.logOnNext(list -> {
                List<LogEntity> logEntities = logAccess(martRequest, request, startTime, reqId);
                    LogEntry.info(logEntities.toArray(LogEntity[]::new));
                })).doOnEach(LogHelper.logOnError(e -> {
                    List<LogEntity> logEntities = logError(martRequest, request, startTime, reqId, e);
                    LogEntry.error(logEntities.toArray(LogEntity[]::new));
                }));

    }

    @GetMapping(value = "/category-data")
    public Mono<MartResponse> getCategoryDatapointValues(
            @ApiParam(example = "57348,57367")
            @RequestParam(value = "dps", required = true) String dpList,
            @ApiParam(example = "1")
            @RequestParam(value = "peer-group-id", required = false, defaultValue = "") String peerGroupId,
            @ApiParam(example = "1")
            @RequestParam(value = "region-id", required = false, defaultValue = "") String regionId,
            @ApiParam(example = "$FOCA$LG$$")
            @RequestParam(value = "category-code", required = false, defaultValue = "") String categoryCode,
            @ApiParam(example = "2022-10-31")
            @RequestParam(value = "start-date", required = false, defaultValue = "") String startDate,
            @ApiParam(example = "2022-11-30")
            @RequestParam(value = "end-date", required = false, defaultValue = "") String endDate,
            @ApiParam(example = "FO")
            @RequestParam(value = "universe", required = false, defaultValue = "") String universe,
            @ApiParam(example = "false")
            @RequestParam(value = "read-cache", required = false, defaultValue = "") String readCache,
            @RequestParam(value= "post-tax", required = false, defaultValue = "") String postTax,
            @RequestParam(value= "useNewCCS", required = false, defaultValue = "false") String useNewCCS,
            @RequestHeader(value = "X-Api-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "X-API-UserId", required = false, defaultValue = "") String userId,
            ServerHttpRequest request
    ) {
        long startTime = System.currentTimeMillis();
        MartRequest martRequest = MartRequest.builder()
                .categoryCode(categoryCode)
                .peerGroupId(peerGroupId)
                .regionId(regionId)
                .dps(Arrays.asList(dpList.split(",")))
                .startDate(startDate)
                .endDate(endDate)
                .universe(universe)
                .readCache(readCache)
                .postTax(postTax)
                .productId(productId)
                .requestId(requestId)
                .useNewCCS(Boolean.parseBoolean(useNewCCS))
                .build();
        return martGateway.asyncRetrieveSecurities(martRequest).doOnEach(LogHelper.logOnNext(list -> {
               List<LogEntity> logEntities = logAccess(martRequest, request, startTime, requestId, productId, userId);
               LogEntry.info(logEntities.toArray(LogEntity[]::new));
               })).doOnEach(LogHelper.logOnError(e -> {
                    List<LogEntity> logEntities = logError(martRequest, request, startTime, requestId, e, productId, userId);
                    LogEntry.error(logEntities.toArray(LogEntity[]::new));
                }));
    }

    private List<LogEntity> logAccess(
            MartRequest martRequest,
            ServerHttpRequest request,
            long startTime,
            String requestId,
            String productId,
            String userId
    ) {
        List<LogEntity> logEntities = logAccess(martRequest, request, startTime, requestId);
        logEntities.add(new LogEntity(PRODUCT_ID, productId));
        logEntities.add(new LogEntity(USER_ID, userId));
        return logEntities;
    }

    private List<LogEntity> logAccess(
            MartRequest martRequest,
            ServerHttpRequest request,
            long startTime,
            String requestId
    ) {
        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), requestId);
        long executionTime = System.currentTimeMillis() - startTime;
        List<LogEntity> logEntities = Stream.of(
                new LogEntity(EVENT_TYPE, RESPONSE),
                new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
                new LogEntity(REQUEST_PARAM, martRequest.getRequestParam(request.getURI().getPath(), request.getURI().getQuery())),
                new LogEntity(EXECUTE_TIME, executionTime)
        ).collect(Collectors.toCollection(ArrayList::new));
        if (Objects.requireNonNull(request.getMethod()).matches(HttpMethod.POST.name())) {
            logEntities.add(new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(martRequest)));
        }
        return logEntities;
    }

    private List<LogEntity> logError(
            MartRequest martRequest,
            ServerHttpRequest request,
            long startTime,
            String requestId,
            Throwable e
    ) {
        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), requestId);
        return Stream.of(
                new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                new LogEntity(EVENT_DESCRIPTION, e),
                new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
                new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(martRequest)),
                new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime),
                new LogEntity(EXCEPTION_TYPE, e.getClass()),
                new LogEntity(REQUEST_PARAM, martRequest.getRequestParam(request.getURI().getPath(), request.getURI().getQuery()))
        ).collect(Collectors.toCollection(ArrayList::new));
    }

    private List<LogEntity> logError(
            MartRequest martRequest,
            ServerHttpRequest request,
            long startTime,
            String requestId,
            Throwable e,
            String productId,
            String userId
    ) {
        List<LogEntity> logEntities = logError(martRequest, request, startTime, requestId, e);
        logEntities.add(new LogEntity(PRODUCT_ID, productId));
        logEntities.add(new LogEntity(USER_ID, userId));
        return logEntities;
    }
}
