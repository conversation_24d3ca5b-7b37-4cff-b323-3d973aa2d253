package com.morningstar.martapi.exception;

import com.morningstar.martapi.controller.AsyncApiController;
import com.morningstar.martapi.controller.DocumentController;
import com.morningstar.martapi.controller.IndexDataController;
import com.morningstar.martapi.controller.InvestmentController;
import com.morningstar.martapi.controller.SecurityController;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.martgateway.domains.entitlement.exceptions.EntitlementException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.core.codec.DecodingException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.ServerWebInputException;

import static com.morningstar.martapi.exception.utils.ExceptionHandlerUtils.extractProductId;
import static com.morningstar.martapi.exception.utils.ExceptionHandlerUtils.extractStatusCode;

@Order(Ordered.HIGHEST_PRECEDENCE)
@RestControllerAdvice(assignableTypes = {InvestmentController.class, AsyncApiController.class, DocumentController.class, IndexDataController.class, SecurityController.class})
public class GridViewExceptionHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(GridViewExceptionHandler.class);

    @ExceptionHandler(value = {DecodingException.class, ServerWebInputException.class})
    public ResponseEntity<InvestmentResponse> handleInvalidRequestInput(
            Exception e,
            ServerWebExchange ex) {
        String url = ex.getRequest().getURI().toString();
        String productId = extractProductId(ex);
        LOGGER.warn("event_type=\"Invalid Request Input\", " +
                "event_description=\"Request Input not valid JSON\", " +
                "url=\"{}\", product_id=\"{}\", e=\"{}\"", url, productId, e.getMessage());
        return ResponseEntity.badRequest().body(new InvestmentResponse(Status.INVALID_REQUEST_INPUT, null));
    }

    @ExceptionHandler(value = InvestmentApiValidationException.class)
    public ResponseEntity<InvestmentResponse> handleValidationException(
            InvestmentApiValidationException e,
            ServerWebExchange ex) {
        String url = ex.getRequest().getURI().toString();
        String productId = extractProductId(ex);
        LOGGER.warn("event_type=\"Investment API Validation\", " +
                "event_description=\"Validation failure\", " +
                "url=\"{}\", product_id=\"{}\", e=\"{}\"", url, productId, e.getMessage());
        return ResponseEntity
                .status(extractStatusCode(e.getStatus()))
                .body(new InvestmentResponse(e.getStatus(), null));
    }

    @ExceptionHandler(value = EntitlementException.class)
    public ResponseEntity<InvestmentResponse> handleEntitlementException(EntitlementException e, ServerWebExchange ex) {
        String url = ex.getRequest().getURI().toString();
        String productId = extractProductId(ex);
        MDC.put("request_id",e.getRequestId());
        LOGGER.warn("event_type=\"Investment API Authorization Failure\", " +
                "event_description=\"Entitlement retrieval failed\", " +
                "url=\"{}\", product_id=\"{}\", e=\"{}\"", url, productId, e.getMessage());
        MDC.clear();
        return ResponseEntity
                .status(extractStatusCode(e.getStatus()))
                .body(new InvestmentResponse(e.getStatus(), null));
    }

    @ExceptionHandler(value = IllegalArgumentException.class)
    public ResponseEntity<InvestmentResponse> handleBadRequestException(Exception e,
                                                                        ServerWebExchange ex) {
        String url = ex.getRequest().getURI().toString();
        LOGGER.warn("event_type=\"BadRequest Exception\", " +
                "event_description=\"Unexpected issue encountered\", " +
                "url=\"{}\", e=\"{}\"", url, e.getMessage());
        return ResponseEntity.badRequest()
                .body(badRequestError(e.getMessage()));
    }

    @ExceptionHandler(value = {TranscriptApiException.class})
    public ResponseEntity<InvestmentResponse> handleNotFoundException(TranscriptApiException e,
                                                                      ServerWebExchange ex)  {
        String url = ex.getRequest().getURI().toString();
        HeadersAndParams headersAndParams = e.getHeadersAndParams();
        String userID = e.getUserId();
        MDC.put("request_id",headersAndParams.getRequestId());
        MDC.clear();
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(notFoundError(e.getMessage() + " - " + e.getCause()));
    }

    @ExceptionHandler(value = Exception.class)
    public ResponseEntity<InvestmentResponse> handleOtherException(Exception e,
                                                                   ServerWebExchange ex) {
        String url = ex.getRequest().getURI().toString();
        LOGGER.warn("event_type=\"Unexpected Exception\", " +
                "event_description=\"Unexpected issue encountered\", " +
                "url=\"{}\", e=\"{}\"", url, e.getMessage());
        return ResponseEntity.internalServerError()
                .body(internalServerError(e.getMessage()));
    }

    @ExceptionHandler(value = {IndexAPIException.class})
    public ResponseEntity<InvestmentResponse> handleIndexAPIException(IndexAPIException e,
                                                                      ServerWebExchange ex) {
        String url = ex.getRequest().getURI().toString();
        LOGGER.warn("event_type=\"RESPONSE_ERROR\",request_type=\"index\", " +
                "event_description=\"Excetpion in Index API\", " +
                "url=\"{}\", e=\"{}\"", url, e.getMessage());
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(internalServerError("Internal Error"));
    }


    public InvestmentResponse internalServerError(String message) {
        Status status = Status.INTERNAL_ERROR.withMessage(message);
        return new InvestmentResponse(status, null);
    }

    public InvestmentResponse badRequestError(String message) {
        Status status = Status.BAD_REQUEST.withMessage(message);
        return new InvestmentResponse(status, null);
    }

    public InvestmentResponse notFoundError(String message) {
        Status status = Status.NOT_FOUND.withMessage(message);
        return new InvestmentResponse(status, null);
    }
}
