package com.morningstar.martapi.controller;

import com.morningstar.martapi.exception.IndexAPIException;
import com.morningstar.martapi.util.InvestmentApiRequestUtil;
import com.morningstar.martapi.util.TokenUtil;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.index.entity.IndexDataResult;
import com.morningstar.martgateway.domains.index.service.IndexDataService;
import com.morningstar.martgateway.interfaces.model.investmentapi.InvestmentApiRequest;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;
import reactor.util.Logger;
import reactor.util.Loggers;

import javax.inject.Inject;

@RestController
@RequestMapping(value = {"/v1/reference-data/","/investment-api/v1/reference-data/"},
        produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE})
public class IndexDataController {

    private final IndexDataService indexDataService;
    private final RequestValidationHandler<HeadersAndParams, InvestmentApiRequest> validator;
    private static final Logger log = Loggers.getLogger(IndexDataController.class);

    @Inject
    public IndexDataController(IndexDataService indexDataService, @Qualifier("investmentApiValidator") RequestValidationHandler<HeadersAndParams, InvestmentApiRequest> validator) {
        this.indexDataService = indexDataService;
        this.validator = validator;
    }

    @GetMapping(value = "/module-id/{moduleId}")
    public Mono<IndexDataResult> getReferenceIndexData(
            @ApiParam(example = "BTWS000001")
            @PathVariable("moduleId") String moduleId,
            @RequestHeader(value = "X-Api-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String token
    ) {

        long startTime = System.currentTimeMillis();
        HeadersAndParams headersAndParams = new InvestmentApiRequestUtil().getHeaderAndParams(token, productId, requestId);
        String userIdFromToken = TokenUtil.getUserId(token);
        validator.validateHeadersAndParams(headersAndParams);

        return indexDataService.getData(moduleId)
                .doOnError(e -> {
                    log.error("event_type=\"RESPONSE_ERROR\", request_type=\"index\" event_description=\"Index reference  Data failed\", user_id=\"{}\",product_id=\"{}\", request_id=\"{}\", error_message=\"{}\"", userIdFromToken, productId, requestId, e.getMessage());
                    throw new IndexAPIException(e.getMessage());
                })
                .doOnSuccess(s ->
                {
                    long executeTime = System.currentTimeMillis() - startTime;
                    log.info("event_type=\"RESPONSE\", request_type=\"index\", event_description=\"Index data fetched successfully!!\" ,executeTime=\"{}\", user_id=\"{}\", product_id=\"{}\", request_id=\"{}\"", executeTime, userIdFromToken, productId, requestId);
                });
    }
}