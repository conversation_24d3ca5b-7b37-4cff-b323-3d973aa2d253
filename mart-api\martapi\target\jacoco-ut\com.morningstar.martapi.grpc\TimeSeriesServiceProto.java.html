<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TimeSeriesServiceProto.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <a href="index.source.html" class="el_package">com.morningstar.martapi.grpc</a> &gt; <span class="el_source">TimeSeriesServiceProto.java</span></div><h1>TimeSeriesServiceProto.java</h1><pre class="source lang-java linenums">// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: timeseries_service.proto

// Protobuf Java Version: 3.25.1
package com.morningstar.martapi.grpc;

public final class TimeSeriesServiceProto {
  private TimeSeriesServiceProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
<span class="nc" id="L11">  }</span>

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
<span class="nc" id="L15">    registerAllExtensions(</span>
        (com.google.protobuf.ExtensionRegistryLite) registry);
<span class="nc" id="L17">  }</span>
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_timeseries_TimeSeriesRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_timeseries_TimeSeriesRequest_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
<span class="nc" id="L26">    return descriptor;</span>
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
<span class="nc" id="L31">    java.lang.String[] descriptorData = {</span>
      &quot;\n\030timeseries_service.proto\022\ntimeseries\032\021&quot; +
      &quot;TsCacheData.proto\&quot;\331\003\n\021TimeSeriesRequest\022&quot; +
      &quot;\026\n\016investment_ids\030\001 \003(\t\022\023\n\013data_points\030\002&quot; +
      &quot; \003(\t\022\022\n\nstart_date\030\003 \001(\t\022\020\n\010end_date\030\004 \001&quot; +
      &quot;(\t\022\020\n\010currency\030\005 \001(\t\022\024\n\014pre_currency\030\006 \001&quot; +
      &quot;(\t\022\022\n\nread_cache\030\007 \001(\t\022\023\n\013date_format\030\010 &quot; +
      &quot;\001(\t\022\026\n\016decimal_format\030\t \001(\t\022\032\n\022extend_pe&quot; +
      &quot;rformance\030\n \001(\t\022\020\n\010post_tax\030\013 \001(\t\022\026\n\016use&quot; +
      &quot;_require_id\030\014 \001(\010\022\020\n\010use_case\030\r \001(\t\022\023\n\013u&quot; +
      &quot;se_new_ccs\030\016 \001(\010\022\022\n\nproduct_id\030\017 \001(\t\022\022\n\n&quot; +
      &quot;request_id\030\020 \001(\t\022\020\n\010trace_id\030\021 \001(\t\022\017\n\007us&quot; +
      &quot;er_id\030\022 \001(\t\022\025\n\rauthorization\030\023 \001(\t\022\031\n\021ch&quot; +
      &quot;eck_entitlement\030\024 \001(\010\022\036\n\026entitlement_pro&quot; +
      &quot;duct_id\030\025 \001(\t2b\n\021TimeSeriesService\022M\n\021Ge&quot; +
      &quot;tTimeSeriesData\022\035.timeseries.TimeSeriesR&quot; +
      &quot;equest\032\031.protobuf.TimeSeriesDatasB8\n\034com&quot; +
      &quot;.morningstar.martapi.grpcB\026TimeSeriesSer&quot; +
      &quot;viceProtoP\001b\006proto3&quot;
    };
<span class="nc" id="L51">    descriptor = com.google.protobuf.Descriptors.FileDescriptor</span>
<span class="nc" id="L52">      .internalBuildGeneratedFileFrom(descriptorData,</span>
        new com.google.protobuf.Descriptors.FileDescriptor[] {
<span class="nc" id="L54">          com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.getDescriptor(),</span>
        });
    internal_static_timeseries_TimeSeriesRequest_descriptor =
<span class="nc" id="L57">      getDescriptor().getMessageTypes().get(0);</span>
<span class="nc" id="L58">    internal_static_timeseries_TimeSeriesRequest_fieldAccessorTable = new</span>
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_timeseries_TimeSeriesRequest_descriptor,
        new java.lang.String[] { &quot;InvestmentIds&quot;, &quot;DataPoints&quot;, &quot;StartDate&quot;, &quot;EndDate&quot;, &quot;Currency&quot;, &quot;PreCurrency&quot;, &quot;ReadCache&quot;, &quot;DateFormat&quot;, &quot;DecimalFormat&quot;, &quot;ExtendPerformance&quot;, &quot;PostTax&quot;, &quot;UseRequireId&quot;, &quot;UseCase&quot;, &quot;UseNewCcs&quot;, &quot;ProductId&quot;, &quot;RequestId&quot;, &quot;TraceId&quot;, &quot;UserId&quot;, &quot;Authorization&quot;, &quot;CheckEntitlement&quot;, &quot;EntitlementProductId&quot;, });
<span class="nc" id="L62">    com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.getDescriptor();</span>
<span class="nc" id="L63">  }</span>

  // @@protoc_insertion_point(outer_class_scope)
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>