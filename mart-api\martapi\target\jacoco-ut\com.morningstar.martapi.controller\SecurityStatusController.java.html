<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SecurityStatusController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <a href="index.source.html" class="el_package">com.morningstar.martapi.controller</a> &gt; <span class="el_source">SecurityStatusController.java</span></div><h1>SecurityStatusController.java</h1><pre class="source lang-java linenums">package com.morningstar.martapi.controller;

import com.morningstar.martcommon.entity.result.request.IdMapper;
import com.morningstar.martgateway.domains.core.entity.response.MartData;
import com.morningstar.martgateway.domains.core.entity.response.MartResponse;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.martgateway.util.IdMapUtil;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.inject.Inject;
import java.util.Arrays;
import springfox.documentation.annotations.ApiIgnore;

@RestController
@RequestMapping(value = {&quot;/v1/security-status&quot;,&quot;/investment-api/v1/security-status&quot;},
        produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE})
@ApiIgnore
public class SecurityStatusController {

    private IdMapUtil idMapUtil;

    @Inject
<span class="nc" id="L29">    public SecurityStatusController(IdMapUtil idMapUtil) {</span>
<span class="nc" id="L30">        this.idMapUtil = idMapUtil;</span>
<span class="nc" id="L31">    }</span>
    @GetMapping(value = &quot;/{idList}&quot;)
    public Mono&lt;MartResponse&gt; checkSecurityStatus(@PathVariable(&quot;idList&quot;) String idList) {
<span class="nc" id="L34">        return Flux.fromIterable(idMapUtil.getIdMappers(Arrays.asList(idList.split(&quot;,&quot;))))</span>
<span class="nc" id="L35">                .map(IdMapper::transformToResponse)</span>
<span class="nc" id="L36">                .collectList()</span>
<span class="nc" id="L37">                .map(rList -&gt; new MartResponse(Status.OK, new MartData(rList)));</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>