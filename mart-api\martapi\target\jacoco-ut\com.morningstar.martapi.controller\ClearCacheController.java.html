<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ClearCacheController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <a href="index.source.html" class="el_package">com.morningstar.martapi.controller</a> &gt; <span class="el_source">ClearCacheController.java</span></div><h1>ClearCacheController.java</h1><pre class="source lang-java linenums">package com.morningstar.martapi.controller;

import com.morningstar.martapi.service.RedisMessagePublisher;
import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martcommon.entity.ClearCacheRequest;
import com.morningstar.martgateway.domains.core.entity.response.MartResponse;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.martgateway.interfaces.MartGateway;
import com.morningstar.martgateway.util.JwtUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;
import springfox.documentation.annotations.ApiIgnore;

import javax.inject.Inject;
import java.util.UUID;

@RestController
@RequestMapping(value = {&quot;/v1/clear-cache&quot;}, produces = {MediaType.APPLICATION_JSON_VALUE})
@ApiIgnore
public class ClearCacheController {

<span class="nc" id="L36">    private static final Logger log = LoggerFactory.getLogger(ClearCacheController.class);</span>
    private final RequestValidationHandler&lt;HeadersAndParams, ClearCacheRequest&gt; validator;
    private final MartGateway&lt;MartResponse, ClearCacheRequest&gt; clearCacheGateway;
    private RedisMessagePublisher redisMessagePublisher;

    @Inject
    public ClearCacheController(
            MartGateway&lt;MartResponse, ClearCacheRequest&gt; clearCacheGateway,
            @Qualifier(&quot;clearCacheValidator&quot;) RequestValidationHandler&lt;HeadersAndParams, ClearCacheRequest&gt; validator,
<span class="nc" id="L45">            RedisMessagePublisher redisMessagePublisher) {</span>
<span class="nc" id="L46">        this.validator = validator;</span>
<span class="nc" id="L47">        this.clearCacheGateway = clearCacheGateway;</span>
<span class="nc" id="L48">        this.redisMessagePublisher = redisMessagePublisher;</span>
<span class="nc" id="L49">    }</span>

    @PostMapping(value = &quot;&quot;, consumes = MediaType.APPLICATION_JSON_VALUE)
    public Mono&lt;MartResponse&gt; clearCache(
            @RequestBody ClearCacheRequest clearCacheRequest,
            @RequestHeader(value = &quot;X-Api-ProductId&quot;, required = false, defaultValue = &quot;&quot;) String productId,
            @RequestHeader(value = &quot;X-API-RequestId&quot;, required = false, defaultValue = &quot;&quot;) String requestId,
            @RequestHeader(value = &quot;Authorization&quot;, required = false, defaultValue = &quot;&quot;) String token,
            ServerHttpRequest request)
    {
<span class="nc" id="L59">        HeadersAndParams headersAndParams = HeadersAndParams.builder()</span>
<span class="nc" id="L60">                .authorizationToken(token)</span>
<span class="nc" id="L61">                .productId(productId)</span>
<span class="nc bnc" id="L62" title="All 2 branches missed.">                .requestId(StringUtils.hasLength(requestId) ? requestId : UUID.randomUUID().toString())</span>
<span class="nc" id="L63">                .build();</span>
<span class="nc" id="L64">        validateRequest(clearCacheRequest, headersAndParams);</span>
<span class="nc" id="L65">        String userIdFromToken = JwtUtil.getFieldValue(token, &quot;https://morningstar.com/mstar_id&quot;);</span>
<span class="nc" id="L66">        clearCacheRequest.setUserId(userIdFromToken);</span>
<span class="nc" id="L67">        clearCacheRequest.setRequestId(requestId);</span>

<span class="nc" id="L69">         return clearCacheGateway.asyncRetrieveSecurities(clearCacheRequest);</span>
    }

    @DeleteMapping(value = &quot;uim-token&quot;)
    public ResponseEntity&lt;Void&gt; clearUimCache (
            @RequestHeader(value = &quot;X-Api-ProductId&quot;, required = false, defaultValue = &quot;&quot;) String productId,
            @RequestHeader(value = &quot;X-API-RequestId&quot;, required = false, defaultValue = &quot;&quot;) String requestId,
            @RequestHeader(value = &quot;Authorization&quot;, required = false, defaultValue = &quot;&quot;) String token) {
<span class="nc bnc" id="L77" title="All 2 branches missed.">        if (!&quot;mds&quot;.equalsIgnoreCase(productId)) { // only allow mds</span>
<span class="nc" id="L78">            throw new ValidationException(Status.INVALID_PRODUCT_ID);</span>
        }
<span class="nc" id="L80">        HeadersAndParams headersAndParams = HeadersAndParams.builder()</span>
<span class="nc" id="L81">                .authorizationToken(token)</span>
<span class="nc bnc" id="L82" title="All 2 branches missed.">                .requestId(StringUtils.hasLength(requestId) ? requestId : UUID.randomUUID().toString())</span>
<span class="nc" id="L83">                .build();</span>
<span class="nc" id="L84">        validator.validateHeadersAndParams(headersAndParams);</span>
<span class="nc" id="L85">        String userIdFromToken = JwtUtil.getFieldValue(token, &quot;https://morningstar.com/mstar_id&quot;);</span>
<span class="nc" id="L86">        log.info(&quot;Received request to clear UIM token cache from {}&quot;, userIdFromToken);</span>
        try {
<span class="nc" id="L88">            redisMessagePublisher.publishUimTokenClearCache(&quot;Clear UIM Token Cache&quot;);</span>
<span class="nc" id="L89">            return ResponseEntity.noContent().build();</span>
<span class="nc" id="L90">        } catch (Exception e) {</span>
<span class="nc" id="L91">            log.error(&quot;event_type=\&quot;Clear Cache\&quot;, event_description=\&quot;Clear UIM Token Cache Error\&quot;, request_id=\&quot;{}\&quot;, error_message=\&quot;{}\&quot;&quot;, requestId, e.getMessage());</span>
<span class="nc" id="L92">            return ResponseEntity.internalServerError().build();</span>
        }
    }

    private void validateRequest(ClearCacheRequest clearCacheRequest, HeadersAndParams headersAndParams) {
<span class="nc" id="L97">        validator.validateHeadersAndParams(headersAndParams);</span>
<span class="nc" id="L98">        validator.validateRequestBody(clearCacheRequest);</span>
<span class="nc" id="L99">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>