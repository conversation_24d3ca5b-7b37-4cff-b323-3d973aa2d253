// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: timeseries_service.proto

// Protobuf Java Version: 3.25.1
package com.morningstar.martapi.grpc;

/**
 * <pre>
 * Request message for time series data
 * </pre>
 *
 * Protobuf type {@code timeseries.TimeSeriesRequest}
 */
public final class TimeSeriesRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:timeseries.TimeSeriesRequest)
    TimeSeriesRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use TimeSeriesRequest.newBuilder() to construct.
  private TimeSeriesRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private TimeSeriesRequest() {
    investmentIds_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    dataPoints_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    startDate_ = "";
    endDate_ = "";
    currency_ = "";
    preCurrency_ = "";
    readCache_ = "";
    dateFormat_ = "";
    decimalFormat_ = "";
    extendPerformance_ = "";
    postTax_ = "";
    useCase_ = "";
    productId_ = "";
    requestId_ = "";
    traceId_ = "";
    userId_ = "";
    authorization_ = "";
    entitlementProductId_ = "";
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new TimeSeriesRequest();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.morningstar.martapi.grpc.TimeSeriesServiceProto.internal_static_timeseries_TimeSeriesRequest_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.morningstar.martapi.grpc.TimeSeriesServiceProto.internal_static_timeseries_TimeSeriesRequest_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.morningstar.martapi.grpc.TimeSeriesRequest.class, com.morningstar.martapi.grpc.TimeSeriesRequest.Builder.class);
  }

  public static final int INVESTMENT_IDS_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private com.google.protobuf.LazyStringArrayList investmentIds_ =
      com.google.protobuf.LazyStringArrayList.emptyList();
  /**
   * <pre>
   * Investment IDs (comma-separated in REST, repeated here)
   * </pre>
   *
   * <code>repeated string investment_ids = 1;</code>
   * @return A list containing the investmentIds.
   */
  public com.google.protobuf.ProtocolStringList
      getInvestmentIdsList() {
    return investmentIds_;
  }
  /**
   * <pre>
   * Investment IDs (comma-separated in REST, repeated here)
   * </pre>
   *
   * <code>repeated string investment_ids = 1;</code>
   * @return The count of investmentIds.
   */
  public int getInvestmentIdsCount() {
    return investmentIds_.size();
  }
  /**
   * <pre>
   * Investment IDs (comma-separated in REST, repeated here)
   * </pre>
   *
   * <code>repeated string investment_ids = 1;</code>
   * @param index The index of the element to return.
   * @return The investmentIds at the given index.
   */
  public java.lang.String getInvestmentIds(int index) {
    return investmentIds_.get(index);
  }
  /**
   * <pre>
   * Investment IDs (comma-separated in REST, repeated here)
   * </pre>
   *
   * <code>repeated string investment_ids = 1;</code>
   * @param index The index of the value to return.
   * @return The bytes of the investmentIds at the given index.
   */
  public com.google.protobuf.ByteString
      getInvestmentIdsBytes(int index) {
    return investmentIds_.getByteString(index);
  }

  public static final int DATA_POINTS_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private com.google.protobuf.LazyStringArrayList dataPoints_ =
      com.google.protobuf.LazyStringArrayList.emptyList();
  /**
   * <pre>
   * Data points (comma-separated in REST, repeated here)
   * </pre>
   *
   * <code>repeated string data_points = 2;</code>
   * @return A list containing the dataPoints.
   */
  public com.google.protobuf.ProtocolStringList
      getDataPointsList() {
    return dataPoints_;
  }
  /**
   * <pre>
   * Data points (comma-separated in REST, repeated here)
   * </pre>
   *
   * <code>repeated string data_points = 2;</code>
   * @return The count of dataPoints.
   */
  public int getDataPointsCount() {
    return dataPoints_.size();
  }
  /**
   * <pre>
   * Data points (comma-separated in REST, repeated here)
   * </pre>
   *
   * <code>repeated string data_points = 2;</code>
   * @param index The index of the element to return.
   * @return The dataPoints at the given index.
   */
  public java.lang.String getDataPoints(int index) {
    return dataPoints_.get(index);
  }
  /**
   * <pre>
   * Data points (comma-separated in REST, repeated here)
   * </pre>
   *
   * <code>repeated string data_points = 2;</code>
   * @param index The index of the value to return.
   * @return The bytes of the dataPoints at the given index.
   */
  public com.google.protobuf.ByteString
      getDataPointsBytes(int index) {
    return dataPoints_.getByteString(index);
  }

  public static final int START_DATE_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object startDate_ = "";
  /**
   * <pre>
   * Date range
   * </pre>
   *
   * <code>string start_date = 3;</code>
   * @return The startDate.
   */
  @java.lang.Override
  public java.lang.String getStartDate() {
    java.lang.Object ref = startDate_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      startDate_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * Date range
   * </pre>
   *
   * <code>string start_date = 3;</code>
   * @return The bytes for startDate.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getStartDateBytes() {
    java.lang.Object ref = startDate_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      startDate_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int END_DATE_FIELD_NUMBER = 4;
  @SuppressWarnings("serial")
  private volatile java.lang.Object endDate_ = "";
  /**
   * <code>string end_date = 4;</code>
   * @return The endDate.
   */
  @java.lang.Override
  public java.lang.String getEndDate() {
    java.lang.Object ref = endDate_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      endDate_ = s;
      return s;
    }
  }
  /**
   * <code>string end_date = 4;</code>
   * @return The bytes for endDate.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getEndDateBytes() {
    java.lang.Object ref = endDate_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      endDate_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CURRENCY_FIELD_NUMBER = 5;
  @SuppressWarnings("serial")
  private volatile java.lang.Object currency_ = "";
  /**
   * <pre>
   * Currency settings
   * </pre>
   *
   * <code>string currency = 5;</code>
   * @return The currency.
   */
  @java.lang.Override
  public java.lang.String getCurrency() {
    java.lang.Object ref = currency_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      currency_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * Currency settings
   * </pre>
   *
   * <code>string currency = 5;</code>
   * @return The bytes for currency.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCurrencyBytes() {
    java.lang.Object ref = currency_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      currency_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PRE_CURRENCY_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object preCurrency_ = "";
  /**
   * <code>string pre_currency = 6;</code>
   * @return The preCurrency.
   */
  @java.lang.Override
  public java.lang.String getPreCurrency() {
    java.lang.Object ref = preCurrency_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      preCurrency_ = s;
      return s;
    }
  }
  /**
   * <code>string pre_currency = 6;</code>
   * @return The bytes for preCurrency.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPreCurrencyBytes() {
    java.lang.Object ref = preCurrency_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      preCurrency_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int READ_CACHE_FIELD_NUMBER = 7;
  @SuppressWarnings("serial")
  private volatile java.lang.Object readCache_ = "";
  /**
   * <pre>
   * Cache and format settings
   * </pre>
   *
   * <code>string read_cache = 7;</code>
   * @return The readCache.
   */
  @java.lang.Override
  public java.lang.String getReadCache() {
    java.lang.Object ref = readCache_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      readCache_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * Cache and format settings
   * </pre>
   *
   * <code>string read_cache = 7;</code>
   * @return The bytes for readCache.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getReadCacheBytes() {
    java.lang.Object ref = readCache_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      readCache_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DATE_FORMAT_FIELD_NUMBER = 8;
  @SuppressWarnings("serial")
  private volatile java.lang.Object dateFormat_ = "";
  /**
   * <code>string date_format = 8;</code>
   * @return The dateFormat.
   */
  @java.lang.Override
  public java.lang.String getDateFormat() {
    java.lang.Object ref = dateFormat_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      dateFormat_ = s;
      return s;
    }
  }
  /**
   * <code>string date_format = 8;</code>
   * @return The bytes for dateFormat.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDateFormatBytes() {
    java.lang.Object ref = dateFormat_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      dateFormat_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int DECIMAL_FORMAT_FIELD_NUMBER = 9;
  @SuppressWarnings("serial")
  private volatile java.lang.Object decimalFormat_ = "";
  /**
   * <code>string decimal_format = 9;</code>
   * @return The decimalFormat.
   */
  @java.lang.Override
  public java.lang.String getDecimalFormat() {
    java.lang.Object ref = decimalFormat_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      decimalFormat_ = s;
      return s;
    }
  }
  /**
   * <code>string decimal_format = 9;</code>
   * @return The bytes for decimalFormat.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDecimalFormatBytes() {
    java.lang.Object ref = decimalFormat_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      decimalFormat_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int EXTEND_PERFORMANCE_FIELD_NUMBER = 10;
  @SuppressWarnings("serial")
  private volatile java.lang.Object extendPerformance_ = "";
  /**
   * <pre>
   * Performance and tax settings
   * </pre>
   *
   * <code>string extend_performance = 10;</code>
   * @return The extendPerformance.
   */
  @java.lang.Override
  public java.lang.String getExtendPerformance() {
    java.lang.Object ref = extendPerformance_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      extendPerformance_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * Performance and tax settings
   * </pre>
   *
   * <code>string extend_performance = 10;</code>
   * @return The bytes for extendPerformance.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getExtendPerformanceBytes() {
    java.lang.Object ref = extendPerformance_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      extendPerformance_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int POST_TAX_FIELD_NUMBER = 11;
  @SuppressWarnings("serial")
  private volatile java.lang.Object postTax_ = "";
  /**
   * <code>string post_tax = 11;</code>
   * @return The postTax.
   */
  @java.lang.Override
  public java.lang.String getPostTax() {
    java.lang.Object ref = postTax_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      postTax_ = s;
      return s;
    }
  }
  /**
   * <code>string post_tax = 11;</code>
   * @return The bytes for postTax.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPostTaxBytes() {
    java.lang.Object ref = postTax_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      postTax_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int USE_REQUIRE_ID_FIELD_NUMBER = 12;
  private boolean useRequireId_ = false;
  /**
   * <pre>
   * Feature flags
   * </pre>
   *
   * <code>bool use_require_id = 12;</code>
   * @return The useRequireId.
   */
  @java.lang.Override
  public boolean getUseRequireId() {
    return useRequireId_;
  }

  public static final int USE_CASE_FIELD_NUMBER = 13;
  @SuppressWarnings("serial")
  private volatile java.lang.Object useCase_ = "";
  /**
   * <code>string use_case = 13;</code>
   * @return The useCase.
   */
  @java.lang.Override
  public java.lang.String getUseCase() {
    java.lang.Object ref = useCase_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      useCase_ = s;
      return s;
    }
  }
  /**
   * <code>string use_case = 13;</code>
   * @return The bytes for useCase.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getUseCaseBytes() {
    java.lang.Object ref = useCase_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      useCase_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int USE_NEW_CCS_FIELD_NUMBER = 14;
  private boolean useNewCcs_ = false;
  /**
   * <code>bool use_new_ccs = 14;</code>
   * @return The useNewCcs.
   */
  @java.lang.Override
  public boolean getUseNewCcs() {
    return useNewCcs_;
  }

  public static final int PRODUCT_ID_FIELD_NUMBER = 15;
  @SuppressWarnings("serial")
  private volatile java.lang.Object productId_ = "";
  /**
   * <pre>
   * Headers
   * </pre>
   *
   * <code>string product_id = 15;</code>
   * @return The productId.
   */
  @java.lang.Override
  public java.lang.String getProductId() {
    java.lang.Object ref = productId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      productId_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * Headers
   * </pre>
   *
   * <code>string product_id = 15;</code>
   * @return The bytes for productId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getProductIdBytes() {
    java.lang.Object ref = productId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      productId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int REQUEST_ID_FIELD_NUMBER = 16;
  @SuppressWarnings("serial")
  private volatile java.lang.Object requestId_ = "";
  /**
   * <code>string request_id = 16;</code>
   * @return The requestId.
   */
  @java.lang.Override
  public java.lang.String getRequestId() {
    java.lang.Object ref = requestId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      requestId_ = s;
      return s;
    }
  }
  /**
   * <code>string request_id = 16;</code>
   * @return The bytes for requestId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRequestIdBytes() {
    java.lang.Object ref = requestId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      requestId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int TRACE_ID_FIELD_NUMBER = 17;
  @SuppressWarnings("serial")
  private volatile java.lang.Object traceId_ = "";
  /**
   * <code>string trace_id = 17;</code>
   * @return The traceId.
   */
  @java.lang.Override
  public java.lang.String getTraceId() {
    java.lang.Object ref = traceId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      traceId_ = s;
      return s;
    }
  }
  /**
   * <code>string trace_id = 17;</code>
   * @return The bytes for traceId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTraceIdBytes() {
    java.lang.Object ref = traceId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      traceId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int USER_ID_FIELD_NUMBER = 18;
  @SuppressWarnings("serial")
  private volatile java.lang.Object userId_ = "";
  /**
   * <code>string user_id = 18;</code>
   * @return The userId.
   */
  @java.lang.Override
  public java.lang.String getUserId() {
    java.lang.Object ref = userId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      userId_ = s;
      return s;
    }
  }
  /**
   * <code>string user_id = 18;</code>
   * @return The bytes for userId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getUserIdBytes() {
    java.lang.Object ref = userId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      userId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AUTHORIZATION_FIELD_NUMBER = 19;
  @SuppressWarnings("serial")
  private volatile java.lang.Object authorization_ = "";
  /**
   * <code>string authorization = 19;</code>
   * @return The authorization.
   */
  @java.lang.Override
  public java.lang.String getAuthorization() {
    java.lang.Object ref = authorization_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      authorization_ = s;
      return s;
    }
  }
  /**
   * <code>string authorization = 19;</code>
   * @return The bytes for authorization.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAuthorizationBytes() {
    java.lang.Object ref = authorization_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      authorization_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int CHECK_ENTITLEMENT_FIELD_NUMBER = 20;
  private boolean checkEntitlement_ = false;
  /**
   * <code>bool check_entitlement = 20;</code>
   * @return The checkEntitlement.
   */
  @java.lang.Override
  public boolean getCheckEntitlement() {
    return checkEntitlement_;
  }

  public static final int ENTITLEMENT_PRODUCT_ID_FIELD_NUMBER = 21;
  @SuppressWarnings("serial")
  private volatile java.lang.Object entitlementProductId_ = "";
  /**
   * <code>string entitlement_product_id = 21;</code>
   * @return The entitlementProductId.
   */
  @java.lang.Override
  public java.lang.String getEntitlementProductId() {
    java.lang.Object ref = entitlementProductId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      entitlementProductId_ = s;
      return s;
    }
  }
  /**
   * <code>string entitlement_product_id = 21;</code>
   * @return The bytes for entitlementProductId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getEntitlementProductIdBytes() {
    java.lang.Object ref = entitlementProductId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      entitlementProductId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    for (int i = 0; i < investmentIds_.size(); i++) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, investmentIds_.getRaw(i));
    }
    for (int i = 0; i < dataPoints_.size(); i++) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, dataPoints_.getRaw(i));
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(startDate_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, startDate_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(endDate_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, endDate_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(currency_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, currency_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(preCurrency_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, preCurrency_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(readCache_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, readCache_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(dateFormat_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, dateFormat_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(decimalFormat_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, decimalFormat_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(extendPerformance_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 10, extendPerformance_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(postTax_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 11, postTax_);
    }
    if (useRequireId_ != false) {
      output.writeBool(12, useRequireId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(useCase_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 13, useCase_);
    }
    if (useNewCcs_ != false) {
      output.writeBool(14, useNewCcs_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(productId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 15, productId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(requestId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 16, requestId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(traceId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 17, traceId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(userId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 18, userId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(authorization_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 19, authorization_);
    }
    if (checkEntitlement_ != false) {
      output.writeBool(20, checkEntitlement_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(entitlementProductId_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 21, entitlementProductId_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    {
      int dataSize = 0;
      for (int i = 0; i < investmentIds_.size(); i++) {
        dataSize += computeStringSizeNoTag(investmentIds_.getRaw(i));
      }
      size += dataSize;
      size += 1 * getInvestmentIdsList().size();
    }
    {
      int dataSize = 0;
      for (int i = 0; i < dataPoints_.size(); i++) {
        dataSize += computeStringSizeNoTag(dataPoints_.getRaw(i));
      }
      size += dataSize;
      size += 1 * getDataPointsList().size();
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(startDate_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, startDate_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(endDate_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, endDate_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(currency_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, currency_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(preCurrency_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, preCurrency_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(readCache_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, readCache_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(dateFormat_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, dateFormat_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(decimalFormat_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, decimalFormat_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(extendPerformance_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, extendPerformance_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(postTax_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, postTax_);
    }
    if (useRequireId_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(12, useRequireId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(useCase_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(13, useCase_);
    }
    if (useNewCcs_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(14, useNewCcs_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(productId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(15, productId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(requestId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(16, requestId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(traceId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(17, traceId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(userId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(18, userId_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(authorization_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(19, authorization_);
    }
    if (checkEntitlement_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(20, checkEntitlement_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(entitlementProductId_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(21, entitlementProductId_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.morningstar.martapi.grpc.TimeSeriesRequest)) {
      return super.equals(obj);
    }
    com.morningstar.martapi.grpc.TimeSeriesRequest other = (com.morningstar.martapi.grpc.TimeSeriesRequest) obj;

    if (!getInvestmentIdsList()
        .equals(other.getInvestmentIdsList())) return false;
    if (!getDataPointsList()
        .equals(other.getDataPointsList())) return false;
    if (!getStartDate()
        .equals(other.getStartDate())) return false;
    if (!getEndDate()
        .equals(other.getEndDate())) return false;
    if (!getCurrency()
        .equals(other.getCurrency())) return false;
    if (!getPreCurrency()
        .equals(other.getPreCurrency())) return false;
    if (!getReadCache()
        .equals(other.getReadCache())) return false;
    if (!getDateFormat()
        .equals(other.getDateFormat())) return false;
    if (!getDecimalFormat()
        .equals(other.getDecimalFormat())) return false;
    if (!getExtendPerformance()
        .equals(other.getExtendPerformance())) return false;
    if (!getPostTax()
        .equals(other.getPostTax())) return false;
    if (getUseRequireId()
        != other.getUseRequireId()) return false;
    if (!getUseCase()
        .equals(other.getUseCase())) return false;
    if (getUseNewCcs()
        != other.getUseNewCcs()) return false;
    if (!getProductId()
        .equals(other.getProductId())) return false;
    if (!getRequestId()
        .equals(other.getRequestId())) return false;
    if (!getTraceId()
        .equals(other.getTraceId())) return false;
    if (!getUserId()
        .equals(other.getUserId())) return false;
    if (!getAuthorization()
        .equals(other.getAuthorization())) return false;
    if (getCheckEntitlement()
        != other.getCheckEntitlement()) return false;
    if (!getEntitlementProductId()
        .equals(other.getEntitlementProductId())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (getInvestmentIdsCount() > 0) {
      hash = (37 * hash) + INVESTMENT_IDS_FIELD_NUMBER;
      hash = (53 * hash) + getInvestmentIdsList().hashCode();
    }
    if (getDataPointsCount() > 0) {
      hash = (37 * hash) + DATA_POINTS_FIELD_NUMBER;
      hash = (53 * hash) + getDataPointsList().hashCode();
    }
    hash = (37 * hash) + START_DATE_FIELD_NUMBER;
    hash = (53 * hash) + getStartDate().hashCode();
    hash = (37 * hash) + END_DATE_FIELD_NUMBER;
    hash = (53 * hash) + getEndDate().hashCode();
    hash = (37 * hash) + CURRENCY_FIELD_NUMBER;
    hash = (53 * hash) + getCurrency().hashCode();
    hash = (37 * hash) + PRE_CURRENCY_FIELD_NUMBER;
    hash = (53 * hash) + getPreCurrency().hashCode();
    hash = (37 * hash) + READ_CACHE_FIELD_NUMBER;
    hash = (53 * hash) + getReadCache().hashCode();
    hash = (37 * hash) + DATE_FORMAT_FIELD_NUMBER;
    hash = (53 * hash) + getDateFormat().hashCode();
    hash = (37 * hash) + DECIMAL_FORMAT_FIELD_NUMBER;
    hash = (53 * hash) + getDecimalFormat().hashCode();
    hash = (37 * hash) + EXTEND_PERFORMANCE_FIELD_NUMBER;
    hash = (53 * hash) + getExtendPerformance().hashCode();
    hash = (37 * hash) + POST_TAX_FIELD_NUMBER;
    hash = (53 * hash) + getPostTax().hashCode();
    hash = (37 * hash) + USE_REQUIRE_ID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getUseRequireId());
    hash = (37 * hash) + USE_CASE_FIELD_NUMBER;
    hash = (53 * hash) + getUseCase().hashCode();
    hash = (37 * hash) + USE_NEW_CCS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getUseNewCcs());
    hash = (37 * hash) + PRODUCT_ID_FIELD_NUMBER;
    hash = (53 * hash) + getProductId().hashCode();
    hash = (37 * hash) + REQUEST_ID_FIELD_NUMBER;
    hash = (53 * hash) + getRequestId().hashCode();
    hash = (37 * hash) + TRACE_ID_FIELD_NUMBER;
    hash = (53 * hash) + getTraceId().hashCode();
    hash = (37 * hash) + USER_ID_FIELD_NUMBER;
    hash = (53 * hash) + getUserId().hashCode();
    hash = (37 * hash) + AUTHORIZATION_FIELD_NUMBER;
    hash = (53 * hash) + getAuthorization().hashCode();
    hash = (37 * hash) + CHECK_ENTITLEMENT_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getCheckEntitlement());
    hash = (37 * hash) + ENTITLEMENT_PRODUCT_ID_FIELD_NUMBER;
    hash = (53 * hash) + getEntitlementProductId().hashCode();
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.morningstar.martapi.grpc.TimeSeriesRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.morningstar.martapi.grpc.TimeSeriesRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.morningstar.martapi.grpc.TimeSeriesRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.morningstar.martapi.grpc.TimeSeriesRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.morningstar.martapi.grpc.TimeSeriesRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.morningstar.martapi.grpc.TimeSeriesRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.morningstar.martapi.grpc.TimeSeriesRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.morningstar.martapi.grpc.TimeSeriesRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.morningstar.martapi.grpc.TimeSeriesRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.morningstar.martapi.grpc.TimeSeriesRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.morningstar.martapi.grpc.TimeSeriesRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.morningstar.martapi.grpc.TimeSeriesRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.morningstar.martapi.grpc.TimeSeriesRequest prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * Request message for time series data
   * </pre>
   *
   * Protobuf type {@code timeseries.TimeSeriesRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:timeseries.TimeSeriesRequest)
      com.morningstar.martapi.grpc.TimeSeriesRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.morningstar.martapi.grpc.TimeSeriesServiceProto.internal_static_timeseries_TimeSeriesRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.morningstar.martapi.grpc.TimeSeriesServiceProto.internal_static_timeseries_TimeSeriesRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.morningstar.martapi.grpc.TimeSeriesRequest.class, com.morningstar.martapi.grpc.TimeSeriesRequest.Builder.class);
    }

    // Construct using com.morningstar.martapi.grpc.TimeSeriesRequest.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      investmentIds_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      dataPoints_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      startDate_ = "";
      endDate_ = "";
      currency_ = "";
      preCurrency_ = "";
      readCache_ = "";
      dateFormat_ = "";
      decimalFormat_ = "";
      extendPerformance_ = "";
      postTax_ = "";
      useRequireId_ = false;
      useCase_ = "";
      useNewCcs_ = false;
      productId_ = "";
      requestId_ = "";
      traceId_ = "";
      userId_ = "";
      authorization_ = "";
      checkEntitlement_ = false;
      entitlementProductId_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.morningstar.martapi.grpc.TimeSeriesServiceProto.internal_static_timeseries_TimeSeriesRequest_descriptor;
    }

    @java.lang.Override
    public com.morningstar.martapi.grpc.TimeSeriesRequest getDefaultInstanceForType() {
      return com.morningstar.martapi.grpc.TimeSeriesRequest.getDefaultInstance();
    }

    @java.lang.Override
    public com.morningstar.martapi.grpc.TimeSeriesRequest build() {
      com.morningstar.martapi.grpc.TimeSeriesRequest result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.morningstar.martapi.grpc.TimeSeriesRequest buildPartial() {
      com.morningstar.martapi.grpc.TimeSeriesRequest result = new com.morningstar.martapi.grpc.TimeSeriesRequest(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.morningstar.martapi.grpc.TimeSeriesRequest result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        investmentIds_.makeImmutable();
        result.investmentIds_ = investmentIds_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        dataPoints_.makeImmutable();
        result.dataPoints_ = dataPoints_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.startDate_ = startDate_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.endDate_ = endDate_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.currency_ = currency_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.preCurrency_ = preCurrency_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.readCache_ = readCache_;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.dateFormat_ = dateFormat_;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.decimalFormat_ = decimalFormat_;
      }
      if (((from_bitField0_ & 0x00000200) != 0)) {
        result.extendPerformance_ = extendPerformance_;
      }
      if (((from_bitField0_ & 0x00000400) != 0)) {
        result.postTax_ = postTax_;
      }
      if (((from_bitField0_ & 0x00000800) != 0)) {
        result.useRequireId_ = useRequireId_;
      }
      if (((from_bitField0_ & 0x00001000) != 0)) {
        result.useCase_ = useCase_;
      }
      if (((from_bitField0_ & 0x00002000) != 0)) {
        result.useNewCcs_ = useNewCcs_;
      }
      if (((from_bitField0_ & 0x00004000) != 0)) {
        result.productId_ = productId_;
      }
      if (((from_bitField0_ & 0x00008000) != 0)) {
        result.requestId_ = requestId_;
      }
      if (((from_bitField0_ & 0x00010000) != 0)) {
        result.traceId_ = traceId_;
      }
      if (((from_bitField0_ & 0x00020000) != 0)) {
        result.userId_ = userId_;
      }
      if (((from_bitField0_ & 0x00040000) != 0)) {
        result.authorization_ = authorization_;
      }
      if (((from_bitField0_ & 0x00080000) != 0)) {
        result.checkEntitlement_ = checkEntitlement_;
      }
      if (((from_bitField0_ & 0x00100000) != 0)) {
        result.entitlementProductId_ = entitlementProductId_;
      }
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.morningstar.martapi.grpc.TimeSeriesRequest) {
        return mergeFrom((com.morningstar.martapi.grpc.TimeSeriesRequest)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.morningstar.martapi.grpc.TimeSeriesRequest other) {
      if (other == com.morningstar.martapi.grpc.TimeSeriesRequest.getDefaultInstance()) return this;
      if (!other.investmentIds_.isEmpty()) {
        if (investmentIds_.isEmpty()) {
          investmentIds_ = other.investmentIds_;
          bitField0_ |= 0x00000001;
        } else {
          ensureInvestmentIdsIsMutable();
          investmentIds_.addAll(other.investmentIds_);
        }
        onChanged();
      }
      if (!other.dataPoints_.isEmpty()) {
        if (dataPoints_.isEmpty()) {
          dataPoints_ = other.dataPoints_;
          bitField0_ |= 0x00000002;
        } else {
          ensureDataPointsIsMutable();
          dataPoints_.addAll(other.dataPoints_);
        }
        onChanged();
      }
      if (!other.getStartDate().isEmpty()) {
        startDate_ = other.startDate_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (!other.getEndDate().isEmpty()) {
        endDate_ = other.endDate_;
        bitField0_ |= 0x00000008;
        onChanged();
      }
      if (!other.getCurrency().isEmpty()) {
        currency_ = other.currency_;
        bitField0_ |= 0x00000010;
        onChanged();
      }
      if (!other.getPreCurrency().isEmpty()) {
        preCurrency_ = other.preCurrency_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      if (!other.getReadCache().isEmpty()) {
        readCache_ = other.readCache_;
        bitField0_ |= 0x00000040;
        onChanged();
      }
      if (!other.getDateFormat().isEmpty()) {
        dateFormat_ = other.dateFormat_;
        bitField0_ |= 0x00000080;
        onChanged();
      }
      if (!other.getDecimalFormat().isEmpty()) {
        decimalFormat_ = other.decimalFormat_;
        bitField0_ |= 0x00000100;
        onChanged();
      }
      if (!other.getExtendPerformance().isEmpty()) {
        extendPerformance_ = other.extendPerformance_;
        bitField0_ |= 0x00000200;
        onChanged();
      }
      if (!other.getPostTax().isEmpty()) {
        postTax_ = other.postTax_;
        bitField0_ |= 0x00000400;
        onChanged();
      }
      if (other.getUseRequireId() != false) {
        setUseRequireId(other.getUseRequireId());
      }
      if (!other.getUseCase().isEmpty()) {
        useCase_ = other.useCase_;
        bitField0_ |= 0x00001000;
        onChanged();
      }
      if (other.getUseNewCcs() != false) {
        setUseNewCcs(other.getUseNewCcs());
      }
      if (!other.getProductId().isEmpty()) {
        productId_ = other.productId_;
        bitField0_ |= 0x00004000;
        onChanged();
      }
      if (!other.getRequestId().isEmpty()) {
        requestId_ = other.requestId_;
        bitField0_ |= 0x00008000;
        onChanged();
      }
      if (!other.getTraceId().isEmpty()) {
        traceId_ = other.traceId_;
        bitField0_ |= 0x00010000;
        onChanged();
      }
      if (!other.getUserId().isEmpty()) {
        userId_ = other.userId_;
        bitField0_ |= 0x00020000;
        onChanged();
      }
      if (!other.getAuthorization().isEmpty()) {
        authorization_ = other.authorization_;
        bitField0_ |= 0x00040000;
        onChanged();
      }
      if (other.getCheckEntitlement() != false) {
        setCheckEntitlement(other.getCheckEntitlement());
      }
      if (!other.getEntitlementProductId().isEmpty()) {
        entitlementProductId_ = other.entitlementProductId_;
        bitField0_ |= 0x00100000;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              java.lang.String s = input.readStringRequireUtf8();
              ensureInvestmentIdsIsMutable();
              investmentIds_.add(s);
              break;
            } // case 10
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();
              ensureDataPointsIsMutable();
              dataPoints_.add(s);
              break;
            } // case 18
            case 26: {
              startDate_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              endDate_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 42: {
              currency_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000010;
              break;
            } // case 42
            case 50: {
              preCurrency_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            case 58: {
              readCache_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000040;
              break;
            } // case 58
            case 66: {
              dateFormat_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000080;
              break;
            } // case 66
            case 74: {
              decimalFormat_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000100;
              break;
            } // case 74
            case 82: {
              extendPerformance_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000200;
              break;
            } // case 82
            case 90: {
              postTax_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000400;
              break;
            } // case 90
            case 96: {
              useRequireId_ = input.readBool();
              bitField0_ |= 0x00000800;
              break;
            } // case 96
            case 106: {
              useCase_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00001000;
              break;
            } // case 106
            case 112: {
              useNewCcs_ = input.readBool();
              bitField0_ |= 0x00002000;
              break;
            } // case 112
            case 122: {
              productId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00004000;
              break;
            } // case 122
            case 130: {
              requestId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00008000;
              break;
            } // case 130
            case 138: {
              traceId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00010000;
              break;
            } // case 138
            case 146: {
              userId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00020000;
              break;
            } // case 146
            case 154: {
              authorization_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00040000;
              break;
            } // case 154
            case 160: {
              checkEntitlement_ = input.readBool();
              bitField0_ |= 0x00080000;
              break;
            } // case 160
            case 170: {
              entitlementProductId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00100000;
              break;
            } // case 170
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private com.google.protobuf.LazyStringArrayList investmentIds_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    private void ensureInvestmentIdsIsMutable() {
      if (!investmentIds_.isModifiable()) {
        investmentIds_ = new com.google.protobuf.LazyStringArrayList(investmentIds_);
      }
      bitField0_ |= 0x00000001;
    }
    /**
     * <pre>
     * Investment IDs (comma-separated in REST, repeated here)
     * </pre>
     *
     * <code>repeated string investment_ids = 1;</code>
     * @return A list containing the investmentIds.
     */
    public com.google.protobuf.ProtocolStringList
        getInvestmentIdsList() {
      investmentIds_.makeImmutable();
      return investmentIds_;
    }
    /**
     * <pre>
     * Investment IDs (comma-separated in REST, repeated here)
     * </pre>
     *
     * <code>repeated string investment_ids = 1;</code>
     * @return The count of investmentIds.
     */
    public int getInvestmentIdsCount() {
      return investmentIds_.size();
    }
    /**
     * <pre>
     * Investment IDs (comma-separated in REST, repeated here)
     * </pre>
     *
     * <code>repeated string investment_ids = 1;</code>
     * @param index The index of the element to return.
     * @return The investmentIds at the given index.
     */
    public java.lang.String getInvestmentIds(int index) {
      return investmentIds_.get(index);
    }
    /**
     * <pre>
     * Investment IDs (comma-separated in REST, repeated here)
     * </pre>
     *
     * <code>repeated string investment_ids = 1;</code>
     * @param index The index of the value to return.
     * @return The bytes of the investmentIds at the given index.
     */
    public com.google.protobuf.ByteString
        getInvestmentIdsBytes(int index) {
      return investmentIds_.getByteString(index);
    }
    /**
     * <pre>
     * Investment IDs (comma-separated in REST, repeated here)
     * </pre>
     *
     * <code>repeated string investment_ids = 1;</code>
     * @param index The index to set the value at.
     * @param value The investmentIds to set.
     * @return This builder for chaining.
     */
    public Builder setInvestmentIds(
        int index, java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ensureInvestmentIdsIsMutable();
      investmentIds_.set(index, value);
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Investment IDs (comma-separated in REST, repeated here)
     * </pre>
     *
     * <code>repeated string investment_ids = 1;</code>
     * @param value The investmentIds to add.
     * @return This builder for chaining.
     */
    public Builder addInvestmentIds(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ensureInvestmentIdsIsMutable();
      investmentIds_.add(value);
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Investment IDs (comma-separated in REST, repeated here)
     * </pre>
     *
     * <code>repeated string investment_ids = 1;</code>
     * @param values The investmentIds to add.
     * @return This builder for chaining.
     */
    public Builder addAllInvestmentIds(
        java.lang.Iterable<java.lang.String> values) {
      ensureInvestmentIdsIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, investmentIds_);
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Investment IDs (comma-separated in REST, repeated here)
     * </pre>
     *
     * <code>repeated string investment_ids = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearInvestmentIds() {
      investmentIds_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
      bitField0_ = (bitField0_ & ~0x00000001);;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Investment IDs (comma-separated in REST, repeated here)
     * </pre>
     *
     * <code>repeated string investment_ids = 1;</code>
     * @param value The bytes of the investmentIds to add.
     * @return This builder for chaining.
     */
    public Builder addInvestmentIdsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      ensureInvestmentIdsIsMutable();
      investmentIds_.add(value);
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private com.google.protobuf.LazyStringArrayList dataPoints_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    private void ensureDataPointsIsMutable() {
      if (!dataPoints_.isModifiable()) {
        dataPoints_ = new com.google.protobuf.LazyStringArrayList(dataPoints_);
      }
      bitField0_ |= 0x00000002;
    }
    /**
     * <pre>
     * Data points (comma-separated in REST, repeated here)
     * </pre>
     *
     * <code>repeated string data_points = 2;</code>
     * @return A list containing the dataPoints.
     */
    public com.google.protobuf.ProtocolStringList
        getDataPointsList() {
      dataPoints_.makeImmutable();
      return dataPoints_;
    }
    /**
     * <pre>
     * Data points (comma-separated in REST, repeated here)
     * </pre>
     *
     * <code>repeated string data_points = 2;</code>
     * @return The count of dataPoints.
     */
    public int getDataPointsCount() {
      return dataPoints_.size();
    }
    /**
     * <pre>
     * Data points (comma-separated in REST, repeated here)
     * </pre>
     *
     * <code>repeated string data_points = 2;</code>
     * @param index The index of the element to return.
     * @return The dataPoints at the given index.
     */
    public java.lang.String getDataPoints(int index) {
      return dataPoints_.get(index);
    }
    /**
     * <pre>
     * Data points (comma-separated in REST, repeated here)
     * </pre>
     *
     * <code>repeated string data_points = 2;</code>
     * @param index The index of the value to return.
     * @return The bytes of the dataPoints at the given index.
     */
    public com.google.protobuf.ByteString
        getDataPointsBytes(int index) {
      return dataPoints_.getByteString(index);
    }
    /**
     * <pre>
     * Data points (comma-separated in REST, repeated here)
     * </pre>
     *
     * <code>repeated string data_points = 2;</code>
     * @param index The index to set the value at.
     * @param value The dataPoints to set.
     * @return This builder for chaining.
     */
    public Builder setDataPoints(
        int index, java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ensureDataPointsIsMutable();
      dataPoints_.set(index, value);
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Data points (comma-separated in REST, repeated here)
     * </pre>
     *
     * <code>repeated string data_points = 2;</code>
     * @param value The dataPoints to add.
     * @return This builder for chaining.
     */
    public Builder addDataPoints(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      ensureDataPointsIsMutable();
      dataPoints_.add(value);
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Data points (comma-separated in REST, repeated here)
     * </pre>
     *
     * <code>repeated string data_points = 2;</code>
     * @param values The dataPoints to add.
     * @return This builder for chaining.
     */
    public Builder addAllDataPoints(
        java.lang.Iterable<java.lang.String> values) {
      ensureDataPointsIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, dataPoints_);
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Data points (comma-separated in REST, repeated here)
     * </pre>
     *
     * <code>repeated string data_points = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearDataPoints() {
      dataPoints_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
      bitField0_ = (bitField0_ & ~0x00000002);;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Data points (comma-separated in REST, repeated here)
     * </pre>
     *
     * <code>repeated string data_points = 2;</code>
     * @param value The bytes of the dataPoints to add.
     * @return This builder for chaining.
     */
    public Builder addDataPointsBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      ensureDataPointsIsMutable();
      dataPoints_.add(value);
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private java.lang.Object startDate_ = "";
    /**
     * <pre>
     * Date range
     * </pre>
     *
     * <code>string start_date = 3;</code>
     * @return The startDate.
     */
    public java.lang.String getStartDate() {
      java.lang.Object ref = startDate_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        startDate_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * Date range
     * </pre>
     *
     * <code>string start_date = 3;</code>
     * @return The bytes for startDate.
     */
    public com.google.protobuf.ByteString
        getStartDateBytes() {
      java.lang.Object ref = startDate_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        startDate_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * Date range
     * </pre>
     *
     * <code>string start_date = 3;</code>
     * @param value The startDate to set.
     * @return This builder for chaining.
     */
    public Builder setStartDate(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      startDate_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Date range
     * </pre>
     *
     * <code>string start_date = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearStartDate() {
      startDate_ = getDefaultInstance().getStartDate();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Date range
     * </pre>
     *
     * <code>string start_date = 3;</code>
     * @param value The bytes for startDate to set.
     * @return This builder for chaining.
     */
    public Builder setStartDateBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      startDate_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private java.lang.Object endDate_ = "";
    /**
     * <code>string end_date = 4;</code>
     * @return The endDate.
     */
    public java.lang.String getEndDate() {
      java.lang.Object ref = endDate_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        endDate_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string end_date = 4;</code>
     * @return The bytes for endDate.
     */
    public com.google.protobuf.ByteString
        getEndDateBytes() {
      java.lang.Object ref = endDate_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        endDate_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string end_date = 4;</code>
     * @param value The endDate to set.
     * @return This builder for chaining.
     */
    public Builder setEndDate(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      endDate_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>string end_date = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearEndDate() {
      endDate_ = getDefaultInstance().getEndDate();
      bitField0_ = (bitField0_ & ~0x00000008);
      onChanged();
      return this;
    }
    /**
     * <code>string end_date = 4;</code>
     * @param value The bytes for endDate to set.
     * @return This builder for chaining.
     */
    public Builder setEndDateBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      endDate_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }

    private java.lang.Object currency_ = "";
    /**
     * <pre>
     * Currency settings
     * </pre>
     *
     * <code>string currency = 5;</code>
     * @return The currency.
     */
    public java.lang.String getCurrency() {
      java.lang.Object ref = currency_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        currency_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * Currency settings
     * </pre>
     *
     * <code>string currency = 5;</code>
     * @return The bytes for currency.
     */
    public com.google.protobuf.ByteString
        getCurrencyBytes() {
      java.lang.Object ref = currency_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        currency_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * Currency settings
     * </pre>
     *
     * <code>string currency = 5;</code>
     * @param value The currency to set.
     * @return This builder for chaining.
     */
    public Builder setCurrency(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      currency_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Currency settings
     * </pre>
     *
     * <code>string currency = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearCurrency() {
      currency_ = getDefaultInstance().getCurrency();
      bitField0_ = (bitField0_ & ~0x00000010);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Currency settings
     * </pre>
     *
     * <code>string currency = 5;</code>
     * @param value The bytes for currency to set.
     * @return This builder for chaining.
     */
    public Builder setCurrencyBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      currency_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }

    private java.lang.Object preCurrency_ = "";
    /**
     * <code>string pre_currency = 6;</code>
     * @return The preCurrency.
     */
    public java.lang.String getPreCurrency() {
      java.lang.Object ref = preCurrency_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        preCurrency_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string pre_currency = 6;</code>
     * @return The bytes for preCurrency.
     */
    public com.google.protobuf.ByteString
        getPreCurrencyBytes() {
      java.lang.Object ref = preCurrency_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        preCurrency_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string pre_currency = 6;</code>
     * @param value The preCurrency to set.
     * @return This builder for chaining.
     */
    public Builder setPreCurrency(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      preCurrency_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>string pre_currency = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearPreCurrency() {
      preCurrency_ = getDefaultInstance().getPreCurrency();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <code>string pre_currency = 6;</code>
     * @param value The bytes for preCurrency to set.
     * @return This builder for chaining.
     */
    public Builder setPreCurrencyBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      preCurrency_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    private java.lang.Object readCache_ = "";
    /**
     * <pre>
     * Cache and format settings
     * </pre>
     *
     * <code>string read_cache = 7;</code>
     * @return The readCache.
     */
    public java.lang.String getReadCache() {
      java.lang.Object ref = readCache_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        readCache_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * Cache and format settings
     * </pre>
     *
     * <code>string read_cache = 7;</code>
     * @return The bytes for readCache.
     */
    public com.google.protobuf.ByteString
        getReadCacheBytes() {
      java.lang.Object ref = readCache_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        readCache_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * Cache and format settings
     * </pre>
     *
     * <code>string read_cache = 7;</code>
     * @param value The readCache to set.
     * @return This builder for chaining.
     */
    public Builder setReadCache(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      readCache_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Cache and format settings
     * </pre>
     *
     * <code>string read_cache = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearReadCache() {
      readCache_ = getDefaultInstance().getReadCache();
      bitField0_ = (bitField0_ & ~0x00000040);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Cache and format settings
     * </pre>
     *
     * <code>string read_cache = 7;</code>
     * @param value The bytes for readCache to set.
     * @return This builder for chaining.
     */
    public Builder setReadCacheBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      readCache_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }

    private java.lang.Object dateFormat_ = "";
    /**
     * <code>string date_format = 8;</code>
     * @return The dateFormat.
     */
    public java.lang.String getDateFormat() {
      java.lang.Object ref = dateFormat_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        dateFormat_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string date_format = 8;</code>
     * @return The bytes for dateFormat.
     */
    public com.google.protobuf.ByteString
        getDateFormatBytes() {
      java.lang.Object ref = dateFormat_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        dateFormat_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string date_format = 8;</code>
     * @param value The dateFormat to set.
     * @return This builder for chaining.
     */
    public Builder setDateFormat(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      dateFormat_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <code>string date_format = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearDateFormat() {
      dateFormat_ = getDefaultInstance().getDateFormat();
      bitField0_ = (bitField0_ & ~0x00000080);
      onChanged();
      return this;
    }
    /**
     * <code>string date_format = 8;</code>
     * @param value The bytes for dateFormat to set.
     * @return This builder for chaining.
     */
    public Builder setDateFormatBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      dateFormat_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }

    private java.lang.Object decimalFormat_ = "";
    /**
     * <code>string decimal_format = 9;</code>
     * @return The decimalFormat.
     */
    public java.lang.String getDecimalFormat() {
      java.lang.Object ref = decimalFormat_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        decimalFormat_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string decimal_format = 9;</code>
     * @return The bytes for decimalFormat.
     */
    public com.google.protobuf.ByteString
        getDecimalFormatBytes() {
      java.lang.Object ref = decimalFormat_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        decimalFormat_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string decimal_format = 9;</code>
     * @param value The decimalFormat to set.
     * @return This builder for chaining.
     */
    public Builder setDecimalFormat(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      decimalFormat_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <code>string decimal_format = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearDecimalFormat() {
      decimalFormat_ = getDefaultInstance().getDecimalFormat();
      bitField0_ = (bitField0_ & ~0x00000100);
      onChanged();
      return this;
    }
    /**
     * <code>string decimal_format = 9;</code>
     * @param value The bytes for decimalFormat to set.
     * @return This builder for chaining.
     */
    public Builder setDecimalFormatBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      decimalFormat_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }

    private java.lang.Object extendPerformance_ = "";
    /**
     * <pre>
     * Performance and tax settings
     * </pre>
     *
     * <code>string extend_performance = 10;</code>
     * @return The extendPerformance.
     */
    public java.lang.String getExtendPerformance() {
      java.lang.Object ref = extendPerformance_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        extendPerformance_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * Performance and tax settings
     * </pre>
     *
     * <code>string extend_performance = 10;</code>
     * @return The bytes for extendPerformance.
     */
    public com.google.protobuf.ByteString
        getExtendPerformanceBytes() {
      java.lang.Object ref = extendPerformance_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        extendPerformance_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * Performance and tax settings
     * </pre>
     *
     * <code>string extend_performance = 10;</code>
     * @param value The extendPerformance to set.
     * @return This builder for chaining.
     */
    public Builder setExtendPerformance(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      extendPerformance_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Performance and tax settings
     * </pre>
     *
     * <code>string extend_performance = 10;</code>
     * @return This builder for chaining.
     */
    public Builder clearExtendPerformance() {
      extendPerformance_ = getDefaultInstance().getExtendPerformance();
      bitField0_ = (bitField0_ & ~0x00000200);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Performance and tax settings
     * </pre>
     *
     * <code>string extend_performance = 10;</code>
     * @param value The bytes for extendPerformance to set.
     * @return This builder for chaining.
     */
    public Builder setExtendPerformanceBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      extendPerformance_ = value;
      bitField0_ |= 0x00000200;
      onChanged();
      return this;
    }

    private java.lang.Object postTax_ = "";
    /**
     * <code>string post_tax = 11;</code>
     * @return The postTax.
     */
    public java.lang.String getPostTax() {
      java.lang.Object ref = postTax_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        postTax_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string post_tax = 11;</code>
     * @return The bytes for postTax.
     */
    public com.google.protobuf.ByteString
        getPostTaxBytes() {
      java.lang.Object ref = postTax_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        postTax_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string post_tax = 11;</code>
     * @param value The postTax to set.
     * @return This builder for chaining.
     */
    public Builder setPostTax(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      postTax_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }
    /**
     * <code>string post_tax = 11;</code>
     * @return This builder for chaining.
     */
    public Builder clearPostTax() {
      postTax_ = getDefaultInstance().getPostTax();
      bitField0_ = (bitField0_ & ~0x00000400);
      onChanged();
      return this;
    }
    /**
     * <code>string post_tax = 11;</code>
     * @param value The bytes for postTax to set.
     * @return This builder for chaining.
     */
    public Builder setPostTaxBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      postTax_ = value;
      bitField0_ |= 0x00000400;
      onChanged();
      return this;
    }

    private boolean useRequireId_ ;
    /**
     * <pre>
     * Feature flags
     * </pre>
     *
     * <code>bool use_require_id = 12;</code>
     * @return The useRequireId.
     */
    @java.lang.Override
    public boolean getUseRequireId() {
      return useRequireId_;
    }
    /**
     * <pre>
     * Feature flags
     * </pre>
     *
     * <code>bool use_require_id = 12;</code>
     * @param value The useRequireId to set.
     * @return This builder for chaining.
     */
    public Builder setUseRequireId(boolean value) {

      useRequireId_ = value;
      bitField0_ |= 0x00000800;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Feature flags
     * </pre>
     *
     * <code>bool use_require_id = 12;</code>
     * @return This builder for chaining.
     */
    public Builder clearUseRequireId() {
      bitField0_ = (bitField0_ & ~0x00000800);
      useRequireId_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object useCase_ = "";
    /**
     * <code>string use_case = 13;</code>
     * @return The useCase.
     */
    public java.lang.String getUseCase() {
      java.lang.Object ref = useCase_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        useCase_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string use_case = 13;</code>
     * @return The bytes for useCase.
     */
    public com.google.protobuf.ByteString
        getUseCaseBytes() {
      java.lang.Object ref = useCase_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        useCase_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string use_case = 13;</code>
     * @param value The useCase to set.
     * @return This builder for chaining.
     */
    public Builder setUseCase(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      useCase_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }
    /**
     * <code>string use_case = 13;</code>
     * @return This builder for chaining.
     */
    public Builder clearUseCase() {
      useCase_ = getDefaultInstance().getUseCase();
      bitField0_ = (bitField0_ & ~0x00001000);
      onChanged();
      return this;
    }
    /**
     * <code>string use_case = 13;</code>
     * @param value The bytes for useCase to set.
     * @return This builder for chaining.
     */
    public Builder setUseCaseBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      useCase_ = value;
      bitField0_ |= 0x00001000;
      onChanged();
      return this;
    }

    private boolean useNewCcs_ ;
    /**
     * <code>bool use_new_ccs = 14;</code>
     * @return The useNewCcs.
     */
    @java.lang.Override
    public boolean getUseNewCcs() {
      return useNewCcs_;
    }
    /**
     * <code>bool use_new_ccs = 14;</code>
     * @param value The useNewCcs to set.
     * @return This builder for chaining.
     */
    public Builder setUseNewCcs(boolean value) {

      useNewCcs_ = value;
      bitField0_ |= 0x00002000;
      onChanged();
      return this;
    }
    /**
     * <code>bool use_new_ccs = 14;</code>
     * @return This builder for chaining.
     */
    public Builder clearUseNewCcs() {
      bitField0_ = (bitField0_ & ~0x00002000);
      useNewCcs_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object productId_ = "";
    /**
     * <pre>
     * Headers
     * </pre>
     *
     * <code>string product_id = 15;</code>
     * @return The productId.
     */
    public java.lang.String getProductId() {
      java.lang.Object ref = productId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        productId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * Headers
     * </pre>
     *
     * <code>string product_id = 15;</code>
     * @return The bytes for productId.
     */
    public com.google.protobuf.ByteString
        getProductIdBytes() {
      java.lang.Object ref = productId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        productId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * Headers
     * </pre>
     *
     * <code>string product_id = 15;</code>
     * @param value The productId to set.
     * @return This builder for chaining.
     */
    public Builder setProductId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      productId_ = value;
      bitField0_ |= 0x00004000;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Headers
     * </pre>
     *
     * <code>string product_id = 15;</code>
     * @return This builder for chaining.
     */
    public Builder clearProductId() {
      productId_ = getDefaultInstance().getProductId();
      bitField0_ = (bitField0_ & ~0x00004000);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * Headers
     * </pre>
     *
     * <code>string product_id = 15;</code>
     * @param value The bytes for productId to set.
     * @return This builder for chaining.
     */
    public Builder setProductIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      productId_ = value;
      bitField0_ |= 0x00004000;
      onChanged();
      return this;
    }

    private java.lang.Object requestId_ = "";
    /**
     * <code>string request_id = 16;</code>
     * @return The requestId.
     */
    public java.lang.String getRequestId() {
      java.lang.Object ref = requestId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        requestId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string request_id = 16;</code>
     * @return The bytes for requestId.
     */
    public com.google.protobuf.ByteString
        getRequestIdBytes() {
      java.lang.Object ref = requestId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        requestId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string request_id = 16;</code>
     * @param value The requestId to set.
     * @return This builder for chaining.
     */
    public Builder setRequestId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      requestId_ = value;
      bitField0_ |= 0x00008000;
      onChanged();
      return this;
    }
    /**
     * <code>string request_id = 16;</code>
     * @return This builder for chaining.
     */
    public Builder clearRequestId() {
      requestId_ = getDefaultInstance().getRequestId();
      bitField0_ = (bitField0_ & ~0x00008000);
      onChanged();
      return this;
    }
    /**
     * <code>string request_id = 16;</code>
     * @param value The bytes for requestId to set.
     * @return This builder for chaining.
     */
    public Builder setRequestIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      requestId_ = value;
      bitField0_ |= 0x00008000;
      onChanged();
      return this;
    }

    private java.lang.Object traceId_ = "";
    /**
     * <code>string trace_id = 17;</code>
     * @return The traceId.
     */
    public java.lang.String getTraceId() {
      java.lang.Object ref = traceId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        traceId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string trace_id = 17;</code>
     * @return The bytes for traceId.
     */
    public com.google.protobuf.ByteString
        getTraceIdBytes() {
      java.lang.Object ref = traceId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        traceId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string trace_id = 17;</code>
     * @param value The traceId to set.
     * @return This builder for chaining.
     */
    public Builder setTraceId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      traceId_ = value;
      bitField0_ |= 0x00010000;
      onChanged();
      return this;
    }
    /**
     * <code>string trace_id = 17;</code>
     * @return This builder for chaining.
     */
    public Builder clearTraceId() {
      traceId_ = getDefaultInstance().getTraceId();
      bitField0_ = (bitField0_ & ~0x00010000);
      onChanged();
      return this;
    }
    /**
     * <code>string trace_id = 17;</code>
     * @param value The bytes for traceId to set.
     * @return This builder for chaining.
     */
    public Builder setTraceIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      traceId_ = value;
      bitField0_ |= 0x00010000;
      onChanged();
      return this;
    }

    private java.lang.Object userId_ = "";
    /**
     * <code>string user_id = 18;</code>
     * @return The userId.
     */
    public java.lang.String getUserId() {
      java.lang.Object ref = userId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        userId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string user_id = 18;</code>
     * @return The bytes for userId.
     */
    public com.google.protobuf.ByteString
        getUserIdBytes() {
      java.lang.Object ref = userId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        userId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string user_id = 18;</code>
     * @param value The userId to set.
     * @return This builder for chaining.
     */
    public Builder setUserId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      userId_ = value;
      bitField0_ |= 0x00020000;
      onChanged();
      return this;
    }
    /**
     * <code>string user_id = 18;</code>
     * @return This builder for chaining.
     */
    public Builder clearUserId() {
      userId_ = getDefaultInstance().getUserId();
      bitField0_ = (bitField0_ & ~0x00020000);
      onChanged();
      return this;
    }
    /**
     * <code>string user_id = 18;</code>
     * @param value The bytes for userId to set.
     * @return This builder for chaining.
     */
    public Builder setUserIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      userId_ = value;
      bitField0_ |= 0x00020000;
      onChanged();
      return this;
    }

    private java.lang.Object authorization_ = "";
    /**
     * <code>string authorization = 19;</code>
     * @return The authorization.
     */
    public java.lang.String getAuthorization() {
      java.lang.Object ref = authorization_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        authorization_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string authorization = 19;</code>
     * @return The bytes for authorization.
     */
    public com.google.protobuf.ByteString
        getAuthorizationBytes() {
      java.lang.Object ref = authorization_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        authorization_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string authorization = 19;</code>
     * @param value The authorization to set.
     * @return This builder for chaining.
     */
    public Builder setAuthorization(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      authorization_ = value;
      bitField0_ |= 0x00040000;
      onChanged();
      return this;
    }
    /**
     * <code>string authorization = 19;</code>
     * @return This builder for chaining.
     */
    public Builder clearAuthorization() {
      authorization_ = getDefaultInstance().getAuthorization();
      bitField0_ = (bitField0_ & ~0x00040000);
      onChanged();
      return this;
    }
    /**
     * <code>string authorization = 19;</code>
     * @param value The bytes for authorization to set.
     * @return This builder for chaining.
     */
    public Builder setAuthorizationBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      authorization_ = value;
      bitField0_ |= 0x00040000;
      onChanged();
      return this;
    }

    private boolean checkEntitlement_ ;
    /**
     * <code>bool check_entitlement = 20;</code>
     * @return The checkEntitlement.
     */
    @java.lang.Override
    public boolean getCheckEntitlement() {
      return checkEntitlement_;
    }
    /**
     * <code>bool check_entitlement = 20;</code>
     * @param value The checkEntitlement to set.
     * @return This builder for chaining.
     */
    public Builder setCheckEntitlement(boolean value) {

      checkEntitlement_ = value;
      bitField0_ |= 0x00080000;
      onChanged();
      return this;
    }
    /**
     * <code>bool check_entitlement = 20;</code>
     * @return This builder for chaining.
     */
    public Builder clearCheckEntitlement() {
      bitField0_ = (bitField0_ & ~0x00080000);
      checkEntitlement_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object entitlementProductId_ = "";
    /**
     * <code>string entitlement_product_id = 21;</code>
     * @return The entitlementProductId.
     */
    public java.lang.String getEntitlementProductId() {
      java.lang.Object ref = entitlementProductId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        entitlementProductId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string entitlement_product_id = 21;</code>
     * @return The bytes for entitlementProductId.
     */
    public com.google.protobuf.ByteString
        getEntitlementProductIdBytes() {
      java.lang.Object ref = entitlementProductId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        entitlementProductId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string entitlement_product_id = 21;</code>
     * @param value The entitlementProductId to set.
     * @return This builder for chaining.
     */
    public Builder setEntitlementProductId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      entitlementProductId_ = value;
      bitField0_ |= 0x00100000;
      onChanged();
      return this;
    }
    /**
     * <code>string entitlement_product_id = 21;</code>
     * @return This builder for chaining.
     */
    public Builder clearEntitlementProductId() {
      entitlementProductId_ = getDefaultInstance().getEntitlementProductId();
      bitField0_ = (bitField0_ & ~0x00100000);
      onChanged();
      return this;
    }
    /**
     * <code>string entitlement_product_id = 21;</code>
     * @param value The bytes for entitlementProductId to set.
     * @return This builder for chaining.
     */
    public Builder setEntitlementProductIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      entitlementProductId_ = value;
      bitField0_ |= 0x00100000;
      onChanged();
      return this;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:timeseries.TimeSeriesRequest)
  }

  // @@protoc_insertion_point(class_scope:timeseries.TimeSeriesRequest)
  private static final com.morningstar.martapi.grpc.TimeSeriesRequest DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.morningstar.martapi.grpc.TimeSeriesRequest();
  }

  public static com.morningstar.martapi.grpc.TimeSeriesRequest getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<TimeSeriesRequest>
      PARSER = new com.google.protobuf.AbstractParser<TimeSeriesRequest>() {
    @java.lang.Override
    public TimeSeriesRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<TimeSeriesRequest> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<TimeSeriesRequest> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.morningstar.martapi.grpc.TimeSeriesRequest getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

