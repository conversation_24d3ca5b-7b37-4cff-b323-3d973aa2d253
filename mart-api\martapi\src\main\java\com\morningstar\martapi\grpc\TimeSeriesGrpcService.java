package com.morningstar.martapi.grpc;

import com.morningstar.martapi.exception.TsCacheProtobufValidationException;
import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.timeseries.entity.TSResponse;
import com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf;
import com.morningstar.martgateway.infrastructures.log.LogAttribute;
import com.morningstar.martgateway.infrastructures.log.LogEntity;
import com.morningstar.martgateway.infrastructures.log.LogEntry;
import com.morningstar.martgateway.infrastructures.log.LogHelper;
import com.morningstar.martgateway.interfaces.MartGateway;
import com.morningstar.martgateway.util.JsonUtils;
import com.morningstar.martgateway.util.JwtUtil;
import com.morningstar.martcommon.entity.result.request.MartRequest;
import io.grpc.stub.StreamObserver;
import net.devh.boot.grpc.server.service.GrpcService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.morningstar.martapi.config.Constant.LOG_REQUEST_PAYLOAD_LIMIT_MILLISECONDS;
import static com.morningstar.martapi.config.Constant.RESPONSE;
import static com.morningstar.martapi.config.Constant.RESPONSE_ERROR;
import static com.morningstar.martapi.config.Constant.SUCCESSFUL_RESPONSE;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.*;

/**
 * gRPC服务实现 - 时间序列数据检索
 */
@GrpcService
public class TimeSeriesGrpcService extends TimeSeriesServiceGrpc.TimeSeriesServiceImplBase {

    private static final Logger log = LoggerFactory.getLogger(TimeSeriesGrpcService.class);
    private static final String REQUEST_TYPE_VALUE = "tscache-grpc";

    private final MartGateway<TSResponse, MartRequest> tsOldRspGateway;
    private final RequestValidationHandler<HeadersAndParams, MartRequest> validator;

    @Autowired
    public TimeSeriesGrpcService(
            @Qualifier("tsOldRspGateway") MartGateway<TSResponse, MartRequest> tsOldRspGateway,
            @Qualifier("timeSeriesApiValidator") RequestValidationHandler<HeadersAndParams, MartRequest> validator) {
        this.tsOldRspGateway = tsOldRspGateway;
        this.validator = validator;
    }

    @Override
    public void getTimeSeriesData(TimeSeriesRequest request, 
                                 StreamObserver<TsCacheDataForProtoBuf.TimeSeriesDatas> responseObserver) {
        
        String reqId = StringUtils.isNotEmpty(request.getRequestId()) ? 
                      request.getRequestId() : UUID.randomUUID().toString();
        long startTime = System.currentTimeMillis();

        try {
            log.info("Processing gRPC request for investment IDs: {}", request.getInvestmentIdsList());

            // 构建MartRequest
            MartRequest martRequest = buildMartRequest(request, reqId);
            
            // 构建HeadersAndParams用于验证
            HeadersAndParams headersAndParams = HeadersAndParams.builder()
                    .authorizationToken(request.getAuthorization())
                    .productId(request.getProductId())
                    .requestId(reqId)
                    .build();

            // 验证请求
            validateRequest(martRequest, headersAndParams);

            // 调用业务逻辑
            tsOldRspGateway.asyncRetrieveSecurities(martRequest)
                    .map(TSResponse::toProtobuf)
                    .doOnNext(result -> {
                        // 记录成功日志
                        logSuccess(martRequest, reqId, startTime);
                        
                        // 返回结果
                        responseObserver.onNext(result);
                        responseObserver.onCompleted();
                    })
                    .doOnError(error -> {
                        // 记录错误日志
                        logError(martRequest, reqId, startTime, error);
                        
                        // 返回错误
                        responseObserver.onError(io.grpc.Status.INTERNAL
                                .withDescription("Failed to retrieve time series data: " + error.getMessage())
                                .withCause(error)
                                .asRuntimeException());
                    })
                    .subscribe();

        } catch (ValidationException e) {
            log.error("Validation failed for gRPC request: {}", e.getMessage());
            responseObserver.onError(io.grpc.Status.INVALID_ARGUMENT
                    .withDescription("Validation failed: " + e.getMessage())
                    .withCause(e)
                    .asRuntimeException());
        } catch (Exception e) {
            log.error("Unexpected error processing gRPC request", e);
            responseObserver.onError(io.grpc.Status.INTERNAL
                    .withDescription("Internal server error: " + e.getMessage())
                    .withCause(e)
                    .asRuntimeException());
        }
    }

    private MartRequest buildMartRequest(TimeSeriesRequest request, String reqId) {
        String userId = getUserIdFromToken(request.getUserId(), request.getAuthorization());
        String configId = getConfigIdFromToken(request.getAuthorization());

        return MartRequest.builder()
                .currency(request.getCurrency())
                .dps(new ArrayList<>(request.getDataPointsList()))
                .ids(new ArrayList<>(request.getInvestmentIdsList()))
                .startDate(request.getStartDate())
                .endDate(request.getEndDate())
                .preCurrency(request.getPreCurrency())
                .readCache(request.getReadCache())
                .productId(request.getProductId())
                .entitlementProductId(request.getEntitlementProductId())
                .requestId(reqId)
                .userId(userId)
                .dateFormat(request.getDateFormat())
                .decimalFormat(request.getDecimalFormat())
                .extendedPerformance(request.getExtendPerformance())
                .postTax(request.getPostTax())
                .useRequireId(request.getUseRequireId())
                .checkEntitlement(request.getCheckEntitlement())
                .useCase(request.getUseCase())
                .useNewCCS(request.getUseNewCcs())
                .configId(configId)
                .build();
    }

    private void validateRequest(MartRequest martRequest, HeadersAndParams headersAndParams) {
        try {
            validator.validateHeadersAndParams(headersAndParams);
            validator.validateRequestBody(martRequest);
        } catch (ValidationException e) {
            throw new TsCacheProtobufValidationException(e.getStatus());
        }
    }

    private static String getUserIdFromToken(String headerUserId, String token) {
        return StringUtils.defaultIfEmpty(JwtUtil.getFieldValue(token, "https://morningstar.com/mstar_id"), headerUserId);
    }

    private static String getConfigIdFromToken(String token) {
        return JwtUtil.getFieldValue(token, "https://morningstar.com/config_id");
    }

    private void logSuccess(MartRequest martRequest, String reqId, long startTime) {
        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), reqId);
        long executionTime = System.currentTimeMillis() - startTime;
        
        List<LogEntity> logEntities = Stream.of(
                new LogEntity(EVENT_TYPE, RESPONSE),
                new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
                new LogEntity(PRODUCT_ID, martRequest.getProductId()),
                new LogEntity(USER_ID, martRequest.getUserId()),
                new LogEntity(EXECUTE_TIME, executionTime)
        ).collect(Collectors.toCollection(ArrayList::new));
        
        addRequestPayload(martRequest, executionTime, logEntities);
        LogEntry.info(logEntities.toArray(LogEntity[]::new));
    }

    private void logError(MartRequest martRequest, String reqId, long startTime, Throwable error) {
        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), reqId);
        LogEntry.error(
                new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                new LogEntity(EVENT_DESCRIPTION, error),
                new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
                new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(martRequest)),
                new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime),
                new LogEntity(EXCEPTION_TYPE, error.getClass()),
                new LogEntity(PRODUCT_ID, martRequest.getProductId()),
                new LogEntity(USER_ID, martRequest.getUserId())
        );
    }

    private void addRequestPayload(MartRequest martRequest, long executionTime, List<LogEntity> logEntities) {
        if (executionTime > LOG_REQUEST_PAYLOAD_LIMIT_MILLISECONDS) {
            logEntities.add(new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(martRequest)));
        }
    }
}
