package com.morningstar.martapi.config;

import java.util.Set;

public final class Constant {
    public static final String X_API_PRODUCT_ID = "X-Api-ProductId";
    public static final String X_API_REQUEST_ID = "X-Api-RequestId";
    public static final String RESPONSE = "response";
    public static final String SUCCESSFUL_RESPONSE = "Successful response";
    public static final String RESPONSE_ERROR = "response error";
    // used for async api
    public static final String SUCCESS = "Success";
    public static final String FAIL = "Fail";
    public static final String RUNNING = "Running";
    public static final String SUBMITTED = "Submitted";

    public static final long LOG_REQUEST_PAYLOAD_LIMIT_MILLISECONDS = 1000;

    // used for transcript api
    public static final String COMPANY_ID_DATAPOINT = "EQOO4";
    public static final String JOB_ID_DATAPOINT = "EQN3D";
    public static final String EVENT_DATETIME_DATAPOINT = "EQ56B";
    public static final String DOCUMENT_TYPE = "EQ5NQ";
    public static final String PACKAGE_ID = "941";

    public static final Set<String> VALID_USE_CASES = Set.of("view", "export", "feed");

    public static final Set<String> EXCLUDED_PRODUCT_IDS = Set.of("DWS","EQAPI");

}
