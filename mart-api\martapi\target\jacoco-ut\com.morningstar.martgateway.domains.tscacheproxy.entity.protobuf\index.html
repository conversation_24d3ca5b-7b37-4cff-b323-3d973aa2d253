<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <span class="el_package">com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf</span></div><h1>com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">4,949 of 4,949</td><td class="ctr2">0%</td><td class="bar">517 of 517</td><td class="ctr2">0%</td><td class="ctr1">577</td><td class="ctr2">577</td><td class="ctr1">1,328</td><td class="ctr2">1,328</td><td class="ctr1">312</td><td class="ctr2">312</td><td class="ctr1">10</td><td class="ctr2">10</td></tr></tfoot><tbody><tr><td id="a2"><a href="TsCacheDataForProtoBuf$TimeSeriesData$Builder.html" class="el_class">TsCacheDataForProtoBuf.TimeSeriesData.Builder</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="1,267" alt="1,267"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="147" alt="147"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">143</td><td class="ctr2" id="g0">143</td><td class="ctr1" id="h0">357</td><td class="ctr2" id="i0">357</td><td class="ctr1" id="j0">67</td><td class="ctr2" id="k0">67</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a5"><a href="TsCacheDataForProtoBuf$TimeSeriesDatas$Builder.html" class="el_class">TsCacheDataForProtoBuf.TimeSeriesDatas.Builder</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="85" height="10" title="902" alt="902"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="85" height="10" title="105" alt="105"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">107</td><td class="ctr2" id="g1">107</td><td class="ctr1" id="h1">251</td><td class="ctr2" id="i1">251</td><td class="ctr1" id="j1">53</td><td class="ctr2" id="k1">53</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a1"><a href="TsCacheDataForProtoBuf$TimeSeriesData.html" class="el_class">TsCacheDataForProtoBuf.TimeSeriesData</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="74" height="10" title="791" alt="791"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="86" height="10" title="106" alt="106"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">102</td><td class="ctr2" id="g2">102</td><td class="ctr1" id="h3">202</td><td class="ctr2" id="i3">202</td><td class="ctr1" id="j2">49</td><td class="ctr2" id="k2">49</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a8"><a href="TsCacheDataForProtoBuf$TSValuePair$Builder.html" class="el_class">TsCacheDataForProtoBuf.TSValuePair.Builder</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="70" height="10" title="745" alt="745"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="59" alt="59"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">81</td><td class="ctr2" id="g3">81</td><td class="ctr1" id="h2">216</td><td class="ctr2" id="i2">216</td><td class="ctr1" id="j3">49</td><td class="ctr2" id="k3">49</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a4"><a href="TsCacheDataForProtoBuf$TimeSeriesDatas.html" class="el_class">TsCacheDataForProtoBuf.TimeSeriesDatas</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="50" height="10" title="528" alt="528"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="50" height="10" title="62" alt="62"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">73</td><td class="ctr2" id="g4">73</td><td class="ctr1" id="h4">132</td><td class="ctr2" id="i4">132</td><td class="ctr1" id="j4">42</td><td class="ctr2" id="k4">42</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a7"><a href="TsCacheDataForProtoBuf$TSValuePair.html" class="el_class">TsCacheDataForProtoBuf.TSValuePair</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="47" height="10" title="506" alt="506"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="31" height="10" title="38" alt="38"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f5">61</td><td class="ctr2" id="g5">61</td><td class="ctr1" id="h5">123</td><td class="ctr2" id="i5">123</td><td class="ctr1" id="j5">42</td><td class="ctr2" id="k5">42</td><td class="ctr1" id="l5">1</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a0"><a href="TsCacheDataForProtoBuf.html" class="el_class">TsCacheDataForProtoBuf</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="102" alt="102"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">4</td><td class="ctr2" id="g6">4</td><td class="ctr1" id="h6">14</td><td class="ctr2" id="i6">14</td><td class="ctr1" id="j6">4</td><td class="ctr2" id="k6">4</td><td class="ctr1" id="l6">1</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a9"><a href="TsCacheDataForProtoBuf$TSValuePair$1.html" class="el_class">TsCacheDataForProtoBuf.TSValuePair.new AbstractParser() {...}</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="36" alt="36"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">2</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h7">12</td><td class="ctr2" id="i7">12</td><td class="ctr1" id="j7">2</td><td class="ctr2" id="k7">2</td><td class="ctr1" id="l7">1</td><td class="ctr2" id="m7">1</td></tr><tr><td id="a3"><a href="TsCacheDataForProtoBuf$TimeSeriesData$1.html" class="el_class">TsCacheDataForProtoBuf.TimeSeriesData.new AbstractParser() {...}</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="36" alt="36"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h8">12</td><td class="ctr2" id="i8">12</td><td class="ctr1" id="j8">2</td><td class="ctr2" id="k8">2</td><td class="ctr1" id="l8">1</td><td class="ctr2" id="m8">1</td></tr><tr><td id="a6"><a href="TsCacheDataForProtoBuf$TimeSeriesDatas$1.html" class="el_class">TsCacheDataForProtoBuf.TimeSeriesDatas.new AbstractParser() {...}</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="36" alt="36"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">2</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h9">12</td><td class="ctr2" id="i9">12</td><td class="ctr1" id="j9">2</td><td class="ctr2" id="k9">2</td><td class="ctr1" id="l9">1</td><td class="ctr2" id="m9">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>