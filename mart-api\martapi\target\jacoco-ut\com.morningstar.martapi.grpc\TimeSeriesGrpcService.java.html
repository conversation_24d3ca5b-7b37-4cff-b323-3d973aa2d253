<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TimeSeriesGrpcService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <a href="index.source.html" class="el_package">com.morningstar.martapi.grpc</a> &gt; <span class="el_source">TimeSeriesGrpcService.java</span></div><h1>TimeSeriesGrpcService.java</h1><pre class="source lang-java linenums">package com.morningstar.martapi.grpc;

import com.morningstar.martapi.exception.TsCacheProtobufValidationException;
import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.timeseries.entity.TSResponse;
import com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf;
import com.morningstar.martgateway.infrastructures.log.LogAttribute;
import com.morningstar.martgateway.infrastructures.log.LogEntity;
import com.morningstar.martgateway.infrastructures.log.LogEntry;
import com.morningstar.martgateway.infrastructures.log.LogHelper;
import com.morningstar.martgateway.interfaces.MartGateway;
import com.morningstar.martgateway.util.JsonUtils;
import com.morningstar.martgateway.util.JwtUtil;
import com.morningstar.martcommon.entity.result.request.MartRequest;
import io.grpc.stub.StreamObserver;
import net.devh.boot.grpc.server.service.GrpcService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.morningstar.martapi.config.Constant.LOG_REQUEST_PAYLOAD_LIMIT_MILLISECONDS;
import static com.morningstar.martapi.config.Constant.RESPONSE;
import static com.morningstar.martapi.config.Constant.RESPONSE_ERROR;
import static com.morningstar.martapi.config.Constant.SUCCESSFUL_RESPONSE;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.*;

/**
 * gRPC服务实现 - 时间序列数据检索
 */
@GrpcService
public class TimeSeriesGrpcService extends TimeSeriesServiceGrpc.TimeSeriesServiceImplBase {

<span class="nc" id="L46">    private static final Logger log = LoggerFactory.getLogger(TimeSeriesGrpcService.class);</span>
    private static final String REQUEST_TYPE_VALUE = &quot;tscache-grpc&quot;;

    private final MartGateway&lt;TSResponse, MartRequest&gt; tsOldRspGateway;
    private final RequestValidationHandler&lt;HeadersAndParams, MartRequest&gt; validator;

    @Autowired
    public TimeSeriesGrpcService(
            @Qualifier(&quot;tsOldRspGateway&quot;) MartGateway&lt;TSResponse, MartRequest&gt; tsOldRspGateway,
<span class="nc" id="L55">            @Qualifier(&quot;timeSeriesApiValidator&quot;) RequestValidationHandler&lt;HeadersAndParams, MartRequest&gt; validator) {</span>
<span class="nc" id="L56">        this.tsOldRspGateway = tsOldRspGateway;</span>
<span class="nc" id="L57">        this.validator = validator;</span>
<span class="nc" id="L58">    }</span>

    @Override
    public void getTimeSeriesData(TimeSeriesRequest request, 
                                 StreamObserver&lt;TsCacheDataForProtoBuf.TimeSeriesDatas&gt; responseObserver) {
        
<span class="nc bnc" id="L64" title="All 2 branches missed.">        String reqId = StringUtils.isNotEmpty(request.getRequestId()) ? </span>
<span class="nc" id="L65">                      request.getRequestId() : UUID.randomUUID().toString();</span>
<span class="nc" id="L66">        long startTime = System.currentTimeMillis();</span>

        try {
<span class="nc" id="L69">            log.info(&quot;Processing gRPC request for investment IDs: {}&quot;, request.getInvestmentIdsList());</span>

            // 构建MartRequest
<span class="nc" id="L72">            MartRequest martRequest = buildMartRequest(request, reqId);</span>
            
            // 构建HeadersAndParams用于验证
<span class="nc" id="L75">            HeadersAndParams headersAndParams = HeadersAndParams.builder()</span>
<span class="nc" id="L76">                    .authorizationToken(request.getAuthorization())</span>
<span class="nc" id="L77">                    .productId(request.getProductId())</span>
<span class="nc" id="L78">                    .requestId(reqId)</span>
<span class="nc" id="L79">                    .build();</span>

            // 验证请求
<span class="nc" id="L82">            validateRequest(martRequest, headersAndParams);</span>

            // 调用业务逻辑
<span class="nc" id="L85">            tsOldRspGateway.asyncRetrieveSecurities(martRequest)</span>
<span class="nc" id="L86">                    .map(TSResponse::toProtobuf)</span>
<span class="nc" id="L87">                    .doOnNext(result -&gt; {</span>
                        // 记录成功日志
<span class="nc" id="L89">                        logSuccess(martRequest, reqId, startTime);</span>
                        
                        // 返回结果
<span class="nc" id="L92">                        responseObserver.onNext(result);</span>
<span class="nc" id="L93">                        responseObserver.onCompleted();</span>
<span class="nc" id="L94">                    })</span>
<span class="nc" id="L95">                    .doOnError(error -&gt; {</span>
                        // 记录错误日志
<span class="nc" id="L97">                        logError(martRequest, reqId, startTime, error);</span>
                        
                        // 返回错误
<span class="nc" id="L100">                        responseObserver.onError(io.grpc.Status.INTERNAL</span>
<span class="nc" id="L101">                                .withDescription(&quot;Failed to retrieve time series data: &quot; + error.getMessage())</span>
<span class="nc" id="L102">                                .withCause(error)</span>
<span class="nc" id="L103">                                .asRuntimeException());</span>
<span class="nc" id="L104">                    })</span>
<span class="nc" id="L105">                    .subscribe();</span>

<span class="nc" id="L107">        } catch (ValidationException e) {</span>
<span class="nc" id="L108">            log.error(&quot;Validation failed for gRPC request: {}&quot;, e.getMessage());</span>
<span class="nc" id="L109">            responseObserver.onError(io.grpc.Status.INVALID_ARGUMENT</span>
<span class="nc" id="L110">                    .withDescription(&quot;Validation failed: &quot; + e.getMessage())</span>
<span class="nc" id="L111">                    .withCause(e)</span>
<span class="nc" id="L112">                    .asRuntimeException());</span>
<span class="nc" id="L113">        } catch (Exception e) {</span>
<span class="nc" id="L114">            log.error(&quot;Unexpected error processing gRPC request&quot;, e);</span>
<span class="nc" id="L115">            responseObserver.onError(io.grpc.Status.INTERNAL</span>
<span class="nc" id="L116">                    .withDescription(&quot;Internal server error: &quot; + e.getMessage())</span>
<span class="nc" id="L117">                    .withCause(e)</span>
<span class="nc" id="L118">                    .asRuntimeException());</span>
<span class="nc" id="L119">        }</span>
<span class="nc" id="L120">    }</span>

    private MartRequest buildMartRequest(TimeSeriesRequest request, String reqId) {
<span class="nc" id="L123">        String userId = getUserIdFromToken(request.getUserId(), request.getAuthorization());</span>
<span class="nc" id="L124">        String configId = getConfigIdFromToken(request.getAuthorization());</span>

<span class="nc" id="L126">        return MartRequest.builder()</span>
<span class="nc" id="L127">                .currency(request.getCurrency())</span>
<span class="nc" id="L128">                .dps(new ArrayList&lt;&gt;(request.getDataPointsList()))</span>
<span class="nc" id="L129">                .ids(new ArrayList&lt;&gt;(request.getInvestmentIdsList()))</span>
<span class="nc" id="L130">                .startDate(request.getStartDate())</span>
<span class="nc" id="L131">                .endDate(request.getEndDate())</span>
<span class="nc" id="L132">                .preCurrency(request.getPreCurrency())</span>
<span class="nc" id="L133">                .readCache(request.getReadCache())</span>
<span class="nc" id="L134">                .productId(request.getProductId())</span>
<span class="nc" id="L135">                .entitlementProductId(request.getEntitlementProductId())</span>
<span class="nc" id="L136">                .requestId(reqId)</span>
<span class="nc" id="L137">                .userId(userId)</span>
<span class="nc" id="L138">                .dateFormat(request.getDateFormat())</span>
<span class="nc" id="L139">                .decimalFormat(request.getDecimalFormat())</span>
<span class="nc" id="L140">                .extendedPerformance(request.getExtendPerformance())</span>
<span class="nc" id="L141">                .postTax(request.getPostTax())</span>
<span class="nc" id="L142">                .useRequireId(request.getUseRequireId())</span>
<span class="nc" id="L143">                .checkEntitlement(request.getCheckEntitlement())</span>
<span class="nc" id="L144">                .useCase(request.getUseCase())</span>
<span class="nc" id="L145">                .useNewCCS(request.getUseNewCcs())</span>
<span class="nc" id="L146">                .configId(configId)</span>
<span class="nc" id="L147">                .build();</span>
    }

    private void validateRequest(MartRequest martRequest, HeadersAndParams headersAndParams) {
        try {
<span class="nc" id="L152">            validator.validateHeadersAndParams(headersAndParams);</span>
<span class="nc" id="L153">            validator.validateRequestBody(martRequest);</span>
<span class="nc" id="L154">        } catch (ValidationException e) {</span>
<span class="nc" id="L155">            throw new TsCacheProtobufValidationException(e.getStatus());</span>
<span class="nc" id="L156">        }</span>
<span class="nc" id="L157">    }</span>

    private static String getUserIdFromToken(String headerUserId, String token) {
<span class="nc" id="L160">        return StringUtils.defaultIfEmpty(JwtUtil.getFieldValue(token, &quot;https://morningstar.com/mstar_id&quot;), headerUserId);</span>
    }

    private static String getConfigIdFromToken(String token) {
<span class="nc" id="L164">        return JwtUtil.getFieldValue(token, &quot;https://morningstar.com/config_id&quot;);</span>
    }

    private void logSuccess(MartRequest martRequest, String reqId, long startTime) {
<span class="nc" id="L168">        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), reqId);</span>
<span class="nc" id="L169">        long executionTime = System.currentTimeMillis() - startTime;</span>
        
<span class="nc" id="L171">        List&lt;LogEntity&gt; logEntities = Stream.of(</span>
                new LogEntity(EVENT_TYPE, RESPONSE),
                new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
<span class="nc" id="L175">                new LogEntity(PRODUCT_ID, martRequest.getProductId()),</span>
<span class="nc" id="L176">                new LogEntity(USER_ID, martRequest.getUserId()),</span>
<span class="nc" id="L177">                new LogEntity(EXECUTE_TIME, executionTime)</span>
<span class="nc" id="L178">        ).collect(Collectors.toCollection(ArrayList::new));</span>
        
<span class="nc" id="L180">        addRequestPayload(martRequest, executionTime, logEntities);</span>
<span class="nc" id="L181">        LogEntry.info(logEntities.toArray(LogEntity[]::new));</span>
<span class="nc" id="L182">    }</span>

    private void logError(MartRequest martRequest, String reqId, long startTime, Throwable error) {
<span class="nc" id="L185">        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), reqId);</span>
<span class="nc" id="L186">        LogEntry.error(</span>
                new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                new LogEntity(EVENT_DESCRIPTION, error),
                new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
<span class="nc" id="L190">                new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(martRequest)),</span>
<span class="nc" id="L191">                new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime),</span>
<span class="nc" id="L192">                new LogEntity(EXCEPTION_TYPE, error.getClass()),</span>
<span class="nc" id="L193">                new LogEntity(PRODUCT_ID, martRequest.getProductId()),</span>
<span class="nc" id="L194">                new LogEntity(USER_ID, martRequest.getUserId())</span>
        );
<span class="nc" id="L196">    }</span>

    private void addRequestPayload(MartRequest martRequest, long executionTime, List&lt;LogEntity&gt; logEntities) {
<span class="nc bnc" id="L199" title="All 2 branches missed.">        if (executionTime &gt; LOG_REQUEST_PAYLOAD_LIMIT_MILLISECONDS) {</span>
<span class="nc" id="L200">            logEntities.add(new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(martRequest)));</span>
        }
<span class="nc" id="L202">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>