package com.morningstar.martapi.controller;

import com.morningstar.martapi.service.RedisMessagePublisher;
import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martcommon.entity.ClearCacheRequest;
import com.morningstar.martgateway.domains.core.entity.response.MartResponse;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.martgateway.interfaces.MartGateway;
import com.morningstar.martgateway.util.JwtUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;
import springfox.documentation.annotations.ApiIgnore;

import javax.inject.Inject;
import java.util.UUID;

@RestController
@RequestMapping(value = {"/v1/clear-cache"}, produces = {MediaType.APPLICATION_JSON_VALUE})
@ApiIgnore
public class ClearCacheController {

    private static final Logger log = LoggerFactory.getLogger(ClearCacheController.class);
    private final RequestValidationHandler<HeadersAndParams, ClearCacheRequest> validator;
    private final MartGateway<MartResponse, ClearCacheRequest> clearCacheGateway;
    private RedisMessagePublisher redisMessagePublisher;

    @Inject
    public ClearCacheController(
            MartGateway<MartResponse, ClearCacheRequest> clearCacheGateway,
            @Qualifier("clearCacheValidator") RequestValidationHandler<HeadersAndParams, ClearCacheRequest> validator,
            RedisMessagePublisher redisMessagePublisher) {
        this.validator = validator;
        this.clearCacheGateway = clearCacheGateway;
        this.redisMessagePublisher = redisMessagePublisher;
    }

    @PostMapping(value = "", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Mono<MartResponse> clearCache(
            @RequestBody ClearCacheRequest clearCacheRequest,
            @RequestHeader(value = "X-Api-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String token,
            ServerHttpRequest request)
    {
        HeadersAndParams headersAndParams = HeadersAndParams.builder()
                .authorizationToken(token)
                .productId(productId)
                .requestId(StringUtils.hasLength(requestId) ? requestId : UUID.randomUUID().toString())
                .build();
        validateRequest(clearCacheRequest, headersAndParams);
        String userIdFromToken = JwtUtil.getFieldValue(token, "https://morningstar.com/mstar_id");
        clearCacheRequest.setUserId(userIdFromToken);
        clearCacheRequest.setRequestId(requestId);

         return clearCacheGateway.asyncRetrieveSecurities(clearCacheRequest);
    }

    @DeleteMapping(value = "uim-token")
    public ResponseEntity<Void> clearUimCache (
            @RequestHeader(value = "X-Api-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String token) {
        if (!"mds".equalsIgnoreCase(productId)) { // only allow mds
            throw new ValidationException(Status.INVALID_PRODUCT_ID);
        }
        HeadersAndParams headersAndParams = HeadersAndParams.builder()
                .authorizationToken(token)
                .requestId(StringUtils.hasLength(requestId) ? requestId : UUID.randomUUID().toString())
                .build();
        validator.validateHeadersAndParams(headersAndParams);
        String userIdFromToken = JwtUtil.getFieldValue(token, "https://morningstar.com/mstar_id");
        log.info("Received request to clear UIM token cache from {}", userIdFromToken);
        try {
            redisMessagePublisher.publishUimTokenClearCache("Clear UIM Token Cache");
            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            log.error("event_type=\"Clear Cache\", event_description=\"Clear UIM Token Cache Error\", request_id=\"{}\", error_message=\"{}\"", requestId, e.getMessage());
            return ResponseEntity.internalServerError().build();
        }
    }

    private void validateRequest(ClearCacheRequest clearCacheRequest, HeadersAndParams headersAndParams) {
        validator.validateHeadersAndParams(headersAndParams);
        validator.validateRequestBody(clearCacheRequest);
    }
}