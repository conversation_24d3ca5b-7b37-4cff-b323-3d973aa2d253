// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: TsCacheData.proto

// Protobuf Java Version: 3.25.1
package com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf;

public final class TsCacheDataForProtoBuf {
  private TsCacheDataForProtoBuf() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface TSValuePairOrBuilder extends
      // @@protoc_insertion_point(interface_extends:protobuf.TSValuePair)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated int64 dates = 1;</code>
     * @return A list containing the dates.
     */
    java.util.List<java.lang.Long> getDatesList();
    /**
     * <code>repeated int64 dates = 1;</code>
     * @return The count of dates.
     */
    int getDatesCount();
    /**
     * <code>repeated int64 dates = 1;</code>
     * @param index The index of the element to return.
     * @return The dates at the given index.
     */
    long getDates(int index);

    /**
     * <code>repeated double values = 2;</code>
     * @return A list containing the values.
     */
    java.util.List<java.lang.Double> getValuesList();
    /**
     * <code>repeated double values = 2;</code>
     * @return The count of values.
     */
    int getValuesCount();
    /**
     * <code>repeated double values = 2;</code>
     * @param index The index of the element to return.
     * @return The values at the given index.
     */
    double getValues(int index);

    /**
     * <code>repeated string strings = 3;</code>
     * @return A list containing the strings.
     */
    java.util.List<java.lang.String>
        getStringsList();
    /**
     * <code>repeated string strings = 3;</code>
     * @return The count of strings.
     */
    int getStringsCount();
    /**
     * <code>repeated string strings = 3;</code>
     * @param index The index of the element to return.
     * @return The strings at the given index.
     */
    java.lang.String getStrings(int index);
    /**
     * <code>repeated string strings = 3;</code>
     * @param index The index of the value to return.
     * @return The bytes of the strings at the given index.
     */
    com.google.protobuf.ByteString
        getStringsBytes(int index);
  }
  /**
   * Protobuf type {@code protobuf.TSValuePair}
   */
  public static final class TSValuePair extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:protobuf.TSValuePair)
      TSValuePairOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TSValuePair.newBuilder() to construct.
    private TSValuePair(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TSValuePair() {
      dates_ = emptyLongList();
      values_ = emptyDoubleList();
      strings_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TSValuePair();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TSValuePair_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TSValuePair_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.class, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.Builder.class);
    }

    public static final int DATES_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.LongList dates_ =
        emptyLongList();
    /**
     * <code>repeated int64 dates = 1;</code>
     * @return A list containing the dates.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getDatesList() {
      return dates_;
    }
    /**
     * <code>repeated int64 dates = 1;</code>
     * @return The count of dates.
     */
    public int getDatesCount() {
      return dates_.size();
    }
    /**
     * <code>repeated int64 dates = 1;</code>
     * @param index The index of the element to return.
     * @return The dates at the given index.
     */
    public long getDates(int index) {
      return dates_.getLong(index);
    }

    public static final int VALUES_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.DoubleList values_ =
        emptyDoubleList();
    /**
     * <code>repeated double values = 2;</code>
     * @return A list containing the values.
     */
    @java.lang.Override
    public java.util.List<java.lang.Double>
        getValuesList() {
      return values_;
    }
    /**
     * <code>repeated double values = 2;</code>
     * @return The count of values.
     */
    public int getValuesCount() {
      return values_.size();
    }
    /**
     * <code>repeated double values = 2;</code>
     * @param index The index of the element to return.
     * @return The values at the given index.
     */
    public double getValues(int index) {
      return values_.getDouble(index);
    }

    public static final int STRINGS_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private com.google.protobuf.LazyStringArrayList strings_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    /**
     * <code>repeated string strings = 3;</code>
     * @return A list containing the strings.
     */
    public com.google.protobuf.ProtocolStringList
        getStringsList() {
      return strings_;
    }
    /**
     * <code>repeated string strings = 3;</code>
     * @return The count of strings.
     */
    public int getStringsCount() {
      return strings_.size();
    }
    /**
     * <code>repeated string strings = 3;</code>
     * @param index The index of the element to return.
     * @return The strings at the given index.
     */
    public java.lang.String getStrings(int index) {
      return strings_.get(index);
    }
    /**
     * <code>repeated string strings = 3;</code>
     * @param index The index of the value to return.
     * @return The bytes of the strings at the given index.
     */
    public com.google.protobuf.ByteString
        getStringsBytes(int index) {
      return strings_.getByteString(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < dates_.size(); i++) {
        output.writeInt64(1, dates_.getLong(i));
      }
      for (int i = 0; i < values_.size(); i++) {
        output.writeDouble(2, values_.getDouble(i));
      }
      for (int i = 0; i < strings_.size(); i++) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, strings_.getRaw(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < dates_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(dates_.getLong(i));
        }
        size += dataSize;
        size += 1 * getDatesList().size();
      }
      {
        int dataSize = 0;
        dataSize = 8 * getValuesList().size();
        size += dataSize;
        size += 1 * getValuesList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < strings_.size(); i++) {
          dataSize += computeStringSizeNoTag(strings_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getStringsList().size();
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair)) {
        return super.equals(obj);
      }
      com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair other = (com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair) obj;

      if (!getDatesList()
          .equals(other.getDatesList())) return false;
      if (!getValuesList()
          .equals(other.getValuesList())) return false;
      if (!getStringsList()
          .equals(other.getStringsList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getDatesCount() > 0) {
        hash = (37 * hash) + DATES_FIELD_NUMBER;
        hash = (53 * hash) + getDatesList().hashCode();
      }
      if (getValuesCount() > 0) {
        hash = (37 * hash) + VALUES_FIELD_NUMBER;
        hash = (53 * hash) + getValuesList().hashCode();
      }
      if (getStringsCount() > 0) {
        hash = (37 * hash) + STRINGS_FIELD_NUMBER;
        hash = (53 * hash) + getStringsList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protobuf.TSValuePair}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:protobuf.TSValuePair)
        com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePairOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TSValuePair_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TSValuePair_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.class, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.Builder.class);
      }

      // Construct using com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        dates_ = emptyLongList();
        values_ = emptyDoubleList();
        strings_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TSValuePair_descriptor;
      }

      @java.lang.Override
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair getDefaultInstanceForType() {
        return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.getDefaultInstance();
      }

      @java.lang.Override
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair build() {
        com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair buildPartial() {
        com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair result = new com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          dates_.makeImmutable();
          result.dates_ = dates_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          values_.makeImmutable();
          result.values_ = values_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          strings_.makeImmutable();
          result.strings_ = strings_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair) {
          return mergeFrom((com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair other) {
        if (other == com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.getDefaultInstance()) return this;
        if (!other.dates_.isEmpty()) {
          if (dates_.isEmpty()) {
            dates_ = other.dates_;
            dates_.makeImmutable();
            bitField0_ |= 0x00000001;
          } else {
            ensureDatesIsMutable();
            dates_.addAll(other.dates_);
          }
          onChanged();
        }
        if (!other.values_.isEmpty()) {
          if (values_.isEmpty()) {
            values_ = other.values_;
            values_.makeImmutable();
            bitField0_ |= 0x00000002;
          } else {
            ensureValuesIsMutable();
            values_.addAll(other.values_);
          }
          onChanged();
        }
        if (!other.strings_.isEmpty()) {
          if (strings_.isEmpty()) {
            strings_ = other.strings_;
            bitField0_ |= 0x00000004;
          } else {
            ensureStringsIsMutable();
            strings_.addAll(other.strings_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                long v = input.readInt64();
                ensureDatesIsMutable();
                dates_.addLong(v);
                break;
              } // case 8
              case 10: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureDatesIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  dates_.addLong(input.readInt64());
                }
                input.popLimit(limit);
                break;
              } // case 10
              case 17: {
                double v = input.readDouble();
                ensureValuesIsMutable();
                values_.addDouble(v);
                break;
              } // case 17
              case 18: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                int alloc = length > 4096 ? 4096 : length;
                ensureValuesIsMutable(alloc / 8);
                while (input.getBytesUntilLimit() > 0) {
                  values_.addDouble(input.readDouble());
                }
                input.popLimit(limit);
                break;
              } // case 18
              case 26: {
                com.google.protobuf.ByteString bs = input.readBytes();
                ensureStringsIsMutable();
                strings_.add(bs);
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.LongList dates_ = emptyLongList();
      private void ensureDatesIsMutable() {
        if (!dates_.isModifiable()) {
          dates_ = makeMutableCopy(dates_);
        }
        bitField0_ |= 0x00000001;
      }
      /**
       * <code>repeated int64 dates = 1;</code>
       * @return A list containing the dates.
       */
      public java.util.List<java.lang.Long>
          getDatesList() {
        dates_.makeImmutable();
        return dates_;
      }
      /**
       * <code>repeated int64 dates = 1;</code>
       * @return The count of dates.
       */
      public int getDatesCount() {
        return dates_.size();
      }
      /**
       * <code>repeated int64 dates = 1;</code>
       * @param index The index of the element to return.
       * @return The dates at the given index.
       */
      public long getDates(int index) {
        return dates_.getLong(index);
      }
      /**
       * <code>repeated int64 dates = 1;</code>
       * @param index The index to set the value at.
       * @param value The dates to set.
       * @return This builder for chaining.
       */
      public Builder setDates(
          int index, long value) {

        ensureDatesIsMutable();
        dates_.setLong(index, value);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 dates = 1;</code>
       * @param value The dates to add.
       * @return This builder for chaining.
       */
      public Builder addDates(long value) {

        ensureDatesIsMutable();
        dates_.addLong(value);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 dates = 1;</code>
       * @param values The dates to add.
       * @return This builder for chaining.
       */
      public Builder addAllDates(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureDatesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, dates_);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 dates = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearDates() {
        dates_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.DoubleList values_ = emptyDoubleList();
      private void ensureValuesIsMutable() {
        if (!values_.isModifiable()) {
          values_ = makeMutableCopy(values_);
        }
        bitField0_ |= 0x00000002;
      }
      private void ensureValuesIsMutable(int capacity) {
        if (!values_.isModifiable()) {
          values_ = makeMutableCopy(values_, capacity);
        }
        bitField0_ |= 0x00000002;
      }
      /**
       * <code>repeated double values = 2;</code>
       * @return A list containing the values.
       */
      public java.util.List<java.lang.Double>
          getValuesList() {
        values_.makeImmutable();
        return values_;
      }
      /**
       * <code>repeated double values = 2;</code>
       * @return The count of values.
       */
      public int getValuesCount() {
        return values_.size();
      }
      /**
       * <code>repeated double values = 2;</code>
       * @param index The index of the element to return.
       * @return The values at the given index.
       */
      public double getValues(int index) {
        return values_.getDouble(index);
      }
      /**
       * <code>repeated double values = 2;</code>
       * @param index The index to set the value at.
       * @param value The values to set.
       * @return This builder for chaining.
       */
      public Builder setValues(
          int index, double value) {

        ensureValuesIsMutable();
        values_.setDouble(index, value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>repeated double values = 2;</code>
       * @param value The values to add.
       * @return This builder for chaining.
       */
      public Builder addValues(double value) {

        ensureValuesIsMutable();
        values_.addDouble(value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>repeated double values = 2;</code>
       * @param values The values to add.
       * @return This builder for chaining.
       */
      public Builder addAllValues(
          java.lang.Iterable<? extends java.lang.Double> values) {
        ensureValuesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, values_);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>repeated double values = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearValues() {
        values_ = emptyDoubleList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringArrayList strings_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      private void ensureStringsIsMutable() {
        if (!strings_.isModifiable()) {
          strings_ = new com.google.protobuf.LazyStringArrayList(strings_);
        }
        bitField0_ |= 0x00000004;
      }
      /**
       * <code>repeated string strings = 3;</code>
       * @return A list containing the strings.
       */
      public com.google.protobuf.ProtocolStringList
          getStringsList() {
        strings_.makeImmutable();
        return strings_;
      }
      /**
       * <code>repeated string strings = 3;</code>
       * @return The count of strings.
       */
      public int getStringsCount() {
        return strings_.size();
      }
      /**
       * <code>repeated string strings = 3;</code>
       * @param index The index of the element to return.
       * @return The strings at the given index.
       */
      public java.lang.String getStrings(int index) {
        return strings_.get(index);
      }
      /**
       * <code>repeated string strings = 3;</code>
       * @param index The index of the value to return.
       * @return The bytes of the strings at the given index.
       */
      public com.google.protobuf.ByteString
          getStringsBytes(int index) {
        return strings_.getByteString(index);
      }
      /**
       * <code>repeated string strings = 3;</code>
       * @param index The index to set the value at.
       * @param value The strings to set.
       * @return This builder for chaining.
       */
      public Builder setStrings(
          int index, java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureStringsIsMutable();
        strings_.set(index, value);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>repeated string strings = 3;</code>
       * @param value The strings to add.
       * @return This builder for chaining.
       */
      public Builder addStrings(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureStringsIsMutable();
        strings_.add(value);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>repeated string strings = 3;</code>
       * @param values The strings to add.
       * @return This builder for chaining.
       */
      public Builder addAllStrings(
          java.lang.Iterable<java.lang.String> values) {
        ensureStringsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, strings_);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>repeated string strings = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearStrings() {
        strings_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);;
        onChanged();
        return this;
      }
      /**
       * <code>repeated string strings = 3;</code>
       * @param value The bytes of the strings to add.
       * @return This builder for chaining.
       */
      public Builder addStringsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureStringsIsMutable();
        strings_.add(value);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:protobuf.TSValuePair)
    }

    // @@protoc_insertion_point(class_scope:protobuf.TSValuePair)
    private static final com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair();
    }

    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<TSValuePair>
        PARSER = new com.google.protobuf.AbstractParser<TSValuePair>() {
      @java.lang.Override
      public TSValuePair parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<TSValuePair> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TSValuePair> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TimeSeriesDatasOrBuilder extends
      // @@protoc_insertion_point(interface_extends:protobuf.TimeSeriesDatas)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
     */
    java.util.List<com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData> 
        getValuesList();
    /**
     * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
     */
    com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData getValues(int index);
    /**
     * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
     */
    int getValuesCount();
    /**
     * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
     */
    java.util.List<? extends com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDataOrBuilder> 
        getValuesOrBuilderList();
    /**
     * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
     */
    com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDataOrBuilder getValuesOrBuilder(
        int index);

    /**
     * <code>optional int64 retcode = 102;</code>
     * @return Whether the retcode field is set.
     */
    boolean hasRetcode();
    /**
     * <code>optional int64 retcode = 102;</code>
     * @return The retcode.
     */
    long getRetcode();

    /**
     * <code>optional string msg = 103;</code>
     * @return Whether the msg field is set.
     */
    boolean hasMsg();
    /**
     * <code>optional string msg = 103;</code>
     * @return The msg.
     */
    java.lang.String getMsg();
    /**
     * <code>optional string msg = 103;</code>
     * @return The bytes for msg.
     */
    com.google.protobuf.ByteString
        getMsgBytes();
  }
  /**
   * Protobuf type {@code protobuf.TimeSeriesDatas}
   */
  public static final class TimeSeriesDatas extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:protobuf.TimeSeriesDatas)
      TimeSeriesDatasOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TimeSeriesDatas.newBuilder() to construct.
    private TimeSeriesDatas(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TimeSeriesDatas() {
      values_ = java.util.Collections.emptyList();
      msg_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TimeSeriesDatas();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TimeSeriesDatas_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TimeSeriesDatas_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas.class, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas.Builder.class);
    }

    private int bitField0_;
    public static final int VALUES_FIELD_NUMBER = 101;
    @SuppressWarnings("serial")
    private java.util.List<com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData> values_;
    /**
     * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
     */
    @java.lang.Override
    public java.util.List<com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData> getValuesList() {
      return values_;
    }
    /**
     * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDataOrBuilder> 
        getValuesOrBuilderList() {
      return values_;
    }
    /**
     * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
     */
    @java.lang.Override
    public int getValuesCount() {
      return values_.size();
    }
    /**
     * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
     */
    @java.lang.Override
    public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData getValues(int index) {
      return values_.get(index);
    }
    /**
     * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
     */
    @java.lang.Override
    public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDataOrBuilder getValuesOrBuilder(
        int index) {
      return values_.get(index);
    }

    public static final int RETCODE_FIELD_NUMBER = 102;
    private long retcode_ = 0L;
    /**
     * <code>optional int64 retcode = 102;</code>
     * @return Whether the retcode field is set.
     */
    @java.lang.Override
    public boolean hasRetcode() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 retcode = 102;</code>
     * @return The retcode.
     */
    @java.lang.Override
    public long getRetcode() {
      return retcode_;
    }

    public static final int MSG_FIELD_NUMBER = 103;
    @SuppressWarnings("serial")
    private volatile java.lang.Object msg_ = "";
    /**
     * <code>optional string msg = 103;</code>
     * @return Whether the msg field is set.
     */
    @java.lang.Override
    public boolean hasMsg() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string msg = 103;</code>
     * @return The msg.
     */
    @java.lang.Override
    public java.lang.String getMsg() {
      java.lang.Object ref = msg_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          msg_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string msg = 103;</code>
     * @return The bytes for msg.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getMsgBytes() {
      java.lang.Object ref = msg_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        msg_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      for (int i = 0; i < getValuesCount(); i++) {
        if (!getValues(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < values_.size(); i++) {
        output.writeMessage(101, values_.get(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(102, retcode_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 103, msg_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < values_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(101, values_.get(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(102, retcode_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(103, msg_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas)) {
        return super.equals(obj);
      }
      com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas other = (com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas) obj;

      if (!getValuesList()
          .equals(other.getValuesList())) return false;
      if (hasRetcode() != other.hasRetcode()) return false;
      if (hasRetcode()) {
        if (getRetcode()
            != other.getRetcode()) return false;
      }
      if (hasMsg() != other.hasMsg()) return false;
      if (hasMsg()) {
        if (!getMsg()
            .equals(other.getMsg())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getValuesCount() > 0) {
        hash = (37 * hash) + VALUES_FIELD_NUMBER;
        hash = (53 * hash) + getValuesList().hashCode();
      }
      if (hasRetcode()) {
        hash = (37 * hash) + RETCODE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getRetcode());
      }
      if (hasMsg()) {
        hash = (37 * hash) + MSG_FIELD_NUMBER;
        hash = (53 * hash) + getMsg().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protobuf.TimeSeriesDatas}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:protobuf.TimeSeriesDatas)
        com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatasOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TimeSeriesDatas_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TimeSeriesDatas_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas.class, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas.Builder.class);
      }

      // Construct using com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (valuesBuilder_ == null) {
          values_ = java.util.Collections.emptyList();
        } else {
          values_ = null;
          valuesBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        retcode_ = 0L;
        msg_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TimeSeriesDatas_descriptor;
      }

      @java.lang.Override
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas getDefaultInstanceForType() {
        return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas.getDefaultInstance();
      }

      @java.lang.Override
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas build() {
        com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas buildPartial() {
        com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas result = new com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas result) {
        if (valuesBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            values_ = java.util.Collections.unmodifiableList(values_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.values_ = values_;
        } else {
          result.values_ = valuesBuilder_.build();
        }
      }

      private void buildPartial0(com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.retcode_ = retcode_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.msg_ = msg_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas) {
          return mergeFrom((com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas other) {
        if (other == com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas.getDefaultInstance()) return this;
        if (valuesBuilder_ == null) {
          if (!other.values_.isEmpty()) {
            if (values_.isEmpty()) {
              values_ = other.values_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureValuesIsMutable();
              values_.addAll(other.values_);
            }
            onChanged();
          }
        } else {
          if (!other.values_.isEmpty()) {
            if (valuesBuilder_.isEmpty()) {
              valuesBuilder_.dispose();
              valuesBuilder_ = null;
              values_ = other.values_;
              bitField0_ = (bitField0_ & ~0x00000001);
              valuesBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getValuesFieldBuilder() : null;
            } else {
              valuesBuilder_.addAllMessages(other.values_);
            }
          }
        }
        if (other.hasRetcode()) {
          setRetcode(other.getRetcode());
        }
        if (other.hasMsg()) {
          msg_ = other.msg_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        for (int i = 0; i < getValuesCount(); i++) {
          if (!getValues(i).isInitialized()) {
            return false;
          }
        }
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 810: {
                com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData m =
                    input.readMessage(
                        com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.PARSER,
                        extensionRegistry);
                if (valuesBuilder_ == null) {
                  ensureValuesIsMutable();
                  values_.add(m);
                } else {
                  valuesBuilder_.addMessage(m);
                }
                break;
              } // case 810
              case 816: {
                retcode_ = input.readInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 816
              case 826: {
                msg_ = input.readBytes();
                bitField0_ |= 0x00000004;
                break;
              } // case 826
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData> values_ =
        java.util.Collections.emptyList();
      private void ensureValuesIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          values_ = new java.util.ArrayList<com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData>(values_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.Builder, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDataOrBuilder> valuesBuilder_;

      /**
       * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
       */
      public java.util.List<com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData> getValuesList() {
        if (valuesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(values_);
        } else {
          return valuesBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
       */
      public int getValuesCount() {
        if (valuesBuilder_ == null) {
          return values_.size();
        } else {
          return valuesBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
       */
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData getValues(int index) {
        if (valuesBuilder_ == null) {
          return values_.get(index);
        } else {
          return valuesBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
       */
      public Builder setValues(
          int index, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData value) {
        if (valuesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureValuesIsMutable();
          values_.set(index, value);
          onChanged();
        } else {
          valuesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
       */
      public Builder setValues(
          int index, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.Builder builderForValue) {
        if (valuesBuilder_ == null) {
          ensureValuesIsMutable();
          values_.set(index, builderForValue.build());
          onChanged();
        } else {
          valuesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
       */
      public Builder addValues(com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData value) {
        if (valuesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureValuesIsMutable();
          values_.add(value);
          onChanged();
        } else {
          valuesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
       */
      public Builder addValues(
          int index, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData value) {
        if (valuesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureValuesIsMutable();
          values_.add(index, value);
          onChanged();
        } else {
          valuesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
       */
      public Builder addValues(
          com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.Builder builderForValue) {
        if (valuesBuilder_ == null) {
          ensureValuesIsMutable();
          values_.add(builderForValue.build());
          onChanged();
        } else {
          valuesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
       */
      public Builder addValues(
          int index, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.Builder builderForValue) {
        if (valuesBuilder_ == null) {
          ensureValuesIsMutable();
          values_.add(index, builderForValue.build());
          onChanged();
        } else {
          valuesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
       */
      public Builder addAllValues(
          java.lang.Iterable<? extends com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData> values) {
        if (valuesBuilder_ == null) {
          ensureValuesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, values_);
          onChanged();
        } else {
          valuesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
       */
      public Builder clearValues() {
        if (valuesBuilder_ == null) {
          values_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          valuesBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
       */
      public Builder removeValues(int index) {
        if (valuesBuilder_ == null) {
          ensureValuesIsMutable();
          values_.remove(index);
          onChanged();
        } else {
          valuesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
       */
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.Builder getValuesBuilder(
          int index) {
        return getValuesFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
       */
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDataOrBuilder getValuesOrBuilder(
          int index) {
        if (valuesBuilder_ == null) {
          return values_.get(index);  } else {
          return valuesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
       */
      public java.util.List<? extends com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDataOrBuilder> 
           getValuesOrBuilderList() {
        if (valuesBuilder_ != null) {
          return valuesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(values_);
        }
      }
      /**
       * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
       */
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.Builder addValuesBuilder() {
        return getValuesFieldBuilder().addBuilder(
            com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.getDefaultInstance());
      }
      /**
       * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
       */
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.Builder addValuesBuilder(
          int index) {
        return getValuesFieldBuilder().addBuilder(
            index, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.getDefaultInstance());
      }
      /**
       * <code>repeated .protobuf.TimeSeriesData Values = 101;</code>
       */
      public java.util.List<com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.Builder> 
           getValuesBuilderList() {
        return getValuesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.Builder, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDataOrBuilder> 
          getValuesFieldBuilder() {
        if (valuesBuilder_ == null) {
          valuesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.Builder, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDataOrBuilder>(
                  values_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          values_ = null;
        }
        return valuesBuilder_;
      }

      private long retcode_ ;
      /**
       * <code>optional int64 retcode = 102;</code>
       * @return Whether the retcode field is set.
       */
      @java.lang.Override
      public boolean hasRetcode() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 retcode = 102;</code>
       * @return The retcode.
       */
      @java.lang.Override
      public long getRetcode() {
        return retcode_;
      }
      /**
       * <code>optional int64 retcode = 102;</code>
       * @param value The retcode to set.
       * @return This builder for chaining.
       */
      public Builder setRetcode(long value) {

        retcode_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 retcode = 102;</code>
       * @return This builder for chaining.
       */
      public Builder clearRetcode() {
        bitField0_ = (bitField0_ & ~0x00000002);
        retcode_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object msg_ = "";
      /**
       * <code>optional string msg = 103;</code>
       * @return Whether the msg field is set.
       */
      public boolean hasMsg() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional string msg = 103;</code>
       * @return The msg.
       */
      public java.lang.String getMsg() {
        java.lang.Object ref = msg_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            msg_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string msg = 103;</code>
       * @return The bytes for msg.
       */
      public com.google.protobuf.ByteString
          getMsgBytes() {
        java.lang.Object ref = msg_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          msg_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string msg = 103;</code>
       * @param value The msg to set.
       * @return This builder for chaining.
       */
      public Builder setMsg(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        msg_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional string msg = 103;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsg() {
        msg_ = getDefaultInstance().getMsg();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <code>optional string msg = 103;</code>
       * @param value The bytes for msg to set.
       * @return This builder for chaining.
       */
      public Builder setMsgBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        msg_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:protobuf.TimeSeriesDatas)
    }

    // @@protoc_insertion_point(class_scope:protobuf.TimeSeriesDatas)
    private static final com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas();
    }

    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<TimeSeriesDatas>
        PARSER = new com.google.protobuf.AbstractParser<TimeSeriesDatas>() {
      @java.lang.Override
      public TimeSeriesDatas parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<TimeSeriesDatas> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TimeSeriesDatas> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TimeSeriesDataOrBuilder extends
      // @@protoc_insertion_point(interface_extends:protobuf.TimeSeriesData)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>required string SecId = 1;</code>
     * @return Whether the secId field is set.
     */
    boolean hasSecId();
    /**
     * <code>required string SecId = 1;</code>
     * @return The secId.
     */
    java.lang.String getSecId();
    /**
     * <code>required string SecId = 1;</code>
     * @return The bytes for secId.
     */
    com.google.protobuf.ByteString
        getSecIdBytes();

    /**
     * <code>optional string Universe = 2;</code>
     * @return Whether the universe field is set.
     */
    boolean hasUniverse();
    /**
     * <code>optional string Universe = 2;</code>
     * @return The universe.
     */
    java.lang.String getUniverse();
    /**
     * <code>optional string Universe = 2;</code>
     * @return The bytes for universe.
     */
    com.google.protobuf.ByteString
        getUniverseBytes();

    /**
     * <code>repeated .protobuf.TSValuePair Values = 3;</code>
     */
    java.util.List<com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair> 
        getValuesList();
    /**
     * <code>repeated .protobuf.TSValuePair Values = 3;</code>
     */
    com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair getValues(int index);
    /**
     * <code>repeated .protobuf.TSValuePair Values = 3;</code>
     */
    int getValuesCount();
    /**
     * <code>repeated .protobuf.TSValuePair Values = 3;</code>
     */
    java.util.List<? extends com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePairOrBuilder> 
        getValuesOrBuilderList();
    /**
     * <code>repeated .protobuf.TSValuePair Values = 3;</code>
     */
    com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePairOrBuilder getValuesOrBuilder(
        int index);

    /**
     * <code>optional string DataId = 4;</code>
     * @return Whether the dataId field is set.
     */
    boolean hasDataId();
    /**
     * <code>optional string DataId = 4;</code>
     * @return The dataId.
     */
    java.lang.String getDataId();
    /**
     * <code>optional string DataId = 4;</code>
     * @return The bytes for dataId.
     */
    com.google.protobuf.ByteString
        getDataIdBytes();

    /**
     * <code>optional string ErrorCode = 5;</code>
     * @return Whether the errorCode field is set.
     */
    boolean hasErrorCode();
    /**
     * <code>optional string ErrorCode = 5;</code>
     * @return The errorCode.
     */
    java.lang.String getErrorCode();
    /**
     * <code>optional string ErrorCode = 5;</code>
     * @return The bytes for errorCode.
     */
    com.google.protobuf.ByteString
        getErrorCodeBytes();
  }
  /**
   * Protobuf type {@code protobuf.TimeSeriesData}
   */
  public static final class TimeSeriesData extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:protobuf.TimeSeriesData)
      TimeSeriesDataOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TimeSeriesData.newBuilder() to construct.
    private TimeSeriesData(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private TimeSeriesData() {
      secId_ = "";
      universe_ = "";
      values_ = java.util.Collections.emptyList();
      dataId_ = "";
      errorCode_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new TimeSeriesData();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TimeSeriesData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TimeSeriesData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.class, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.Builder.class);
    }

    private int bitField0_;
    public static final int SECID_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object secId_ = "";
    /**
     * <code>required string SecId = 1;</code>
     * @return Whether the secId field is set.
     */
    @java.lang.Override
    public boolean hasSecId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>required string SecId = 1;</code>
     * @return The secId.
     */
    @java.lang.Override
    public java.lang.String getSecId() {
      java.lang.Object ref = secId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          secId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string SecId = 1;</code>
     * @return The bytes for secId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getSecIdBytes() {
      java.lang.Object ref = secId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        secId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int UNIVERSE_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object universe_ = "";
    /**
     * <code>optional string Universe = 2;</code>
     * @return Whether the universe field is set.
     */
    @java.lang.Override
    public boolean hasUniverse() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string Universe = 2;</code>
     * @return The universe.
     */
    @java.lang.Override
    public java.lang.String getUniverse() {
      java.lang.Object ref = universe_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          universe_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string Universe = 2;</code>
     * @return The bytes for universe.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getUniverseBytes() {
      java.lang.Object ref = universe_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        universe_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int VALUES_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private java.util.List<com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair> values_;
    /**
     * <code>repeated .protobuf.TSValuePair Values = 3;</code>
     */
    @java.lang.Override
    public java.util.List<com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair> getValuesList() {
      return values_;
    }
    /**
     * <code>repeated .protobuf.TSValuePair Values = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePairOrBuilder> 
        getValuesOrBuilderList() {
      return values_;
    }
    /**
     * <code>repeated .protobuf.TSValuePair Values = 3;</code>
     */
    @java.lang.Override
    public int getValuesCount() {
      return values_.size();
    }
    /**
     * <code>repeated .protobuf.TSValuePair Values = 3;</code>
     */
    @java.lang.Override
    public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair getValues(int index) {
      return values_.get(index);
    }
    /**
     * <code>repeated .protobuf.TSValuePair Values = 3;</code>
     */
    @java.lang.Override
    public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePairOrBuilder getValuesOrBuilder(
        int index) {
      return values_.get(index);
    }

    public static final int DATAID_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private volatile java.lang.Object dataId_ = "";
    /**
     * <code>optional string DataId = 4;</code>
     * @return Whether the dataId field is set.
     */
    @java.lang.Override
    public boolean hasDataId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string DataId = 4;</code>
     * @return The dataId.
     */
    @java.lang.Override
    public java.lang.String getDataId() {
      java.lang.Object ref = dataId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          dataId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string DataId = 4;</code>
     * @return The bytes for dataId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDataIdBytes() {
      java.lang.Object ref = dataId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        dataId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ERRORCODE_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private volatile java.lang.Object errorCode_ = "";
    /**
     * <code>optional string ErrorCode = 5;</code>
     * @return Whether the errorCode field is set.
     */
    @java.lang.Override
    public boolean hasErrorCode() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional string ErrorCode = 5;</code>
     * @return The errorCode.
     */
    @java.lang.Override
    public java.lang.String getErrorCode() {
      java.lang.Object ref = errorCode_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          errorCode_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string ErrorCode = 5;</code>
     * @return The bytes for errorCode.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getErrorCodeBytes() {
      java.lang.Object ref = errorCode_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        errorCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasSecId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, secId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, universe_);
      }
      for (int i = 0; i < values_.size(); i++) {
        output.writeMessage(3, values_.get(i));
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, dataId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, errorCode_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, secId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, universe_);
      }
      for (int i = 0; i < values_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, values_.get(i));
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, dataId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, errorCode_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData)) {
        return super.equals(obj);
      }
      com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData other = (com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData) obj;

      if (hasSecId() != other.hasSecId()) return false;
      if (hasSecId()) {
        if (!getSecId()
            .equals(other.getSecId())) return false;
      }
      if (hasUniverse() != other.hasUniverse()) return false;
      if (hasUniverse()) {
        if (!getUniverse()
            .equals(other.getUniverse())) return false;
      }
      if (!getValuesList()
          .equals(other.getValuesList())) return false;
      if (hasDataId() != other.hasDataId()) return false;
      if (hasDataId()) {
        if (!getDataId()
            .equals(other.getDataId())) return false;
      }
      if (hasErrorCode() != other.hasErrorCode()) return false;
      if (hasErrorCode()) {
        if (!getErrorCode()
            .equals(other.getErrorCode())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSecId()) {
        hash = (37 * hash) + SECID_FIELD_NUMBER;
        hash = (53 * hash) + getSecId().hashCode();
      }
      if (hasUniverse()) {
        hash = (37 * hash) + UNIVERSE_FIELD_NUMBER;
        hash = (53 * hash) + getUniverse().hashCode();
      }
      if (getValuesCount() > 0) {
        hash = (37 * hash) + VALUES_FIELD_NUMBER;
        hash = (53 * hash) + getValuesList().hashCode();
      }
      if (hasDataId()) {
        hash = (37 * hash) + DATAID_FIELD_NUMBER;
        hash = (53 * hash) + getDataId().hashCode();
      }
      if (hasErrorCode()) {
        hash = (37 * hash) + ERRORCODE_FIELD_NUMBER;
        hash = (53 * hash) + getErrorCode().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protobuf.TimeSeriesData}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:protobuf.TimeSeriesData)
        com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDataOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TimeSeriesData_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TimeSeriesData_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.class, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.Builder.class);
      }

      // Construct using com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        secId_ = "";
        universe_ = "";
        if (valuesBuilder_ == null) {
          values_ = java.util.Collections.emptyList();
        } else {
          values_ = null;
          valuesBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        dataId_ = "";
        errorCode_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TimeSeriesData_descriptor;
      }

      @java.lang.Override
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData getDefaultInstanceForType() {
        return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.getDefaultInstance();
      }

      @java.lang.Override
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData build() {
        com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData buildPartial() {
        com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData result = new com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData result) {
        if (valuesBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0)) {
            values_ = java.util.Collections.unmodifiableList(values_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.values_ = values_;
        } else {
          result.values_ = valuesBuilder_.build();
        }
      }

      private void buildPartial0(com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.secId_ = secId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.universe_ = universe_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.dataId_ = dataId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.errorCode_ = errorCode_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData) {
          return mergeFrom((com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData other) {
        if (other == com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.getDefaultInstance()) return this;
        if (other.hasSecId()) {
          secId_ = other.secId_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (other.hasUniverse()) {
          universe_ = other.universe_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        if (valuesBuilder_ == null) {
          if (!other.values_.isEmpty()) {
            if (values_.isEmpty()) {
              values_ = other.values_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureValuesIsMutable();
              values_.addAll(other.values_);
            }
            onChanged();
          }
        } else {
          if (!other.values_.isEmpty()) {
            if (valuesBuilder_.isEmpty()) {
              valuesBuilder_.dispose();
              valuesBuilder_ = null;
              values_ = other.values_;
              bitField0_ = (bitField0_ & ~0x00000004);
              valuesBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getValuesFieldBuilder() : null;
            } else {
              valuesBuilder_.addAllMessages(other.values_);
            }
          }
        }
        if (other.hasDataId()) {
          dataId_ = other.dataId_;
          bitField0_ |= 0x00000008;
          onChanged();
        }
        if (other.hasErrorCode()) {
          errorCode_ = other.errorCode_;
          bitField0_ |= 0x00000010;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        if (!hasSecId()) {
          return false;
        }
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                secId_ = input.readBytes();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                universe_ = input.readBytes();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair m =
                    input.readMessage(
                        com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.PARSER,
                        extensionRegistry);
                if (valuesBuilder_ == null) {
                  ensureValuesIsMutable();
                  values_.add(m);
                } else {
                  valuesBuilder_.addMessage(m);
                }
                break;
              } // case 26
              case 34: {
                dataId_ = input.readBytes();
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                errorCode_ = input.readBytes();
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object secId_ = "";
      /**
       * <code>required string SecId = 1;</code>
       * @return Whether the secId field is set.
       */
      public boolean hasSecId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>required string SecId = 1;</code>
       * @return The secId.
       */
      public java.lang.String getSecId() {
        java.lang.Object ref = secId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            secId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string SecId = 1;</code>
       * @return The bytes for secId.
       */
      public com.google.protobuf.ByteString
          getSecIdBytes() {
        java.lang.Object ref = secId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          secId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string SecId = 1;</code>
       * @param value The secId to set.
       * @return This builder for chaining.
       */
      public Builder setSecId(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        secId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>required string SecId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSecId() {
        secId_ = getDefaultInstance().getSecId();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>required string SecId = 1;</code>
       * @param value The bytes for secId to set.
       * @return This builder for chaining.
       */
      public Builder setSecIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        secId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private java.lang.Object universe_ = "";
      /**
       * <code>optional string Universe = 2;</code>
       * @return Whether the universe field is set.
       */
      public boolean hasUniverse() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional string Universe = 2;</code>
       * @return The universe.
       */
      public java.lang.String getUniverse() {
        java.lang.Object ref = universe_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            universe_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string Universe = 2;</code>
       * @return The bytes for universe.
       */
      public com.google.protobuf.ByteString
          getUniverseBytes() {
        java.lang.Object ref = universe_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          universe_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string Universe = 2;</code>
       * @param value The universe to set.
       * @return This builder for chaining.
       */
      public Builder setUniverse(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        universe_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional string Universe = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearUniverse() {
        universe_ = getDefaultInstance().getUniverse();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <code>optional string Universe = 2;</code>
       * @param value The bytes for universe to set.
       * @return This builder for chaining.
       */
      public Builder setUniverseBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        universe_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      private java.util.List<com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair> values_ =
        java.util.Collections.emptyList();
      private void ensureValuesIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          values_ = new java.util.ArrayList<com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair>(values_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.Builder, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePairOrBuilder> valuesBuilder_;

      /**
       * <code>repeated .protobuf.TSValuePair Values = 3;</code>
       */
      public java.util.List<com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair> getValuesList() {
        if (valuesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(values_);
        } else {
          return valuesBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protobuf.TSValuePair Values = 3;</code>
       */
      public int getValuesCount() {
        if (valuesBuilder_ == null) {
          return values_.size();
        } else {
          return valuesBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protobuf.TSValuePair Values = 3;</code>
       */
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair getValues(int index) {
        if (valuesBuilder_ == null) {
          return values_.get(index);
        } else {
          return valuesBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protobuf.TSValuePair Values = 3;</code>
       */
      public Builder setValues(
          int index, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair value) {
        if (valuesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureValuesIsMutable();
          values_.set(index, value);
          onChanged();
        } else {
          valuesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protobuf.TSValuePair Values = 3;</code>
       */
      public Builder setValues(
          int index, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.Builder builderForValue) {
        if (valuesBuilder_ == null) {
          ensureValuesIsMutable();
          values_.set(index, builderForValue.build());
          onChanged();
        } else {
          valuesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protobuf.TSValuePair Values = 3;</code>
       */
      public Builder addValues(com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair value) {
        if (valuesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureValuesIsMutable();
          values_.add(value);
          onChanged();
        } else {
          valuesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protobuf.TSValuePair Values = 3;</code>
       */
      public Builder addValues(
          int index, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair value) {
        if (valuesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureValuesIsMutable();
          values_.add(index, value);
          onChanged();
        } else {
          valuesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protobuf.TSValuePair Values = 3;</code>
       */
      public Builder addValues(
          com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.Builder builderForValue) {
        if (valuesBuilder_ == null) {
          ensureValuesIsMutable();
          values_.add(builderForValue.build());
          onChanged();
        } else {
          valuesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protobuf.TSValuePair Values = 3;</code>
       */
      public Builder addValues(
          int index, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.Builder builderForValue) {
        if (valuesBuilder_ == null) {
          ensureValuesIsMutable();
          values_.add(index, builderForValue.build());
          onChanged();
        } else {
          valuesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protobuf.TSValuePair Values = 3;</code>
       */
      public Builder addAllValues(
          java.lang.Iterable<? extends com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair> values) {
        if (valuesBuilder_ == null) {
          ensureValuesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, values_);
          onChanged();
        } else {
          valuesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protobuf.TSValuePair Values = 3;</code>
       */
      public Builder clearValues() {
        if (valuesBuilder_ == null) {
          values_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          valuesBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protobuf.TSValuePair Values = 3;</code>
       */
      public Builder removeValues(int index) {
        if (valuesBuilder_ == null) {
          ensureValuesIsMutable();
          values_.remove(index);
          onChanged();
        } else {
          valuesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protobuf.TSValuePair Values = 3;</code>
       */
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.Builder getValuesBuilder(
          int index) {
        return getValuesFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protobuf.TSValuePair Values = 3;</code>
       */
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePairOrBuilder getValuesOrBuilder(
          int index) {
        if (valuesBuilder_ == null) {
          return values_.get(index);  } else {
          return valuesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protobuf.TSValuePair Values = 3;</code>
       */
      public java.util.List<? extends com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePairOrBuilder> 
           getValuesOrBuilderList() {
        if (valuesBuilder_ != null) {
          return valuesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(values_);
        }
      }
      /**
       * <code>repeated .protobuf.TSValuePair Values = 3;</code>
       */
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.Builder addValuesBuilder() {
        return getValuesFieldBuilder().addBuilder(
            com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.getDefaultInstance());
      }
      /**
       * <code>repeated .protobuf.TSValuePair Values = 3;</code>
       */
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.Builder addValuesBuilder(
          int index) {
        return getValuesFieldBuilder().addBuilder(
            index, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.getDefaultInstance());
      }
      /**
       * <code>repeated .protobuf.TSValuePair Values = 3;</code>
       */
      public java.util.List<com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.Builder> 
           getValuesBuilderList() {
        return getValuesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.Builder, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePairOrBuilder> 
          getValuesFieldBuilder() {
        if (valuesBuilder_ == null) {
          valuesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.Builder, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePairOrBuilder>(
                  values_,
                  ((bitField0_ & 0x00000004) != 0),
                  getParentForChildren(),
                  isClean());
          values_ = null;
        }
        return valuesBuilder_;
      }

      private java.lang.Object dataId_ = "";
      /**
       * <code>optional string DataId = 4;</code>
       * @return Whether the dataId field is set.
       */
      public boolean hasDataId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional string DataId = 4;</code>
       * @return The dataId.
       */
      public java.lang.String getDataId() {
        java.lang.Object ref = dataId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            dataId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string DataId = 4;</code>
       * @return The bytes for dataId.
       */
      public com.google.protobuf.ByteString
          getDataIdBytes() {
        java.lang.Object ref = dataId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          dataId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string DataId = 4;</code>
       * @param value The dataId to set.
       * @return This builder for chaining.
       */
      public Builder setDataId(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        dataId_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional string DataId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearDataId() {
        dataId_ = getDefaultInstance().getDataId();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      /**
       * <code>optional string DataId = 4;</code>
       * @param value The bytes for dataId to set.
       * @return This builder for chaining.
       */
      public Builder setDataIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        dataId_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }

      private java.lang.Object errorCode_ = "";
      /**
       * <code>optional string ErrorCode = 5;</code>
       * @return Whether the errorCode field is set.
       */
      public boolean hasErrorCode() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional string ErrorCode = 5;</code>
       * @return The errorCode.
       */
      public java.lang.String getErrorCode() {
        java.lang.Object ref = errorCode_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            errorCode_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string ErrorCode = 5;</code>
       * @return The bytes for errorCode.
       */
      public com.google.protobuf.ByteString
          getErrorCodeBytes() {
        java.lang.Object ref = errorCode_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          errorCode_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string ErrorCode = 5;</code>
       * @param value The errorCode to set.
       * @return This builder for chaining.
       */
      public Builder setErrorCode(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        errorCode_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional string ErrorCode = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearErrorCode() {
        errorCode_ = getDefaultInstance().getErrorCode();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }
      /**
       * <code>optional string ErrorCode = 5;</code>
       * @param value The bytes for errorCode to set.
       * @return This builder for chaining.
       */
      public Builder setErrorCodeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        errorCode_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:protobuf.TimeSeriesData)
    }

    // @@protoc_insertion_point(class_scope:protobuf.TimeSeriesData)
    private static final com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData();
    }

    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<TimeSeriesData>
        PARSER = new com.google.protobuf.AbstractParser<TimeSeriesData>() {
      @java.lang.Override
      public TimeSeriesData parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<TimeSeriesData> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TimeSeriesData> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_protobuf_TSValuePair_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_protobuf_TSValuePair_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_protobuf_TimeSeriesDatas_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_protobuf_TimeSeriesDatas_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_protobuf_TimeSeriesData_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_protobuf_TimeSeriesData_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\021TsCacheData.proto\022\010protobuf\"=\n\013TSValue" +
      "Pair\022\r\n\005dates\030\001 \003(\003\022\016\n\006values\030\002 \003(\001\022\017\n\007s" +
      "trings\030\003 \003(\t\"Y\n\017TimeSeriesDatas\022(\n\006Value" +
      "s\030e \003(\0132\030.protobuf.TimeSeriesData\022\017\n\007ret" +
      "code\030f \001(\003\022\013\n\003msg\030g \001(\t\"{\n\016TimeSeriesDat" +
      "a\022\r\n\005SecId\030\001 \002(\t\022\020\n\010Universe\030\002 \001(\t\022%\n\006Va" +
      "lues\030\003 \003(\0132\025.protobuf.TSValuePair\022\016\n\006Dat" +
      "aId\030\004 \001(\t\022\021\n\tErrorCode\030\005 \001(\tB\\\<EMAIL>" +
      "ingstar.martgateway.domains.tscacheproxy" +
      ".entity.protobufB\026TsCacheDataForProtoBuf" +
      "H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_protobuf_TSValuePair_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_protobuf_TSValuePair_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_protobuf_TSValuePair_descriptor,
        new java.lang.String[] { "Dates", "Values", "Strings", });
    internal_static_protobuf_TimeSeriesDatas_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_protobuf_TimeSeriesDatas_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_protobuf_TimeSeriesDatas_descriptor,
        new java.lang.String[] { "Values", "Retcode", "Msg", });
    internal_static_protobuf_TimeSeriesData_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_protobuf_TimeSeriesData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_protobuf_TimeSeriesData_descriptor,
        new java.lang.String[] { "SecId", "Universe", "Values", "DataId", "ErrorCode", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
