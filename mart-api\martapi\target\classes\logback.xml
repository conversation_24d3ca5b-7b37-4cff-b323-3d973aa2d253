<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-4relative %-5level [%-15.15thread] : service_name="Mart API" request_id=%X{request_id} %msg %n%rEx{full,
				java.lang.reflect.Method,
				org.apache.catalina,
				org.springframework.aop,
				org.springframework.security,
				org.springframework.transaction,
				org.springframework.web,
				reactor.core,
				io.netty,
				com.sun.org.apache,
				com.morningstar
				}</pattern>
		</encoder>
	</appender>
	<appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
		<appender-ref ref="STDOUT" />
		<queueSize>600</queueSize>
		<discardingThreshold>0</discardingThreshold>
	</appender>
	<logger name="com.morningstar" level="INFO" />
	<logger name="org.springframework" level="INFO" />
	<logger name="reactor.ipc.netty.http" level="ERROR" />
	<root level="INFO" includeLocation="false">
		<appender-ref ref="ASYNC" />
	</root>
	<logger name="org.redisson" level="INFO" />
	<logger name="org.mybatis"><level value="TRACE"/></logger>
	<logger name="java.sql"><level value="INFO"/></logger>
	<logger name="com.morningstar.martgateway.gateway.rdb.sql" level="DEBUG"/>
</configuration>
