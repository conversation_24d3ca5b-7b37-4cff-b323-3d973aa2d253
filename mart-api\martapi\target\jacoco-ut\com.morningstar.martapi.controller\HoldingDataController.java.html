<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>HoldingDataController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <a href="index.source.html" class="el_package">com.morningstar.martapi.controller</a> &gt; <span class="el_source">HoldingDataController.java</span></div><h1>HoldingDataController.java</h1><pre class="source lang-java linenums">package com.morningstar.martapi.controller;

import com.morningstar.martgateway.domains.core.entity.holdingresponse.HoldingResponse;
import com.morningstar.martgateway.infrastructures.log.LogAttribute;
import com.morningstar.martgateway.infrastructures.log.LogEntity;
import com.morningstar.martgateway.infrastructures.log.LogEntry;
import com.morningstar.martgateway.infrastructures.log.LogHelper;
import com.morningstar.martgateway.interfaces.MartGateway;
import com.morningstar.martcommon.entity.result.request.IdDatePair;
import com.morningstar.martcommon.entity.result.request.MartRequest;
import io.swagger.annotations.ApiParam;
import org.slf4j.MDC;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import javax.inject.Inject;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

import static com.morningstar.martapi.config.Constant.RESPONSE;
import static com.morningstar.martapi.config.Constant.RESPONSE_ERROR;
import static com.morningstar.martapi.config.Constant.SUCCESSFUL_RESPONSE;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.EVENT_DESCRIPTION;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.EVENT_TYPE;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.EXCEPTION_TYPE;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.EXECUTE_TIME;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.PRODUCT_ID;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.REQUEST_PARAM;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.USER_ID;

@RestController
@RequestMapping(value = &quot;/v1/holding-data&quot;,
        produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE})
public class HoldingDataController {
    private final MartGateway&lt;HoldingResponse, MartRequest&gt; holdingDataGateway;

    @Inject
<span class="nc" id="L46">    public HoldingDataController(MartGateway&lt;HoldingResponse, MartRequest&gt; holdingDataGateway) {</span>
<span class="nc" id="L47">        this.holdingDataGateway = holdingDataGateway;</span>
<span class="nc" id="L48">    }</span>

    @GetMapping(value = &quot;/investment-id/{id}&quot;)
    public Mono&lt;?&gt; getData(
            @ApiParam(example = &quot;0P00000AWG&quot;)
            @PathVariable(&quot;id&quot;) String id,
            @ApiParam(example = &quot;PMP03&quot;)
            @RequestParam(value = &quot;dps&quot;, required = true) String dpList,
            @RequestParam(value = &quot;top&quot;, required = false, defaultValue = &quot;10&quot;) Integer top,
            @RequestParam(value = &quot;readCache&quot;, required = false, defaultValue = &quot;false&quot;) String readCache,
            @RequestHeader(value = &quot;X-API-UserId&quot;, required = false, defaultValue = &quot;&quot;) String userId,
            @RequestHeader(value = &quot;X-API-RequestId&quot;, required = false, defaultValue = &quot;&quot;) String requestId,
            @RequestHeader(value = &quot;X-Api-ProductId&quot;, required = false, defaultValue = &quot;&quot;) String productId,
            ServerHttpRequest request) {
<span class="nc" id="L62">        long startTime = System.currentTimeMillis();</span>

<span class="nc" id="L64">        IdDatePair idDate = new IdDatePair();</span>
<span class="nc" id="L65">        idDate.setId(id);</span>
<span class="nc" id="L66">        idDate.setDates(Arrays.asList(LocalDate.now()));</span>

<span class="nc" id="L68">        MartRequest martRequest = MartRequest.builder()</span>
<span class="nc" id="L69">                .dps(Arrays.asList(dpList.split(&quot;,&quot;)))</span>
<span class="nc" id="L70">                .idPairs(List.of(idDate))</span>
<span class="nc" id="L71">                .readCache(readCache)</span>
<span class="nc" id="L72">                .productId(productId)</span>
<span class="nc" id="L73">                .requestId(requestId)</span>
<span class="nc" id="L74">                .userId(userId)</span>
<span class="nc" id="L75">                .top(top)</span>
<span class="nc" id="L76">                .build();</span>

<span class="nc" id="L78">        return holdingDataGateway.asyncRetrieveSecurities(martRequest).doOnEach(LogHelper.logOnNext(list -&gt; {</span>
<span class="nc" id="L79">            MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), requestId);</span>
<span class="nc" id="L80">            LogEntry.info(new LogEntity(EVENT_TYPE, RESPONSE),</span>
                    new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                    new LogEntity(PRODUCT_ID, productId),
                    new LogEntity(USER_ID, userId),
<span class="nc" id="L84">                    new LogEntity(REQUEST_PARAM, martRequest.getRequestParam(request.getURI().getPath(), request.getURI().getQuery())),</span>
<span class="nc" id="L85">                    new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime));</span>
<span class="nc" id="L86">        })).doOnEach(LogHelper.logOnError(e -&gt; {</span>
<span class="nc" id="L87">            MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), requestId);</span>
<span class="nc" id="L88">            LogEntry.error(</span>
                    new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                    new LogEntity(EVENT_DESCRIPTION, e),
<span class="nc" id="L91">                    new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime),</span>
<span class="nc" id="L92">                    new LogEntity(EXCEPTION_TYPE, e.getClass()),</span>
                    new LogEntity(PRODUCT_ID, productId),
                    new LogEntity(USER_ID, userId),
<span class="nc" id="L95">                    new LogEntity(REQUEST_PARAM, martRequest.getRequestParam(request.getURI().getPath(), request.getURI().getQuery()))</span>
            );
<span class="nc" id="L97">        }));</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>