<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.morningstar.martapi.config</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <span class="el_package">com.morningstar.martapi.config</span></div><h1>com.morningstar.martapi.config</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">586 of 586</td><td class="ctr2">0%</td><td class="bar">0 of 0</td><td class="ctr2">n/a</td><td class="ctr1">30</td><td class="ctr2">30</td><td class="ctr1">89</td><td class="ctr2">89</td><td class="ctr1">30</td><td class="ctr2">30</td><td class="ctr1">7</td><td class="ctr2">7</td></tr></tfoot><tbody><tr><td id="a5"><a href="ValidatorsConfig.html" class="el_class">ValidatorsConfig</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="337" alt="337"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"/><td class="ctr2" id="e0">n/a</td><td class="ctr1" id="f0">12</td><td class="ctr2" id="g0">12</td><td class="ctr1" id="h0">32</td><td class="ctr2" id="i0">32</td><td class="ctr1" id="j0">12</td><td class="ctr2" id="k0">12</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a3"><a href="RedisMessageConfig.html" class="el_class">RedisMessageConfig</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="91" alt="91"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"/><td class="ctr2" id="e1">n/a</td><td class="ctr1" id="f3">3</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h1">17</td><td class="ctr2" id="i1">17</td><td class="ctr1" id="j3">3</td><td class="ctr2" id="k3">3</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a0"><a href="AsyncApiConfig.html" class="el_class">AsyncApiConfig</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="57" alt="57"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f1">5</td><td class="ctr2" id="g1">5</td><td class="ctr1" id="h2">15</td><td class="ctr2" id="i2">15</td><td class="ctr1" id="j1">5</td><td class="ctr2" id="k1">5</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a4"><a href="SwaggerConfig.html" class="el_class">SwaggerConfig</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="42" alt="42"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h3">9</td><td class="ctr2" id="i3">9</td><td class="ctr1" id="j4">2</td><td class="ctr2" id="k4">2</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a6"><a href="WebFluxConfig.html" class="el_class">WebFluxConfig</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="25" alt="25"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h4">9</td><td class="ctr2" id="i4">9</td><td class="ctr1" id="j5">2</td><td class="ctr2" id="k5">2</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a2"><a href="NettyWebServerFactoryHeaderSizeCustomizer.html" class="el_class">NettyWebServerFactoryHeaderSizeCustomizer</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="21" alt="21"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f2">4</td><td class="ctr2" id="g2">4</td><td class="ctr1" id="h5">4</td><td class="ctr2" id="i5">4</td><td class="ctr1" id="j2">4</td><td class="ctr2" id="k2">4</td><td class="ctr1" id="l5">1</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a1"><a href="Constant.html" class="el_class">Constant</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="13" alt="13"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h6">3</td><td class="ctr2" id="i6">3</td><td class="ctr1" id="j6">2</td><td class="ctr2" id="k6">2</td><td class="ctr1" id="l6">1</td><td class="ctr2" id="m6">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>