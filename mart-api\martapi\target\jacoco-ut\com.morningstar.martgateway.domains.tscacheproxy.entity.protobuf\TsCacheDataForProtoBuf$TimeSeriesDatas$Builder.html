<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TsCacheDataForProtoBuf.TimeSeriesDatas.Builder</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <a href="index.html" class="el_package">com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf</a> &gt; <span class="el_class">TsCacheDataForProtoBuf.TimeSeriesDatas.Builder</span></div><h1>TsCacheDataForProtoBuf.TimeSeriesDatas.Builder</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">902 of 902</td><td class="ctr2">0%</td><td class="bar">105 of 105</td><td class="ctr2">0%</td><td class="ctr1">107</td><td class="ctr2">107</td><td class="ctr1">251</td><td class="ctr2">251</td><td class="ctr1">53</td><td class="ctr2">53</td></tr></tfoot><tbody><tr><td id="a40"><a href="TsCacheDataForProtoBuf.java.html#L1545" class="el_method">mergeFrom(TsCacheDataForProtoBuf.TimeSeriesDatas)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="109" alt="109"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="18" alt="18"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">10</td><td class="ctr2" id="g0">10</td><td class="ctr1" id="h0">28</td><td class="ctr2" id="i0">28</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a38"><a href="TsCacheDataForProtoBuf.java.html#L1600" class="el_method">mergeFrom(CodedInputStream, ExtensionRegistryLite)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="88" height="10" title="80" alt="80"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="86" height="10" title="13" alt="13"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">9</td><td class="ctr2" id="g1">9</td><td class="ctr1" id="h1">28</td><td class="ctr2" id="i1">28</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a10"><a href="TsCacheDataForProtoBuf.java.html#L1489" class="el_method">buildPartial0(TsCacheDataForProtoBuf.TimeSeriesDatas)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="39" height="10" title="36" alt="36"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">3</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h2">10</td><td class="ctr2" id="i2">10</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a12"><a href="TsCacheDataForProtoBuf.java.html#L1433" class="el_method">clear()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="33" alt="33"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f10">2</td><td class="ctr2" id="g10">2</td><td class="ctr1" id="h3">10</td><td class="ctr2" id="i3">10</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a11"><a href="TsCacheDataForProtoBuf.java.html#L1477" class="el_method">buildPartialRepeatedFields(TsCacheDataForProtoBuf.TimeSeriesDatas)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="30" alt="30"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f3">3</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h8">7</td><td class="ctr2" id="i8">7</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a49"><a href="TsCacheDataForProtoBuf.java.html#L1698" class="el_method">setValues(int, TsCacheDataForProtoBuf.TimeSeriesData)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="28" alt="28"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f4">3</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h4">8</td><td class="ctr2" id="i4">8</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a30"><a href="TsCacheDataForProtoBuf.java.html#L1879" class="el_method">getValuesFieldBuilder()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="28" alt="28"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f5">3</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h10">6</td><td class="ctr2" id="i10">6</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a2"><a href="TsCacheDataForProtoBuf.java.html#L1745" class="el_method">addValues(int, TsCacheDataForProtoBuf.TimeSeriesData)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="29" height="10" title="27" alt="27"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f6">3</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h5">8</td><td class="ctr2" id="i5">8</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a4"><a href="TsCacheDataForProtoBuf.java.html#L1728" class="el_method">addValues(TsCacheDataForProtoBuf.TimeSeriesData)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="26" alt="26"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f7">3</td><td class="ctr2" id="g7">3</td><td class="ctr1" id="h6">8</td><td class="ctr2" id="i6">8</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a50"><a href="TsCacheDataForProtoBuf.java.html#L1715" class="el_method">setValues(int, TsCacheDataForProtoBuf.TimeSeriesData.Builder)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="24" alt="24"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f11">2</td><td class="ctr2" id="g11">2</td><td class="ctr1" id="h11">6</td><td class="ctr2" id="i11">6</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a3"><a href="TsCacheDataForProtoBuf.java.html#L1776" class="el_method">addValues(int, TsCacheDataForProtoBuf.TimeSeriesData.Builder)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="23" alt="23"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f12">2</td><td class="ctr2" id="g12">2</td><td class="ctr1" id="h12">6</td><td class="ctr2" id="i12">6</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a23"><a href="TsCacheDataForProtoBuf.java.html#L1944" class="el_method">getMsg()</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="23" alt="23"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f8">3</td><td class="ctr2" id="g8">3</td><td class="ctr1" id="h7">8</td><td class="ctr2" id="i7">8</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a5"><a href="TsCacheDataForProtoBuf.java.html#L1762" class="el_method">addValues(TsCacheDataForProtoBuf.TimeSeriesData.Builder)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="22" alt="22"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d13"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f13">2</td><td class="ctr2" id="g13">2</td><td class="ctr1" id="h13">6</td><td class="ctr2" id="i13">6</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a17"><a href="TsCacheDataForProtoBuf.java.html#L1804" class="el_method">clearValues()</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="20" alt="20"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d14"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e13">0%</td><td class="ctr1" id="f14">2</td><td class="ctr2" id="g14">2</td><td class="ctr1" id="h14">6</td><td class="ctr2" id="i14">6</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a19"><a href="TsCacheDataForProtoBuf.java.html#L1654" class="el_method">ensureValuesIsMutable()</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="19" alt="19"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d15"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e14">0%</td><td class="ctr1" id="f15">2</td><td class="ctr2" id="g15">2</td><td class="ctr1" id="h22">4</td><td class="ctr2" id="i22">4</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a0"><a href="TsCacheDataForProtoBuf.java.html#L1790" class="el_method">addAllValues(Iterable)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="19" alt="19"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d16"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e15">0%</td><td class="ctr1" id="f16">2</td><td class="ctr2" id="g16">2</td><td class="ctr1" id="h15">6</td><td class="ctr2" id="i15">6</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a42"><a href="TsCacheDataForProtoBuf.java.html#L1817" class="el_method">removeValues(int)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="19" alt="19"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d17"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e16">0%</td><td class="ctr1" id="f17">2</td><td class="ctr2" id="g17">2</td><td class="ctr1" id="h16">6</td><td class="ctr2" id="i16">6</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a44"><a href="TsCacheDataForProtoBuf.java.html#L1981" class="el_method">setMsg(String)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="19" alt="19"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d18"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e17">0%</td><td class="ctr1" id="f18">2</td><td class="ctr2" id="g18">2</td><td class="ctr1" id="h17">5</td><td class="ctr2" id="i17">5</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a45"><a href="TsCacheDataForProtoBuf.java.html#L2004" class="el_method">setMsgBytes(ByteString)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="19" alt="19"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d19"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e18">0%</td><td class="ctr1" id="f19">2</td><td class="ctr2" id="g19">2</td><td class="ctr1" id="h18">5</td><td class="ctr2" id="i18">5</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a9"><a href="TsCacheDataForProtoBuf.java.html#L1469" class="el_method">buildPartial()</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="18" alt="18"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d20"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e19">0%</td><td class="ctr1" id="f20">2</td><td class="ctr2" id="g20">2</td><td class="ctr1" id="h19">5</td><td class="ctr2" id="i19">5</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a24"><a href="TsCacheDataForProtoBuf.java.html#L1963" class="el_method">getMsgBytes()</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="18" alt="18"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d21"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e20">0%</td><td class="ctr1" id="f21">2</td><td class="ctr2" id="g21">2</td><td class="ctr1" id="h9">7</td><td class="ctr2" id="i9">7</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a37"><a href="TsCacheDataForProtoBuf.java.html#L1587" class="el_method">isInitialized()</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="17" alt="17"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e21">0%</td><td class="ctr1" id="f9">3</td><td class="ctr2" id="g9">3</td><td class="ctr1" id="h23">4</td><td class="ctr2" id="i23">4</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a26"><a href="TsCacheDataForProtoBuf.java.html#L1687" class="el_method">getValues(int)</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="15" alt="15"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d22"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e22">0%</td><td class="ctr1" id="f22">2</td><td class="ctr2" id="g22">2</td><td class="ctr1" id="h29">3</td><td class="ctr2" id="i29">3</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a32"><a href="TsCacheDataForProtoBuf.java.html#L1838" class="el_method">getValuesOrBuilder(int)</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="15" alt="15"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d23"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e23">0%</td><td class="ctr1" id="f23">2</td><td class="ctr2" id="g23">2</td><td class="ctr1" id="h30">3</td><td class="ctr2" id="i30">3</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a39"><a href="TsCacheDataForProtoBuf.java.html#L1536" class="el_method">mergeFrom(Message)</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="14" alt="14"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d24"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e24">0%</td><td class="ctr1" id="f24">2</td><td class="ctr2" id="g24">2</td><td class="ctr1" id="h24">4</td><td class="ctr2" id="i24">4</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a14"><a href="TsCacheDataForProtoBuf.java.html#L1992" class="el_method">clearMsg()</a></td><td class="bar" id="b25"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="14" alt="14"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d31"/><td class="ctr2" id="e31">n/a</td><td class="ctr1" id="f31">1</td><td class="ctr2" id="g31">1</td><td class="ctr1" id="h25">4</td><td class="ctr2" id="i25">4</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a47"><a href="TsCacheDataForProtoBuf.java.html#L1915" class="el_method">setRetcode(long)</a></td><td class="bar" id="b26"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="13" alt="13"/></td><td class="ctr2" id="c26">0%</td><td class="bar" id="d32"/><td class="ctr2" id="e32">n/a</td><td class="ctr1" id="f32">1</td><td class="ctr2" id="g32">1</td><td class="ctr1" id="h26">4</td><td class="ctr2" id="i26">4</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a16"><a href="TsCacheDataForProtoBuf.java.html#L1925" class="el_method">clearRetcode()</a></td><td class="bar" id="b27"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="13" alt="13"/></td><td class="ctr2" id="c27">0%</td><td class="bar" id="d33"/><td class="ctr2" id="e33">n/a</td><td class="ctr1" id="f33">1</td><td class="ctr2" id="g33">1</td><td class="ctr1" id="h27">4</td><td class="ctr2" id="i27">4</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a8"><a href="TsCacheDataForProtoBuf.java.html#L1460" class="el_method">build()</a></td><td class="bar" id="b28"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="11" alt="11"/></td><td class="ctr2" id="c28">0%</td><td class="bar" id="d25"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e25">0%</td><td class="ctr1" id="f25">2</td><td class="ctr2" id="g25">2</td><td class="ctr1" id="h28">4</td><td class="ctr2" id="i28">4</td><td class="ctr1" id="j28">1</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a31"><a href="TsCacheDataForProtoBuf.java.html#L1667" class="el_method">getValuesList()</a></td><td class="bar" id="b29"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="11" alt="11"/></td><td class="ctr2" id="c29">0%</td><td class="bar" id="d26"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e26">0%</td><td class="ctr1" id="f26">2</td><td class="ctr2" id="g26">2</td><td class="ctr1" id="h31">3</td><td class="ctr2" id="i31">3</td><td class="ctr1" id="j29">1</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a29"><a href="TsCacheDataForProtoBuf.java.html#L1677" class="el_method">getValuesCount()</a></td><td class="bar" id="b30"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="11" alt="11"/></td><td class="ctr2" id="c30">0%</td><td class="bar" id="d27"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e27">0%</td><td class="ctr1" id="f27">2</td><td class="ctr2" id="g27">2</td><td class="ctr1" id="h32">3</td><td class="ctr2" id="i32">3</td><td class="ctr1" id="j30">1</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a33"><a href="TsCacheDataForProtoBuf.java.html#L1848" class="el_method">getValuesOrBuilderList()</a></td><td class="bar" id="b31"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="11" alt="11"/></td><td class="ctr2" id="c31">0%</td><td class="bar" id="d28"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e28">0%</td><td class="ctr1" id="f28">2</td><td class="ctr2" id="g28">2</td><td class="ctr1" id="h33">3</td><td class="ctr2" id="i33">3</td><td class="ctr1" id="j31">1</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a52"><a href="TsCacheDataForProtoBuf.java.html#L1428" class="el_method">TsCacheDataForProtoBuf.TimeSeriesDatas.Builder(GeneratedMessageV3.BuilderParent)</a></td><td class="bar" id="b32"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="10" alt="10"/></td><td class="ctr2" id="c32">0%</td><td class="bar" id="d34"/><td class="ctr2" id="e34">n/a</td><td class="ctr1" id="f34">1</td><td class="ctr2" id="g34">1</td><td class="ctr1" id="h20">5</td><td class="ctr2" id="i20">5</td><td class="ctr1" id="j32">1</td><td class="ctr2" id="k32">1</td></tr><tr><td id="a51"><a href="TsCacheDataForProtoBuf.java.html#L1422" class="el_method">TsCacheDataForProtoBuf.TimeSeriesDatas.Builder()</a></td><td class="bar" id="b33"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="9" alt="9"/></td><td class="ctr2" id="c33">0%</td><td class="bar" id="d35"/><td class="ctr2" id="e35">n/a</td><td class="ctr1" id="f35">1</td><td class="ctr2" id="g35">1</td><td class="ctr1" id="h21">5</td><td class="ctr2" id="i21">5</td><td class="ctr1" id="j33">1</td><td class="ctr2" id="k33">1</td></tr><tr><td id="a35"><a href="TsCacheDataForProtoBuf.java.html#L1898" class="el_method">hasRetcode()</a></td><td class="bar" id="b34"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="9" alt="9"/></td><td class="ctr2" id="c34">0%</td><td class="bar" id="d29"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e29">0%</td><td class="ctr1" id="f29">2</td><td class="ctr2" id="g29">2</td><td class="ctr1" id="h37">1</td><td class="ctr2" id="i37">1</td><td class="ctr1" id="j34">1</td><td class="ctr2" id="k34">1</td></tr><tr><td id="a34"><a href="TsCacheDataForProtoBuf.java.html#L1937" class="el_method">hasMsg()</a></td><td class="bar" id="b35"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="9" alt="9"/></td><td class="ctr2" id="c35">0%</td><td class="bar" id="d30"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e30">0%</td><td class="ctr1" id="f30">2</td><td class="ctr2" id="g30">2</td><td class="ctr1" id="h38">1</td><td class="ctr2" id="i38">1</td><td class="ctr1" id="j35">1</td><td class="ctr2" id="k35">1</td></tr><tr><td id="a46"><a href="TsCacheDataForProtoBuf.java.html#L1526" class="el_method">setRepeatedField(Descriptors.FieldDescriptor, int, Object)</a></td><td class="bar" id="b36"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="7" alt="7"/></td><td class="ctr2" id="c36">0%</td><td class="bar" id="d36"/><td class="ctr2" id="e36">n/a</td><td class="ctr1" id="f36">1</td><td class="ctr2" id="g36">1</td><td class="ctr1" id="h39">1</td><td class="ctr2" id="i39">1</td><td class="ctr1" id="j36">1</td><td class="ctr2" id="k36">1</td></tr><tr><td id="a7"><a href="TsCacheDataForProtoBuf.java.html#L1866" class="el_method">addValuesBuilder(int)</a></td><td class="bar" id="b37"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="7" alt="7"/></td><td class="ctr2" id="c37">0%</td><td class="bar" id="d37"/><td class="ctr2" id="e37">n/a</td><td class="ctr1" id="f37">1</td><td class="ctr2" id="g37">1</td><td class="ctr1" id="h34">2</td><td class="ctr2" id="i34">2</td><td class="ctr1" id="j37">1</td><td class="ctr2" id="k37">1</td></tr><tr><td id="a43"><a href="TsCacheDataForProtoBuf.java.html#L1510" class="el_method">setField(Descriptors.FieldDescriptor, Object)</a></td><td class="bar" id="b38"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="6" alt="6"/></td><td class="ctr2" id="c38">0%</td><td class="bar" id="d38"/><td class="ctr2" id="e38">n/a</td><td class="ctr1" id="f38">1</td><td class="ctr2" id="g38">1</td><td class="ctr1" id="h40">1</td><td class="ctr2" id="i40">1</td><td class="ctr1" id="j38">1</td><td class="ctr2" id="k38">1</td></tr><tr><td id="a1"><a href="TsCacheDataForProtoBuf.java.html#L1532" class="el_method">addRepeatedField(Descriptors.FieldDescriptor, Object)</a></td><td class="bar" id="b39"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="6" alt="6"/></td><td class="ctr2" id="c39">0%</td><td class="bar" id="d39"/><td class="ctr2" id="e39">n/a</td><td class="ctr1" id="f39">1</td><td class="ctr2" id="g39">1</td><td class="ctr1" id="h41">1</td><td class="ctr2" id="i41">1</td><td class="ctr1" id="j39">1</td><td class="ctr2" id="k39">1</td></tr><tr><td id="a27"><a href="TsCacheDataForProtoBuf.java.html#L1831" class="el_method">getValuesBuilder(int)</a></td><td class="bar" id="b40"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="6" alt="6"/></td><td class="ctr2" id="c40">0%</td><td class="bar" id="d40"/><td class="ctr2" id="e40">n/a</td><td class="ctr1" id="f40">1</td><td class="ctr2" id="g40">1</td><td class="ctr1" id="h42">1</td><td class="ctr2" id="i42">1</td><td class="ctr1" id="j40">1</td><td class="ctr2" id="k40">1</td></tr><tr><td id="a6"><a href="TsCacheDataForProtoBuf.java.html#L1858" class="el_method">addValuesBuilder()</a></td><td class="bar" id="b41"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="6" alt="6"/></td><td class="ctr2" id="c41">0%</td><td class="bar" id="d41"/><td class="ctr2" id="e41">n/a</td><td class="ctr1" id="f41">1</td><td class="ctr2" id="g41">1</td><td class="ctr1" id="h35">2</td><td class="ctr2" id="i35">2</td><td class="ctr1" id="j41">1</td><td class="ctr2" id="k41">1</td></tr><tr><td id="a36"><a href="TsCacheDataForProtoBuf.java.html#L1416" class="el_method">internalGetFieldAccessorTable()</a></td><td class="bar" id="b42"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="5" alt="5"/></td><td class="ctr2" id="c42">0%</td><td class="bar" id="d42"/><td class="ctr2" id="e42">n/a</td><td class="ctr1" id="f42">1</td><td class="ctr2" id="g42">1</td><td class="ctr1" id="h36">2</td><td class="ctr2" id="i36">2</td><td class="ctr1" id="j42">1</td><td class="ctr2" id="k42">1</td></tr><tr><td id="a13"><a href="TsCacheDataForProtoBuf.java.html#L1515" class="el_method">clearField(Descriptors.FieldDescriptor)</a></td><td class="bar" id="b43"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="5" alt="5"/></td><td class="ctr2" id="c43">0%</td><td class="bar" id="d43"/><td class="ctr2" id="e43">n/a</td><td class="ctr1" id="f43">1</td><td class="ctr2" id="g43">1</td><td class="ctr1" id="h43">1</td><td class="ctr2" id="i43">1</td><td class="ctr1" id="j43">1</td><td class="ctr2" id="k43">1</td></tr><tr><td id="a15"><a href="TsCacheDataForProtoBuf.java.html#L1520" class="el_method">clearOneof(Descriptors.OneofDescriptor)</a></td><td class="bar" id="b44"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="5" alt="5"/></td><td class="ctr2" id="c44">0%</td><td class="bar" id="d44"/><td class="ctr2" id="e44">n/a</td><td class="ctr1" id="f44">1</td><td class="ctr2" id="g44">1</td><td class="ctr1" id="h44">1</td><td class="ctr2" id="i44">1</td><td class="ctr1" id="j44">1</td><td class="ctr2" id="k44">1</td></tr><tr><td id="a48"><a href="TsCacheDataForProtoBuf.java.html#L2013" class="el_method">setUnknownFields(UnknownFieldSet)</a></td><td class="bar" id="b45"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="5" alt="5"/></td><td class="ctr2" id="c45">0%</td><td class="bar" id="d45"/><td class="ctr2" id="e45">n/a</td><td class="ctr1" id="f45">1</td><td class="ctr2" id="g45">1</td><td class="ctr1" id="h45">1</td><td class="ctr2" id="i45">1</td><td class="ctr1" id="j45">1</td><td class="ctr2" id="k45">1</td></tr><tr><td id="a41"><a href="TsCacheDataForProtoBuf.java.html#L2019" class="el_method">mergeUnknownFields(UnknownFieldSet)</a></td><td class="bar" id="b46"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="5" alt="5"/></td><td class="ctr2" id="c46">0%</td><td class="bar" id="d46"/><td class="ctr2" id="e46">n/a</td><td class="ctr1" id="f46">1</td><td class="ctr2" id="g46">1</td><td class="ctr1" id="h46">1</td><td class="ctr2" id="i46">1</td><td class="ctr1" id="j46">1</td><td class="ctr2" id="k46">1</td></tr><tr><td id="a18"><a href="TsCacheDataForProtoBuf.java.html#L1504" class="el_method">clone()</a></td><td class="bar" id="b47"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="4" alt="4"/></td><td class="ctr2" id="c47">0%</td><td class="bar" id="d47"/><td class="ctr2" id="e47">n/a</td><td class="ctr1" id="f47">1</td><td class="ctr2" id="g47">1</td><td class="ctr1" id="h47">1</td><td class="ctr2" id="i47">1</td><td class="ctr1" id="j47">1</td><td class="ctr2" id="k47">1</td></tr><tr><td id="a28"><a href="TsCacheDataForProtoBuf.java.html#L1874" class="el_method">getValuesBuilderList()</a></td><td class="bar" id="b48"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="4" alt="4"/></td><td class="ctr2" id="c48">0%</td><td class="bar" id="d48"/><td class="ctr2" id="e48">n/a</td><td class="ctr1" id="f48">1</td><td class="ctr2" id="g48">1</td><td class="ctr1" id="h48">1</td><td class="ctr2" id="i48">1</td><td class="ctr1" id="j48">1</td><td class="ctr2" id="k48">1</td></tr><tr><td id="a25"><a href="TsCacheDataForProtoBuf.java.html#L1906" class="el_method">getRetcode()</a></td><td class="bar" id="b49"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="3" alt="3"/></td><td class="ctr2" id="c49">0%</td><td class="bar" id="d49"/><td class="ctr2" id="e49">n/a</td><td class="ctr1" id="f49">1</td><td class="ctr2" id="g49">1</td><td class="ctr1" id="h49">1</td><td class="ctr2" id="i49">1</td><td class="ctr1" id="j49">1</td><td class="ctr2" id="k49">1</td></tr><tr><td id="a21"><a href="TsCacheDataForProtoBuf.java.html#L1410" class="el_method">getDescriptor()</a></td><td class="bar" id="b50"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="c50">0%</td><td class="bar" id="d50"/><td class="ctr2" id="e50">n/a</td><td class="ctr1" id="f50">1</td><td class="ctr2" id="g50">1</td><td class="ctr1" id="h50">1</td><td class="ctr2" id="i50">1</td><td class="ctr1" id="j50">1</td><td class="ctr2" id="k50">1</td></tr><tr><td id="a22"><a href="TsCacheDataForProtoBuf.java.html#L1450" class="el_method">getDescriptorForType()</a></td><td class="bar" id="b51"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="c51">0%</td><td class="bar" id="d51"/><td class="ctr2" id="e51">n/a</td><td class="ctr1" id="f51">1</td><td class="ctr2" id="g51">1</td><td class="ctr1" id="h51">1</td><td class="ctr2" id="i51">1</td><td class="ctr1" id="j51">1</td><td class="ctr2" id="k51">1</td></tr><tr><td id="a20"><a href="TsCacheDataForProtoBuf.java.html#L1455" class="el_method">getDefaultInstanceForType()</a></td><td class="bar" id="b52"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="c52">0%</td><td class="bar" id="d52"/><td class="ctr2" id="e52">n/a</td><td class="ctr1" id="f52">1</td><td class="ctr2" id="g52">1</td><td class="ctr1" id="h52">1</td><td class="ctr2" id="i52">1</td><td class="ctr1" id="j52">1</td><td class="ctr2" id="k52">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>