option optimize_for = SPEED;
package protobuf;

option java_package = "com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf";
option java_outer_classname = "TsCacheDataForProtoBuf";

message TSValuePair {
  repeated int64         dates          = 1;
  repeated double        values          = 2;
  repeated string        strings          = 3;
}


message TimeSeriesDatas
{
    repeated TimeSeriesData Values   = 101;
    optional int64 retcode = 102;
    optional string msg = 103;
}

message TimeSeriesData
{
 required string        SecId         = 1;
 optional string        Universe        = 2;
 repeated TSValuePair       Values      = 3;
 optional string        DataId        = 4;
 optional string        ErrorCode     = 5;
}
