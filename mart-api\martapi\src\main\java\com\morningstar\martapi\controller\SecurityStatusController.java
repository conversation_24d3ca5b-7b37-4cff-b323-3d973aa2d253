package com.morningstar.martapi.controller;

import com.morningstar.martcommon.entity.result.request.IdMapper;
import com.morningstar.martgateway.domains.core.entity.response.MartData;
import com.morningstar.martgateway.domains.core.entity.response.MartResponse;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.martgateway.util.IdMapUtil;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.inject.Inject;
import java.util.Arrays;
import springfox.documentation.annotations.ApiIgnore;

@RestController
@RequestMapping(value = {"/v1/security-status","/investment-api/v1/security-status"},
        produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE})
@ApiIgnore
public class SecurityStatusController {

    private IdMapUtil idMapUtil;

    @Inject
    public SecurityStatusController(IdMapUtil idMapUtil) {
        this.idMapUtil = idMapUtil;
    }
    @GetMapping(value = "/{idList}")
    public Mono<MartResponse> checkSecurityStatus(@PathVariable("idList") String idList) {
        return Flux.fromIterable(idMapUtil.getIdMappers(Arrays.asList(idList.split(","))))
                .map(IdMapper::transformToResponse)
                .collectList()
                .map(rList -> new MartResponse(Status.OK, new MartData(rList)));
    }
}
