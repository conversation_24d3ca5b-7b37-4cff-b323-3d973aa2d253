<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>IndexDataController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <a href="index.source.html" class="el_package">com.morningstar.martapi.controller</a> &gt; <span class="el_source">IndexDataController.java</span></div><h1>IndexDataController.java</h1><pre class="source lang-java linenums">package com.morningstar.martapi.controller;

import com.morningstar.martapi.exception.IndexAPIException;
import com.morningstar.martapi.util.InvestmentApiRequestUtil;
import com.morningstar.martapi.util.TokenUtil;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.index.entity.IndexDataResult;
import com.morningstar.martgateway.domains.index.service.IndexDataService;
import com.morningstar.martgateway.interfaces.model.investmentapi.InvestmentApiRequest;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;
import reactor.util.Logger;
import reactor.util.Loggers;

import javax.inject.Inject;

@RestController
@RequestMapping(value = {&quot;/v1/reference-data/&quot;,&quot;/investment-api/v1/reference-data/&quot;},
        produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE})
public class IndexDataController {

    private final IndexDataService indexDataService;
    private final RequestValidationHandler&lt;HeadersAndParams, InvestmentApiRequest&gt; validator;
<span class="nc" id="L28">    private static final Logger log = Loggers.getLogger(IndexDataController.class);</span>

    @Inject
<span class="nc" id="L31">    public IndexDataController(IndexDataService indexDataService, @Qualifier(&quot;investmentApiValidator&quot;) RequestValidationHandler&lt;HeadersAndParams, InvestmentApiRequest&gt; validator) {</span>
<span class="nc" id="L32">        this.indexDataService = indexDataService;</span>
<span class="nc" id="L33">        this.validator = validator;</span>
<span class="nc" id="L34">    }</span>

    @GetMapping(value = &quot;/module-id/{moduleId}&quot;)
    public Mono&lt;IndexDataResult&gt; getReferenceIndexData(
            @ApiParam(example = &quot;BTWS000001&quot;)
            @PathVariable(&quot;moduleId&quot;) String moduleId,
            @RequestHeader(value = &quot;X-Api-ProductId&quot;, required = false, defaultValue = &quot;&quot;) String productId,
            @RequestHeader(value = &quot;X-API-RequestId&quot;, required = false, defaultValue = &quot;&quot;) String requestId,
            @RequestHeader(value = &quot;Authorization&quot;, required = false, defaultValue = &quot;&quot;) String token
    ) {

<span class="nc" id="L45">        long startTime = System.currentTimeMillis();</span>
<span class="nc" id="L46">        HeadersAndParams headersAndParams = new InvestmentApiRequestUtil().getHeaderAndParams(token, productId, requestId);</span>
<span class="nc" id="L47">        String userIdFromToken = TokenUtil.getUserId(token);</span>
<span class="nc" id="L48">        validator.validateHeadersAndParams(headersAndParams);</span>

<span class="nc" id="L50">        return indexDataService.getData(moduleId)</span>
<span class="nc" id="L51">                .doOnError(e -&gt; {</span>
<span class="nc" id="L52">                    log.error(&quot;event_type=\&quot;RESPONSE_ERROR\&quot;, request_type=\&quot;index\&quot; event_description=\&quot;Index reference  Data failed\&quot;, user_id=\&quot;{}\&quot;,product_id=\&quot;{}\&quot;, request_id=\&quot;{}\&quot;, error_message=\&quot;{}\&quot;&quot;, userIdFromToken, productId, requestId, e.getMessage());</span>
<span class="nc" id="L53">                    throw new IndexAPIException(e.getMessage());</span>
                })
<span class="nc" id="L55">                .doOnSuccess(s -&gt;</span>
                {
<span class="nc" id="L57">                    long executeTime = System.currentTimeMillis() - startTime;</span>
<span class="nc" id="L58">                    log.info(&quot;event_type=\&quot;RESPONSE\&quot;, request_type=\&quot;index\&quot;, event_description=\&quot;Index data fetched successfully!!\&quot; ,executeTime=\&quot;{}\&quot;, user_id=\&quot;{}\&quot;, product_id=\&quot;{}\&quot;, request_id=\&quot;{}\&quot;&quot;, executeTime, userIdFromToken, productId, requestId);</span>
<span class="nc" id="L59">                });</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>