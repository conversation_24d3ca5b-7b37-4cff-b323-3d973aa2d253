package com.morningstar.martapi.controller;

import com.morningstar.martapi.service.DocumentService;
import com.morningstar.martapi.util.InvestmentApiRequestUtil;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martcommon.entity.payload.gridview.GridviewDataPoint;
import com.morningstar.martgateway.infrastructures.log.LogAttribute;
import com.morningstar.martgateway.infrastructures.log.LogEntity;
import com.morningstar.martgateway.infrastructures.log.LogEntry;
import com.morningstar.martgateway.infrastructures.log.LogHelper;
import com.morningstar.martgateway.interfaces.model.investmentapi.Investment;
import com.morningstar.martgateway.interfaces.model.investmentapi.InvestmentApiRequest;
import java.util.ArrayList;

import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static com.morningstar.martapi.config.Constant.*;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.*;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.EXECUTE_TIME;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.REQUEST_PARAM;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.USER_ID;

@RestController
@RequestMapping(value = {"/v1/documents","/investment-api/v1/documents"},
        produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE})
@Validated
public class DocumentController {

    private final RequestValidationHandler<HeadersAndParams,InvestmentApiRequest> validator;
    private final DocumentService documentService;

    @Autowired
    public DocumentController(
            @Qualifier("investmentApiValidator") RequestValidationHandler<HeadersAndParams, InvestmentApiRequest> validator, DocumentService documentService) {
        this.validator = validator;
        this.documentService = documentService;
    }

    @GetMapping(value = "", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Mono<ResponseEntity<InputStreamResource>> getData(
            @RequestParam(value = "performanceId") String performanceId,
            @RequestParam(value = "documentType") String documentType,
            @RequestParam(value = "documentId") String documentId,
            @RequestParam(value = "readCache", required = false, defaultValue = "") String readCache,
            @RequestHeader(value = "X-API-UserId", required = false, defaultValue = "") String headerUserId,
            @RequestHeader(value = "Authorization", required = false, defaultValue = "") String token,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "X-Api-ProductId", required = false, defaultValue = "") String productId,
            @RequestHeader(value = "checkEntitlement", required = false, defaultValue = "true") String checkEntitlement,
            @RequestHeader(value = "X-API-SkipProxy", required = false, defaultValue = "false") boolean skipProxy,
            ServerHttpRequest request) {
        long startTime = System.currentTimeMillis();

        if (!Objects.equals(documentType, DOCUMENT_TYPE)) {
            return Mono.error(new IllegalArgumentException("Invalid documentType: Correct the documentType, example: EQ5NQ"));
        }

        if (!isValidDocumentId(documentId)) {
            return Mono.error(new IllegalArgumentException("Invalid documentId: Correct the documentId, example: 164531"));
        }

        if (!isValidPerformanceId(performanceId)) {
            // Checking format for performanceID
            return Mono.error(new IllegalArgumentException("Invalid performanceId: Correct the performanceID, example: 0P00000001"));
        }
        InvestmentApiRequest validatedInvestmentApiRequest = buildInvestmentApiRequest(
                performanceId, documentId, token, productId, requestId, readCache, checkEntitlement, headerUserId, skipProxy
        );

        return documentService.getDocument(validatedInvestmentApiRequest, getHeadersAndParams(token, productId, requestId), startTime, documentId)
                .flatMap(inputStream -> Mono.just(buildResponse(inputStream, performanceId, documentType, documentId)))
                .doOnEach(LogHelper.logOnNext(list -> {
                    MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), requestId);
                    LogEntry.info(new LogEntity(EVENT_TYPE, RESPONSE),
                            new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                            new LogEntity(PRODUCT_ID, productId),
                            new LogEntity(USER_ID, validatedInvestmentApiRequest.getUserId()),
                            new LogEntity(REQUEST_PARAM, request.getURI()),
                            new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime));
                })).doOnEach(LogHelper.logOnError(e -> {
                    MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), requestId);
                    LogEntry.error(
                            new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                            new LogEntity(EVENT_DESCRIPTION, e),
                            new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime),
                            new LogEntity(EXCEPTION_TYPE, e.getClass()),
                            new LogEntity(PRODUCT_ID, productId),
                            new LogEntity(USER_ID, validatedInvestmentApiRequest.getUserId()),
                            new LogEntity(REQUEST_PARAM, request.getURI())
                    );
                }));
    }

    private InvestmentApiRequest buildInvestmentApiRequest(
            String performanceId, String documentId, String token, String productId, String requestId,
            String readCache, String checkEntitlement, String headerUserId, boolean skipProxy) {

        Investment investment = new Investment(performanceId);
        InvestmentApiRequest investmentApiRequest = new InvestmentApiRequest();
        investmentApiRequest.setInvestments(new ArrayList<>(List.of(investment))); // should use mutable List

        GridviewDataPoint gridviewDataPoint = new GridviewDataPoint();
        gridviewDataPoint.setDataPointIds(new ArrayList<>(List.of(COMPANY_ID_DATAPOINT, JOB_ID_DATAPOINT, EVENT_DATETIME_DATAPOINT))); // should use mutable List
        gridviewDataPoint.setEventId(Set.of(documentId));

        investmentApiRequest.setDataPoints(new ArrayList<>(List.of(gridviewDataPoint))); // should use mutable List

        HeadersAndParams headersAndParams = getHeadersAndParams(token, productId, requestId);
        validateRequest(investmentApiRequest, headersAndParams);

        return new InvestmentApiRequestUtil().getValidatedInvestmentApiRequest(
                investmentApiRequest, token, headerUserId, productId, requestId, readCache, checkEntitlement
        );
    }

    private HeadersAndParams getHeadersAndParams(String token, String productId, String requestId) {
        return new InvestmentApiRequestUtil().getHeaderAndParams(token, productId, requestId);
    }

    private ResponseEntity<InputStreamResource> buildResponse(InputStream inputStream, String performanceId, String documentType, String documentId) {
        String filename = String.format("%s_%s_%s.json", performanceId, documentType, documentId);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename);
        headers.add(HttpHeaders.CONTENT_TYPE, "application/json");

        try {
            headers.add(HttpHeaders.CONTENT_LENGTH, String.valueOf(inputStream.available()));
        } catch (IOException e) {
            throw new RuntimeException("Error calculating file size", e);
        }

        return new ResponseEntity<>(new InputStreamResource(inputStream), headers, HttpStatus.OK);
    }

    private void validateRequest(InvestmentApiRequest investmentApiRequest, HeadersAndParams headersAndParams) {
        validator.validateHeadersAndParams(headersAndParams);
        validator.validateRequestBody(investmentApiRequest);
    }

    private boolean isValidDocumentId(String documentId) {
        return documentId != null && !documentId.isEmpty();
    }

    private boolean isValidPerformanceId(String performanceId) {
        return performanceId != null && performanceId.length() == 10;
    }
}