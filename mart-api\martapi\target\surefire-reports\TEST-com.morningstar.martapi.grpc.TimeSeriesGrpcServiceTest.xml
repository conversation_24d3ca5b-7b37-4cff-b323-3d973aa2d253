<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.morningstar.martapi.grpc.TimeSeriesGrpcServiceTest" time="17.297" tests="1" errors="1" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="Cp1252"/>
    <property name="java.class.path" value="C:\Users\<USER>\.m2\repository\org\apache\maven\surefire\surefire-booter\3.2.5\surefire-booter-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\surefire\surefire-api\3.2.5\surefire-api-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\surefire\surefire-logger-api\3.2.5\surefire-logger-api-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\surefire\surefire-shared-utils\3.2.5\surefire-shared-utils-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\surefire\surefire-extensions-spi\3.2.5\surefire-extensions-spi-3.2.5.jar;C:\Zeng\dataac\msstash1\mart-api\martapi\target\test-classes;C:\Zeng\dataac\msstash1\mart-api\martapi\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-test\2.7.18\spring-boot-starter-test-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.7.18\spring-boot-starter-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.7.18\spring-boot-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.7.18\spring-boot-starter-logging-2.7.18.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.12\logback-classic-1.2.12.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.12\logback-core-1.2.12.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.7.18\spring-boot-test-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.7.18\spring-boot-test-autoconfigure-2.7.18.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.4.11\json-smart-2.4.11.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.4.11\accessors-smart-2.4.11.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.3\asm-9.3.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.22.0\assertj-core-3.22.0.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.8.2\junit-jupiter-5.8.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.8.2\junit-jupiter-params-5.8.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.8.2\junit-jupiter-engine-5.8.2.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\4.5.1\mockito-junit-jupiter-4.5.1.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.31\spring-core-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.3.31\spring-jcl-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;C:\Users\<USER>\.m2\repository\com\morningstar\mart-gateway\2.41.1-RELEASE\mart-gateway-2.41.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.7.18\spring-boot-autoconfigure-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-actuator\2.7.18\spring-boot-starter-actuator-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\2.7.18\spring-boot-actuator-autoconfigure-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\2.7.18\spring-boot-actuator-2.7.18.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-core\1.9.17\micrometer-core-1.9.17.jar;C:\Users\<USER>\.m2\repository\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar;C:\Users\<USER>\.m2\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-mongodb\2.7.18\spring-boot-starter-data-mongodb-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\mongodb\mongodb-driver-sync\4.6.1\mongodb-driver-sync-4.6.1.jar;C:\Users\<USER>\.m2\repository\org\mongodb\bson\4.6.1\bson-4.6.1.jar;C:\Users\<USER>\.m2\repository\org\mongodb\mongodb-driver-core\4.6.1\mongodb-driver-core-4.6.1.jar;C:\Users\<USER>\.m2\repository\org\mongodb\bson-record-codec\4.6.1\bson-record-codec-4.6.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-mongodb\3.4.18\spring-data-mongodb-3.4.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.3.31\spring-tx-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.31\spring-expression-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.7.18\spring-data-commons-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webflux\5.3.31\spring-webflux-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.31\spring-beans-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.31\spring-web-5.3.31.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.4.34\reactor-core-3.4.34.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Users\<USER>\.m2\repository\org\postgresql\postgresql\42.3.8\postgresql-42.3.8.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.5.0\checker-qual-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis-reactive\2.7.18\spring-boot-starter-data-redis-reactive-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis\2.7.18\spring-boot-starter-data-redis-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-redis\2.7.18\spring-data-redis-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-keyvalue\2.7.18\spring-data-keyvalue-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\5.3.31\spring-oxm-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\5.3.31\spring-context-support-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-elasticsearch\2.7.18\spring-boot-starter-data-elasticsearch-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-elasticsearch\4.4.18\spring-data-elasticsearch-4.4.18.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\client\elasticsearch-rest-high-level-client\7.17.15\elasticsearch-rest-high-level-client-7.17.15.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\elasticsearch\7.17.15\elasticsearch-7.17.15.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\elasticsearch-core\7.17.15\elasticsearch-core-7.17.15.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\elasticsearch-secure-sm\7.17.15\elasticsearch-secure-sm-7.17.15.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\elasticsearch-x-content\7.17.15\elasticsearch-x-content-7.17.15.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-smile\2.13.5\jackson-dataformat-smile-2.13.5.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\elasticsearch-geo\7.17.15\elasticsearch-geo-7.17.15.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\elasticsearch-lz4\7.17.15\elasticsearch-lz4-7.17.15.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-core\8.11.1\lucene-core-8.11.1.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-analyzers-common\8.11.1\lucene-analyzers-common-8.11.1.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-backward-codecs\8.11.1\lucene-backward-codecs-8.11.1.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-grouping\8.11.1\lucene-grouping-8.11.1.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-highlighter\8.11.1\lucene-highlighter-8.11.1.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-join\8.11.1\lucene-join-8.11.1.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-memory\8.11.1\lucene-memory-8.11.1.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-misc\8.11.1\lucene-misc-8.11.1.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-queries\8.11.1\lucene-queries-8.11.1.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-queryparser\8.11.1\lucene-queryparser-8.11.1.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-sandbox\8.11.1\lucene-sandbox-8.11.1.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-spatial3d\8.11.1\lucene-spatial3d-8.11.1.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-suggest\8.11.1\lucene-suggest-8.11.1.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\elasticsearch-cli\7.17.15\elasticsearch-cli-7.17.15.jar;C:\Users\<USER>\.m2\repository\net\sf\jopt-simple\jopt-simple\5.0.2\jopt-simple-5.0.2.jar;C:\Users\<USER>\.m2\repository\com\carrotsearch\hppc\0.8.1\hppc-0.8.1.jar;C:\Users\<USER>\.m2\repository\com\tdunning\t-digest\3.2\t-digest-3.2.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-core\2.17.2\log4j-core-2.17.2.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna\5.10.0\jna-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\elasticsearch-plugin-classloader\7.17.15\elasticsearch-plugin-classloader-7.17.15.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\plugin\mapper-extras-client\7.17.15\mapper-extras-client-7.17.15.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\plugin\parent-join-client\7.17.15\parent-join-client-7.17.15.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\plugin\aggs-matrix-stats-client\7.17.15\aggs-matrix-stats-client-7.17.15.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\plugin\rank-eval-client\7.17.15\rank-eval-client-7.17.15.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\plugin\lang-mustache-client\7.17.15\lang-mustache-client-7.17.15.jar;C:\Users\<USER>\.m2\repository\com\github\spullara\mustache\java\compiler\0.9.6\compiler-0.9.6.jar;C:\Users\<USER>\.m2\repository\co\elastic\clients\elasticsearch-java\7.17.15\elasticsearch-java-7.17.15.jar;C:\Users\<USER>\.m2\repository\jakarta\json\jakarta.json-api\1.1.6\jakarta.json-api-1.1.6.jar;C:\Users\<USER>\.m2\repository\org\eclipse\parsson\parsson\1.0.0\parsson-1.0.0.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\client\elasticsearch-rest-client\7.17.15\elasticsearch-rest-client-7.17.15.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpasyncclient\4.1.5\httpasyncclient-4.1.5.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore-nio\4.4.16\httpcore-nio-4.4.16.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-pool2\2.11.1\commons-pool2-2.11.1.jar;C:\Users\<USER>\.m2\repository\org\dom4j\dom4j\2.1.4\dom4j-2.1.4.jar;C:\Users\<USER>\.m2\repository\jaxen\jaxen\1.2.0\jaxen-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\json\json\20200518\json-20200518.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.13.5\jackson-databind-2.13.5.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.5\jackson-annotations-2.13.5.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.13.5\jackson-core-2.13.5.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-protobuf\2.13.5\jackson-dataformat-protobuf-2.13.5.jar;C:\Users\<USER>\.m2\repository\com\squareup\protoparser\4.0.3\protoparser-4.0.3.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.9.1\gson-2.9.1.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty\1.0.39\reactor-netty-1.0.39.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-core\1.0.39\reactor-netty-core-1.0.39.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-proxy\4.1.101.Final\netty-handler-proxy-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-socks\4.1.101.Final\netty-codec-socks-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.101.Final\netty-resolver-dns-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.101.Final\netty-codec-dns-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.101.Final\netty-resolver-dns-native-macos-4.1.101.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-classes-macos\4.1.101.Final\netty-resolver-dns-classes-macos-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-epoll\4.1.101.Final\netty-transport-native-epoll-4.1.101.Final-linux-x86_64.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-http\1.0.39\reactor-netty-http-1.0.39.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\incubator\reactor-netty-incubator-quic\0.0.28\reactor-netty-incubator-quic-0.0.28.jar;C:\Users\<USER>\.m2\repository\io\netty\incubator\netty-incubator-codec-native-quic\0.0.52.Final\netty-incubator-codec-native-quic-0.0.52.Final-linux-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\incubator\netty-incubator-codec-classes-quic\0.0.52.Final\netty-incubator-codec-classes-quic-0.0.52.Final.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-http-brave\1.0.39\reactor-netty-http-brave-1.0.39.jar;C:\Users\<USER>\.m2\repository\io\zipkin\brave\brave-instrumentation-http\5.16.0\brave-instrumentation-http-5.16.0.jar;C:\Users\<USER>\.m2\repository\io\zipkin\brave\brave\5.16.0\brave-5.16.0.jar;C:\Users\<USER>\.m2\repository\io\zipkin\reporter2\zipkin-reporter-brave\2.16.3\zipkin-reporter-brave-2.16.3.jar;C:\Users\<USER>\.m2\repository\io\zipkin\reporter2\zipkin-reporter\2.16.3\zipkin-reporter-2.16.3.jar;C:\Users\<USER>\.m2\repository\io\zipkin\zipkin2\zipkin\2.23.2\zipkin-2.23.2.jar;C:\Users\<USER>\.m2\repository\com\morningstar\calculation\calculation\1.0.0\calculation-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\morningstar\calculation\calculationlibrary\1.0.0\calculationlibrary-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\morningstar\messaging\domain-objects\1.0.49-RELEASE\domain-objects-1.0.49-RELEASE.jar;C:\Users\<USER>\.m2\repository\joda-time\joda-time\2.10.1\joda-time-2.10.1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.21\commons-compress-1.21.jar;C:\Users\<USER>\.m2\repository\com\morningstar\redshift-connection-lib\1.3.1-RELEASE\redshift-connection-lib-1.3.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis\3.5.7\mybatis-3.5.7.jar;C:\Users\<USER>\.m2\repository\com\amazonaws\aws-java-sdk-redshift\1.11.900\aws-java-sdk-redshift-1.11.900.jar;C:\Users\<USER>\.m2\repository\com\amazonaws\aws-java-sdk-redshiftdataapi\1.11.900\aws-java-sdk-redshiftdataapi-1.11.900.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\com\amazonaws\aws-java-sdk-ssm\1.11.900\aws-java-sdk-ssm-1.11.900.jar;C:\Users\<USER>\.m2\repository\com\amazonaws\aws-java-sdk-sts\1.11.900\aws-java-sdk-sts-1.11.900.jar;C:\Users\<USER>\.m2\repository\com\amazon\redshift\redshift-jdbc42\2.1.0.9\redshift-jdbc42-2.1.0.9.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\5.3.31\spring-aspects-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;C:\Users\<USER>\.m2\repository\org\mybatis\spring\boot\mybatis-spring-boot-starter\2.1.3\mybatis-spring-boot-starter-2.1.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.7.18\spring-boot-starter-jdbc-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.3.31\spring-jdbc-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\2.1.3\mybatis-spring-boot-autoconfigure-2.1.3.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis-spring\2.0.5\mybatis-spring-2.0.5.jar;C:\Users\<USER>\.m2\repository\com\microsoft\sqlserver\mssql-jdbc\10.2.3.jre8\mssql-jdbc-10.2.3.jre8.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\tools\blockhound\1.0.8.RELEASE\blockhound-1.0.8.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\apache\avro\avro\1.9.2\avro-1.9.2.jar;C:\Users\<USER>\.m2\repository\org\apache\avro\avro-maven-plugin\1.9.2\avro-maven-plugin-1.9.2.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-plugin-api\2.0.11\maven-plugin-api-2.0.11.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-project\2.0.11\maven-project-2.0.11.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-settings\2.0.11\maven-settings-2.0.11.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-profile\2.0.11\maven-profile-2.0.11.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-model\2.0.11\maven-model-2.0.11.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-artifact-manager\2.0.11\maven-artifact-manager-2.0.11.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-repository-metadata\2.0.11\maven-repository-metadata-2.0.11.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\wagon\wagon-provider-api\1.0-beta-2\wagon-provider-api-1.0-beta-2.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-plugin-registry\2.0.11\maven-plugin-registry-2.0.11.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-interpolation\1.1\plexus-interpolation-1.1.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-utils\1.5.6\plexus-utils-1.5.6.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-artifact\2.0.11\maven-artifact-2.0.11.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-container-default\1.0-alpha-9-stable-1\plexus-container-default-1.0-alpha-9-stable-1.jar;C:\Users\<USER>\.m2\repository\classworlds\classworlds\1.1-alpha-2\classworlds-1.1-alpha-2.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\shared\file-management\1.2.1\file-management-1.2.1.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\shared\maven-shared-io\1.1\maven-shared-io-1.1.jar;C:\Users\<USER>\.m2\repository\org\apache\avro\avro-compiler\1.9.2\avro-compiler-1.9.2.jar;C:\Users\<USER>\.m2\repository\org\apache\velocity\velocity-engine-core\2.2\velocity-engine-core-2.2.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\com\sun\xml\bind\jaxb-core\4.0.5\jaxb-core-4.0.5.jar;C:\Users\<USER>\.m2\repository\org\eclipse\angus\angus-activation\2.0.2\angus-activation-2.0.2.jar;C:\Users\<USER>\.m2\repository\com\sun\xml\bind\jaxb-impl\4.0.5\jaxb-impl-4.0.5.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\2.3.9\jaxb-runtime-2.3.9.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\2.3.9\txw2-2.3.9.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;C:\Users\<USER>\.m2\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.5\jackson-datatype-jsr310-2.13.5.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\33.2.0-jre\guava-33.2.0-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.2\failureaccess-1.0.2.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\morningstar\mart-component-common\2.41.1-RELEASE\mart-component-common-2.41.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\com\amazonaws\aws-java-sdk-s3\1.12.120\aws-java-sdk-s3-1.12.120.jar;C:\Users\<USER>\.m2\repository\com\amazonaws\aws-java-sdk-kms\1.12.120\aws-java-sdk-kms-1.12.120.jar;C:\Users\<USER>\.m2\repository\commons-beanutils\commons-beanutils\1.9.4\commons-beanutils-1.9.4.jar;C:\Users\<USER>\.m2\repository\commons-collections\commons-collections\3.2.2\commons-collections-3.2.2.jar;C:\Users\<USER>\.m2\repository\com\amazonaws\aws-java-sdk-sqs\1.11.724\aws-java-sdk-sqs-1.11.724.jar;C:\Users\<USER>\.m2\repository\io\vavr\vavr\0.9.1\vavr-0.9.1.jar;C:\Users\<USER>\.m2\repository\io\vavr\vavr-match\0.9.1\vavr-match-0.9.1.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.16.1\commons-io-2.16.1.jar;C:\Users\<USER>\.m2\repository\org\lz4\lz4-java\1.8.0\lz4-java-1.8.0.jar;C:\Users\<USER>\.m2\repository\com\github\ben-manes\caffeine\caffeine\3.2.0\caffeine-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\jspecify\jspecify\1.0.0\jspecify-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\morningstar\ecs-metadata-core\2.41.1-RELEASE\ecs-metadata-core-2.41.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\ecs\2.25.27\ecs-2.25.27.jar;C:\Users\<USER>\.m2\repository\com\auth0\java-jwt\4.0.0\java-jwt-4.0.0.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\s3\2.25.27\s3-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\aws-xml-protocol\2.25.27\aws-xml-protocol-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\aws-query-protocol\2.25.27\aws-query-protocol-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\arns\2.25.27\arns-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\crt-core\2.25.27\crt-core-2.25.27.jar;C:\Users\<USER>\.m2\repository\com\morningstar\mart-component-data-point\2.41.1-RELEASE\mart-component-data-point-2.41.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\com\morningstar\mart-component-entitlement\2.41.1-RELEASE\mart-component-entitlement-2.41.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\com\morningstar\mart-component-uim-core\2.41.1-RELEASE\mart-component-uim-core-2.41.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\com\morningstar\mart-component-gateway-core\2.41.1-RELEASE\mart-component-gateway-core-2.41.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\com\morningstar\mart-component-fixed-income-ice-data\2.41.1-RELEASE\mart-component-fixed-income-ice-data-2.41.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\2.7.18\spring-boot-starter-aop-2.7.18.jar;C:\Users\<USER>\.m2\repository\com\morningstar\mart-component-portfolio-holdings-service\2.41.1-RELEASE\mart-component-portfolio-holdings-service-2.41.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\com\morningstar\mart-component-portfolio-holdings-data\2.41.1-RELEASE\mart-component-portfolio-holdings-data-2.41.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\com\morningstar\mart-component-redisson-lock\2.41.1-RELEASE\mart-component-redisson-lock-2.41.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\org\redisson\redisson\3.15.0\redisson-3.15.0.jar;C:\Users\<USER>\.m2\repository\javax\cache\cache-api\1.1.1\cache-api-1.1.1.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxjava3\rxjava\3.0.7\rxjava-3.0.7.jar;C:\Users\<USER>\.m2\repository\org\jboss\marshalling\jboss-marshalling-river\2.0.10.Final\jboss-marshalling-river-2.0.10.Final.jar;C:\Users\<USER>\.m2\repository\org\jboss\marshalling\jboss-marshalling\2.0.10.Final\jboss-marshalling-2.0.10.Final.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.13.5\jackson-dataformat-yaml-2.13.5.jar;C:\Users\<USER>\.m2\repository\org\jodd\jodd-bean\5.1.6\jodd-bean-5.1.6.jar;C:\Users\<USER>\.m2\repository\org\jodd\jodd-core\5.1.6\jodd-core-5.1.6.jar;C:\Users\<USER>\.m2\repository\com\morningstar\mart-equity-package\2.41.1-RELEASE\mart-equity-package-2.41.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.7.18\spring-boot-starter-web-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.7.18\spring-boot-starter-tomcat-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.83\tomcat-embed-core-9.0.83.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.83\tomcat-embed-el-9.0.83.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.83\tomcat-embed-websocket-9.0.83.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.3.31\spring-webmvc-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\retry\spring-retry\1.3.4\spring-retry-1.3.4.jar;C:\Users\<USER>\.m2\repository\com\morningstar\entitlement-eod-data\4.3.1-RELEASE\entitlement-eod-data-4.3.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\com\google\protobuf\protobuf-java\3.25.1\protobuf-java-3.25.1.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-netty-shaded\1.58.0\grpc-netty-shaded-1.58.0.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.20.0\error_prone_annotations-2.20.0.jar;C:\Users\<USER>\.m2\repository\io\perfmark\perfmark-api\0.26.0\perfmark-api-0.26.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-core\1.58.0\grpc-core-1.58.0.jar;C:\Users\<USER>\.m2\repository\com\google\android\annotations\4.1.1.4\annotations-4.1.1.4.jar;C:\Users\<USER>\.m2\repository\org\codehaus\mojo\animal-sniffer-annotations\1.23\animal-sniffer-annotations-1.23.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-context\1.58.0\grpc-context-1.58.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-protobuf\1.58.0\grpc-protobuf-1.58.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-api\1.58.0\grpc-api-1.58.0.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\proto-google-common-protos\2.22.0\proto-google-common-protos-2.22.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-protobuf-lite\1.58.0\grpc-protobuf-lite-1.58.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-stub\1.58.0\grpc-stub-1.58.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-services\1.58.0\grpc-services-1.58.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-util\1.58.0\grpc-util-1.58.0.jar;C:\Users\<USER>\.m2\repository\com\google\protobuf\protobuf-java-util\3.24.0\protobuf-java-util-3.24.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\2.8\j2objc-annotations-2.8.jar;C:\Users\<USER>\.m2\repository\net\devh\grpc-spring-boot-starter\2.15.0.RELEASE\grpc-spring-boot-starter-2.15.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\net\devh\grpc-server-spring-boot-starter\2.15.0.RELEASE\grpc-server-spring-boot-starter-2.15.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\net\devh\grpc-server-spring-boot-autoconfigure\2.15.0.RELEASE\grpc-server-spring-boot-autoconfigure-2.15.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\net\devh\grpc-common-spring-boot\2.15.0.RELEASE\grpc-common-spring-boot-2.15.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-inprocess\1.58.0\grpc-inprocess-1.58.0.jar;C:\Users\<USER>\.m2\repository\net\devh\grpc-client-spring-boot-starter\2.15.0.RELEASE\grpc-client-spring-boot-starter-2.15.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\net\devh\grpc-client-spring-boot-autoconfigure\2.15.0.RELEASE\grpc-client-spring-boot-autoconfigure-2.15.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\mysql\mysql-connector-java\8.0.16\mysql-connector-java-8.0.16.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-webflux\2.7.18\spring-boot-starter-webflux-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.7.18\spring-boot-starter-json-2.7.18.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.5\jackson-datatype-jdk8-2.13.5.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.5\jackson-module-parameter-names-2.13.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-reactor-netty\2.7.18\spring-boot-starter-reactor-netty-2.7.18.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-test\3.4.41\reactor-test-3.4.41.jar;C:\Users\<USER>\.m2\repository\javax\inject\javax.inject\1\javax.inject-1.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.26\lombok-1.18.26.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger2\3.0.0\springfox-swagger2-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spi\3.0.0\springfox-spi-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-core\3.0.0\springfox-core-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-schema\3.0.0\springfox-schema-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-common\3.0.0\springfox-swagger-common-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-annotations\2.1.2\swagger-annotations-2.1.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-web\3.0.0\springfox-spring-web-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\github\classgraph\classgraph\4.8.83\classgraph-4.8.83.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-webmvc\3.0.0\springfox-spring-webmvc-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-webflux\3.0.0\springfox-spring-webflux-3.0.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\2.0.0.RELEASE\spring-plugin-core-2.0.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.31\spring-context-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.31\spring-aop-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-metadata\2.0.0.RELEASE\spring-plugin-metadata-2.0.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-annotations\1.5.20\swagger-annotations-1.5.20.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-models\1.5.20\swagger-models-1.5.20.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.3.1.Final\mapstruct-1.3.1.Final.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-boot-starter\3.0.0\springfox-boot-starter-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-oas\3.0.0\springfox-oas-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-models\2.1.2\swagger-models-2.1.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-data-rest\3.0.0\springfox-data-rest-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-bean-validators\3.0.0\springfox-bean-validators-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-ui\3.0.0\springfox-swagger-ui-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\lettuce\lettuce-core\6.1.6.RELEASE\lettuce-core-6.1.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\5.3.37\spring-test-5.3.37.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\vintage\junit-vintage-engine\5.8.2\junit-vintage-engine-5.8.2.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.8.2\junit-platform-engine-1.8.2.jar;C:\Users\<USER>\.m2\repository\junit\junit\4.13.2\junit-4.13.2.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-core\2.2\hamcrest-core-2.2.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\5.2.0\mockito-core-5.2.0.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-inline\5.2.0\mockito-inline-5.2.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.14.11\byte-buddy-1.14.11.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.11\byte-buddy-agent-1.14.11.jar;C:\Users\<USER>\.m2\repository\com\amazonaws\aws-java-sdk-lambda\1.12.120\aws-java-sdk-lambda-1.12.120.jar;C:\Users\<USER>\.m2\repository\com\amazonaws\aws-java-sdk-core\1.12.120\aws-java-sdk-core-1.12.120.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.1.3\commons-logging-1.1.3.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;C:\Users\<USER>\.m2\repository\software\amazon\ion\ion-java\1.0.2\ion-java-1.0.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-cbor\2.13.5\jackson-dataformat-cbor-2.13.5.jar;C:\Users\<USER>\.m2\repository\com\amazonaws\jmespath-java\1.12.120\jmespath-java-1.12.120.jar;C:\Users\<USER>\.m2\repository\com\morningstar\content-service-data-point-columns-util\1.0.2-RELEASE\content-service-data-point-columns-util-1.0.2-RELEASE.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\dynamodb\2.25.27\dynamodb-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\aws-json-protocol\2.25.27\aws-json-protocol-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\third-party-jackson-core\2.25.27\third-party-jackson-core-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\protocol-core\2.25.27\protocol-core-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\profiles\2.25.27\profiles-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\http-auth-aws\2.25.27\http-auth-aws-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\checksums-spi\2.25.27\checksums-spi-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\checksums\2.25.27\checksums-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\sdk-core\2.25.27\sdk-core-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\auth\2.25.27\auth-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\eventstream\eventstream\1.0.1\eventstream-1.0.1.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\http-auth-spi\2.25.27\http-auth-spi-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\http-auth\2.25.27\http-auth-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\identity-spi\2.25.27\identity-spi-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\http-client-spi\2.25.27\http-client-spi-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\regions\2.25.27\regions-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\annotations\2.25.27\annotations-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\utils\2.25.27\utils-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\aws-core\2.25.27\aws-core-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\metrics-spi\2.25.27\metrics-spi-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\json-utils\2.25.27\json-utils-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\endpoints-spi\2.25.27\endpoints-spi-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\apache-client\2.25.27\apache-client-2.25.27.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\netty-nio-client\2.25.27\netty-nio-client-2.25.27.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.101.Final\netty-codec-http-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http2\4.1.101.Final\netty-codec-http2-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.101.Final\netty-codec-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.101.Final\netty-transport-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.101.Final\netty-common-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.101.Final\netty-buffer-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.101.Final\netty-handler-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.101.Final\netty-transport-native-unix-common-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-epoll\4.1.101.Final\netty-transport-classes-epoll-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.101.Final\netty-resolver-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\dynamodb-enhanced\2.25.27\dynamodb-enhanced-2.25.27.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\surefire\surefire-junit-platform\3.2.5\surefire-junit-platform-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\surefire\common-java5\3.2.5\common-java5-3.2.5.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-launcher\1.8.2\junit-platform-launcher-1.8.2.jar"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="America/Toronto"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="US"/>
    <property name="sun.boot.library.path" value="C:\Program Files\Java\jdk-17\bin"/>
    <property name="sun.java.command" value="org.apache.maven.surefire.booter.ForkedBooter C:\Users\<USER>\AppData\Local\Temp\surefire12128248184438374442 2025-06-24T18-25-53_229-jvmRun1 surefire-20250624182553376_1tmp surefire_0-20250624182553376_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="test" value="TimeSeriesGrpcServiceTest"/>
    <property name="surefire.test.class.path" value="C:\Zeng\dataac\msstash1\mart-api\martapi\target\test-classes;C:\Zeng\dataac\msstash1\mart-api\martapi\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-test\2.7.18\spring-boot-starter-test-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.7.18\spring-boot-starter-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.7.18\spring-boot-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.7.18\spring-boot-starter-logging-2.7.18.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.12\logback-classic-1.2.12.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.12\logback-core-1.2.12.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;C:\Users\<USER>\.m2\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.30\snakeyaml-1.30.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.7.18\spring-boot-test-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.7.18\spring-boot-test-autoconfigure-2.7.18.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.7.0\json-path-2.7.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.4.11\json-smart-2.4.11.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\2.4.11\accessors-smart-2.4.11.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.3\asm-9.3.jar;C:\Users\<USER>\.m2\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;C:\Users\<USER>\.m2\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.22.0\assertj-core-3.22.0.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter\5.8.2\junit-jupiter-5.8.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-params\5.8.2\junit-jupiter-params-5.8.2.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.8.2\junit-jupiter-engine-5.8.2.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-junit-jupiter\4.5.1\mockito-junit-jupiter-4.5.1.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.3.31\spring-core-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.3.31\spring-jcl-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;C:\Users\<USER>\.m2\repository\com\morningstar\mart-gateway\2.41.1-RELEASE\mart-gateway-2.41.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.7.18\spring-boot-autoconfigure-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-actuator\2.7.18\spring-boot-starter-actuator-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\2.7.18\spring-boot-actuator-autoconfigure-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\2.7.18\spring-boot-actuator-2.7.18.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-core\1.9.17\micrometer-core-1.9.17.jar;C:\Users\<USER>\.m2\repository\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar;C:\Users\<USER>\.m2\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-mongodb\2.7.18\spring-boot-starter-data-mongodb-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\mongodb\mongodb-driver-sync\4.6.1\mongodb-driver-sync-4.6.1.jar;C:\Users\<USER>\.m2\repository\org\mongodb\bson\4.6.1\bson-4.6.1.jar;C:\Users\<USER>\.m2\repository\org\mongodb\mongodb-driver-core\4.6.1\mongodb-driver-core-4.6.1.jar;C:\Users\<USER>\.m2\repository\org\mongodb\bson-record-codec\4.6.1\bson-record-codec-4.6.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-mongodb\3.4.18\spring-data-mongodb-3.4.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.3.31\spring-tx-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.3.31\spring-expression-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.7.18\spring-data-commons-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webflux\5.3.31\spring-webflux-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.3.31\spring-beans-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.3.31\spring-web-5.3.31.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.4.34\reactor-core-3.4.34.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\Users\<USER>\.m2\repository\org\postgresql\postgresql\42.3.8\postgresql-42.3.8.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.5.0\checker-qual-3.5.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis-reactive\2.7.18\spring-boot-starter-data-redis-reactive-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis\2.7.18\spring-boot-starter-data-redis-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-redis\2.7.18\spring-data-redis-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-keyvalue\2.7.18\spring-data-keyvalue-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\5.3.31\spring-oxm-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\5.3.31\spring-context-support-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-elasticsearch\2.7.18\spring-boot-starter-data-elasticsearch-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-elasticsearch\4.4.18\spring-data-elasticsearch-4.4.18.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\client\elasticsearch-rest-high-level-client\7.17.15\elasticsearch-rest-high-level-client-7.17.15.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\elasticsearch\7.17.15\elasticsearch-7.17.15.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\elasticsearch-core\7.17.15\elasticsearch-core-7.17.15.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\elasticsearch-secure-sm\7.17.15\elasticsearch-secure-sm-7.17.15.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\elasticsearch-x-content\7.17.15\elasticsearch-x-content-7.17.15.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-smile\2.13.5\jackson-dataformat-smile-2.13.5.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\elasticsearch-geo\7.17.15\elasticsearch-geo-7.17.15.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\elasticsearch-lz4\7.17.15\elasticsearch-lz4-7.17.15.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-core\8.11.1\lucene-core-8.11.1.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-analyzers-common\8.11.1\lucene-analyzers-common-8.11.1.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-backward-codecs\8.11.1\lucene-backward-codecs-8.11.1.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-grouping\8.11.1\lucene-grouping-8.11.1.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-highlighter\8.11.1\lucene-highlighter-8.11.1.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-join\8.11.1\lucene-join-8.11.1.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-memory\8.11.1\lucene-memory-8.11.1.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-misc\8.11.1\lucene-misc-8.11.1.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-queries\8.11.1\lucene-queries-8.11.1.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-queryparser\8.11.1\lucene-queryparser-8.11.1.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-sandbox\8.11.1\lucene-sandbox-8.11.1.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-spatial3d\8.11.1\lucene-spatial3d-8.11.1.jar;C:\Users\<USER>\.m2\repository\org\apache\lucene\lucene-suggest\8.11.1\lucene-suggest-8.11.1.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\elasticsearch-cli\7.17.15\elasticsearch-cli-7.17.15.jar;C:\Users\<USER>\.m2\repository\net\sf\jopt-simple\jopt-simple\5.0.2\jopt-simple-5.0.2.jar;C:\Users\<USER>\.m2\repository\com\carrotsearch\hppc\0.8.1\hppc-0.8.1.jar;C:\Users\<USER>\.m2\repository\com\tdunning\t-digest\3.2\t-digest-3.2.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-core\2.17.2\log4j-core-2.17.2.jar;C:\Users\<USER>\.m2\repository\net\java\dev\jna\jna\5.10.0\jna-5.10.0.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\elasticsearch-plugin-classloader\7.17.15\elasticsearch-plugin-classloader-7.17.15.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\plugin\mapper-extras-client\7.17.15\mapper-extras-client-7.17.15.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\plugin\parent-join-client\7.17.15\parent-join-client-7.17.15.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\plugin\aggs-matrix-stats-client\7.17.15\aggs-matrix-stats-client-7.17.15.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\plugin\rank-eval-client\7.17.15\rank-eval-client-7.17.15.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\plugin\lang-mustache-client\7.17.15\lang-mustache-client-7.17.15.jar;C:\Users\<USER>\.m2\repository\com\github\spullara\mustache\java\compiler\0.9.6\compiler-0.9.6.jar;C:\Users\<USER>\.m2\repository\co\elastic\clients\elasticsearch-java\7.17.15\elasticsearch-java-7.17.15.jar;C:\Users\<USER>\.m2\repository\jakarta\json\jakarta.json-api\1.1.6\jakarta.json-api-1.1.6.jar;C:\Users\<USER>\.m2\repository\org\eclipse\parsson\parsson\1.0.0\parsson-1.0.0.jar;C:\Users\<USER>\.m2\repository\org\elasticsearch\client\elasticsearch-rest-client\7.17.15\elasticsearch-rest-client-7.17.15.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpasyncclient\4.1.5\httpasyncclient-4.1.5.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore-nio\4.4.16\httpcore-nio-4.4.16.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-pool2\2.11.1\commons-pool2-2.11.1.jar;C:\Users\<USER>\.m2\repository\org\dom4j\dom4j\2.1.4\dom4j-2.1.4.jar;C:\Users\<USER>\.m2\repository\jaxen\jaxen\1.2.0\jaxen-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\json\json\20200518\json-20200518.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.13.5\jackson-databind-2.13.5.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.5\jackson-annotations-2.13.5.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.13.5\jackson-core-2.13.5.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-protobuf\2.13.5\jackson-dataformat-protobuf-2.13.5.jar;C:\Users\<USER>\.m2\repository\com\squareup\protoparser\4.0.3\protoparser-4.0.3.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.9.1\gson-2.9.1.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty\1.0.39\reactor-netty-1.0.39.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-core\1.0.39\reactor-netty-core-1.0.39.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler-proxy\4.1.101.Final\netty-handler-proxy-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-socks\4.1.101.Final\netty-codec-socks-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns\4.1.101.Final\netty-resolver-dns-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-dns\4.1.101.Final\netty-codec-dns-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-native-macos\4.1.101.Final\netty-resolver-dns-native-macos-4.1.101.Final-osx-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver-dns-classes-macos\4.1.101.Final\netty-resolver-dns-classes-macos-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-epoll\4.1.101.Final\netty-transport-native-epoll-4.1.101.Final-linux-x86_64.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-http\1.0.39\reactor-netty-http-1.0.39.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\incubator\reactor-netty-incubator-quic\0.0.28\reactor-netty-incubator-quic-0.0.28.jar;C:\Users\<USER>\.m2\repository\io\netty\incubator\netty-incubator-codec-native-quic\0.0.52.Final\netty-incubator-codec-native-quic-0.0.52.Final-linux-x86_64.jar;C:\Users\<USER>\.m2\repository\io\netty\incubator\netty-incubator-codec-classes-quic\0.0.52.Final\netty-incubator-codec-classes-quic-0.0.52.Final.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\netty\reactor-netty-http-brave\1.0.39\reactor-netty-http-brave-1.0.39.jar;C:\Users\<USER>\.m2\repository\io\zipkin\brave\brave-instrumentation-http\5.16.0\brave-instrumentation-http-5.16.0.jar;C:\Users\<USER>\.m2\repository\io\zipkin\brave\brave\5.16.0\brave-5.16.0.jar;C:\Users\<USER>\.m2\repository\io\zipkin\reporter2\zipkin-reporter-brave\2.16.3\zipkin-reporter-brave-2.16.3.jar;C:\Users\<USER>\.m2\repository\io\zipkin\reporter2\zipkin-reporter\2.16.3\zipkin-reporter-2.16.3.jar;C:\Users\<USER>\.m2\repository\io\zipkin\zipkin2\zipkin\2.23.2\zipkin-2.23.2.jar;C:\Users\<USER>\.m2\repository\com\morningstar\calculation\calculation\1.0.0\calculation-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\morningstar\calculation\calculationlibrary\1.0.0\calculationlibrary-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\morningstar\messaging\domain-objects\1.0.49-RELEASE\domain-objects-1.0.49-RELEASE.jar;C:\Users\<USER>\.m2\repository\joda-time\joda-time\2.10.1\joda-time-2.10.1.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-compress\1.21\commons-compress-1.21.jar;C:\Users\<USER>\.m2\repository\com\morningstar\redshift-connection-lib\1.3.1-RELEASE\redshift-connection-lib-1.3.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis\3.5.7\mybatis-3.5.7.jar;C:\Users\<USER>\.m2\repository\com\amazonaws\aws-java-sdk-redshift\1.11.900\aws-java-sdk-redshift-1.11.900.jar;C:\Users\<USER>\.m2\repository\com\amazonaws\aws-java-sdk-redshiftdataapi\1.11.900\aws-java-sdk-redshiftdataapi-1.11.900.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\com\amazonaws\aws-java-sdk-ssm\1.11.900\aws-java-sdk-ssm-1.11.900.jar;C:\Users\<USER>\.m2\repository\com\amazonaws\aws-java-sdk-sts\1.11.900\aws-java-sdk-sts-1.11.900.jar;C:\Users\<USER>\.m2\repository\com\amazon\redshift\redshift-jdbc42\2.1.0.9\redshift-jdbc42-2.1.0.9.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aspects\5.3.31\spring-aspects-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;C:\Users\<USER>\.m2\repository\org\mybatis\spring\boot\mybatis-spring-boot-starter\2.1.3\mybatis-spring-boot-starter-2.1.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.7.18\spring-boot-starter-jdbc-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.3.31\spring-jdbc-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\2.1.3\mybatis-spring-boot-autoconfigure-2.1.3.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis-spring\2.0.5\mybatis-spring-2.0.5.jar;C:\Users\<USER>\.m2\repository\com\microsoft\sqlserver\mssql-jdbc\10.2.3.jre8\mssql-jdbc-10.2.3.jre8.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\tools\blockhound\1.0.8.RELEASE\blockhound-1.0.8.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\apache\avro\avro\1.9.2\avro-1.9.2.jar;C:\Users\<USER>\.m2\repository\org\apache\avro\avro-maven-plugin\1.9.2\avro-maven-plugin-1.9.2.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-plugin-api\2.0.11\maven-plugin-api-2.0.11.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-project\2.0.11\maven-project-2.0.11.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-settings\2.0.11\maven-settings-2.0.11.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-profile\2.0.11\maven-profile-2.0.11.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-model\2.0.11\maven-model-2.0.11.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-artifact-manager\2.0.11\maven-artifact-manager-2.0.11.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-repository-metadata\2.0.11\maven-repository-metadata-2.0.11.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\wagon\wagon-provider-api\1.0-beta-2\wagon-provider-api-1.0-beta-2.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-plugin-registry\2.0.11\maven-plugin-registry-2.0.11.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-interpolation\1.1\plexus-interpolation-1.1.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-utils\1.5.6\plexus-utils-1.5.6.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-artifact\2.0.11\maven-artifact-2.0.11.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-container-default\1.0-alpha-9-stable-1\plexus-container-default-1.0-alpha-9-stable-1.jar;C:\Users\<USER>\.m2\repository\classworlds\classworlds\1.1-alpha-2\classworlds-1.1-alpha-2.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\shared\file-management\1.2.1\file-management-1.2.1.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\shared\maven-shared-io\1.1\maven-shared-io-1.1.jar;C:\Users\<USER>\.m2\repository\org\apache\avro\avro-compiler\1.9.2\avro-compiler-1.9.2.jar;C:\Users\<USER>\.m2\repository\org\apache\velocity\velocity-engine-core\2.2\velocity-engine-core-2.2.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\com\sun\xml\bind\jaxb-core\4.0.5\jaxb-core-4.0.5.jar;C:\Users\<USER>\.m2\repository\org\eclipse\angus\angus-activation\2.0.2\angus-activation-2.0.2.jar;C:\Users\<USER>\.m2\repository\com\sun\xml\bind\jaxb-impl\4.0.5\jaxb-impl-4.0.5.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\jaxb-runtime\2.3.9\jaxb-runtime-2.3.9.jar;C:\Users\<USER>\.m2\repository\org\glassfish\jaxb\txw2\2.3.9\txw2-2.3.9.jar;C:\Users\<USER>\.m2\repository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;C:\Users\<USER>\.m2\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.5\jackson-datatype-jsr310-2.13.5.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\33.2.0-jre\guava-33.2.0-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.2\failureaccess-1.0.2.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\morningstar\mart-component-common\2.41.1-RELEASE\mart-component-common-2.41.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\com\amazonaws\aws-java-sdk-s3\1.12.120\aws-java-sdk-s3-1.12.120.jar;C:\Users\<USER>\.m2\repository\com\amazonaws\aws-java-sdk-kms\1.12.120\aws-java-sdk-kms-1.12.120.jar;C:\Users\<USER>\.m2\repository\commons-beanutils\commons-beanutils\1.9.4\commons-beanutils-1.9.4.jar;C:\Users\<USER>\.m2\repository\commons-collections\commons-collections\3.2.2\commons-collections-3.2.2.jar;C:\Users\<USER>\.m2\repository\com\amazonaws\aws-java-sdk-sqs\1.11.724\aws-java-sdk-sqs-1.11.724.jar;C:\Users\<USER>\.m2\repository\io\vavr\vavr\0.9.1\vavr-0.9.1.jar;C:\Users\<USER>\.m2\repository\io\vavr\vavr-match\0.9.1\vavr-match-0.9.1.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.16.1\commons-io-2.16.1.jar;C:\Users\<USER>\.m2\repository\org\lz4\lz4-java\1.8.0\lz4-java-1.8.0.jar;C:\Users\<USER>\.m2\repository\com\github\ben-manes\caffeine\caffeine\3.2.0\caffeine-3.2.0.jar;C:\Users\<USER>\.m2\repository\org\jspecify\jspecify\1.0.0\jspecify-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\morningstar\ecs-metadata-core\2.41.1-RELEASE\ecs-metadata-core-2.41.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\ecs\2.25.27\ecs-2.25.27.jar;C:\Users\<USER>\.m2\repository\com\auth0\java-jwt\4.0.0\java-jwt-4.0.0.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\s3\2.25.27\s3-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\aws-xml-protocol\2.25.27\aws-xml-protocol-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\aws-query-protocol\2.25.27\aws-query-protocol-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\arns\2.25.27\arns-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\crt-core\2.25.27\crt-core-2.25.27.jar;C:\Users\<USER>\.m2\repository\com\morningstar\mart-component-data-point\2.41.1-RELEASE\mart-component-data-point-2.41.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\com\morningstar\mart-component-entitlement\2.41.1-RELEASE\mart-component-entitlement-2.41.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\com\morningstar\mart-component-uim-core\2.41.1-RELEASE\mart-component-uim-core-2.41.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\com\morningstar\mart-component-gateway-core\2.41.1-RELEASE\mart-component-gateway-core-2.41.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\com\morningstar\mart-component-fixed-income-ice-data\2.41.1-RELEASE\mart-component-fixed-income-ice-data-2.41.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\2.7.18\spring-boot-starter-aop-2.7.18.jar;C:\Users\<USER>\.m2\repository\com\morningstar\mart-component-portfolio-holdings-service\2.41.1-RELEASE\mart-component-portfolio-holdings-service-2.41.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\com\morningstar\mart-component-portfolio-holdings-data\2.41.1-RELEASE\mart-component-portfolio-holdings-data-2.41.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\com\morningstar\mart-component-redisson-lock\2.41.1-RELEASE\mart-component-redisson-lock-2.41.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\org\redisson\redisson\3.15.0\redisson-3.15.0.jar;C:\Users\<USER>\.m2\repository\javax\cache\cache-api\1.1.1\cache-api-1.1.1.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxjava3\rxjava\3.0.7\rxjava-3.0.7.jar;C:\Users\<USER>\.m2\repository\org\jboss\marshalling\jboss-marshalling-river\2.0.10.Final\jboss-marshalling-river-2.0.10.Final.jar;C:\Users\<USER>\.m2\repository\org\jboss\marshalling\jboss-marshalling\2.0.10.Final\jboss-marshalling-2.0.10.Final.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.13.5\jackson-dataformat-yaml-2.13.5.jar;C:\Users\<USER>\.m2\repository\org\jodd\jodd-bean\5.1.6\jodd-bean-5.1.6.jar;C:\Users\<USER>\.m2\repository\org\jodd\jodd-core\5.1.6\jodd-core-5.1.6.jar;C:\Users\<USER>\.m2\repository\com\morningstar\mart-equity-package\2.41.1-RELEASE\mart-equity-package-2.41.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.7.18\spring-boot-starter-web-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.7.18\spring-boot-starter-tomcat-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.83\tomcat-embed-core-9.0.83.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.83\tomcat-embed-el-9.0.83.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.83\tomcat-embed-websocket-9.0.83.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.3.31\spring-webmvc-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\retry\spring-retry\1.3.4\spring-retry-1.3.4.jar;C:\Users\<USER>\.m2\repository\com\morningstar\entitlement-eod-data\4.3.1-RELEASE\entitlement-eod-data-4.3.1-RELEASE.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\com\google\protobuf\protobuf-java\3.25.1\protobuf-java-3.25.1.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-netty-shaded\1.58.0\grpc-netty-shaded-1.58.0.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.20.0\error_prone_annotations-2.20.0.jar;C:\Users\<USER>\.m2\repository\io\perfmark\perfmark-api\0.26.0\perfmark-api-0.26.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-core\1.58.0\grpc-core-1.58.0.jar;C:\Users\<USER>\.m2\repository\com\google\android\annotations\4.1.1.4\annotations-4.1.1.4.jar;C:\Users\<USER>\.m2\repository\org\codehaus\mojo\animal-sniffer-annotations\1.23\animal-sniffer-annotations-1.23.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-context\1.58.0\grpc-context-1.58.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-protobuf\1.58.0\grpc-protobuf-1.58.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-api\1.58.0\grpc-api-1.58.0.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\com\google\api\grpc\proto-google-common-protos\2.22.0\proto-google-common-protos-2.22.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-protobuf-lite\1.58.0\grpc-protobuf-lite-1.58.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-stub\1.58.0\grpc-stub-1.58.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-services\1.58.0\grpc-services-1.58.0.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-util\1.58.0\grpc-util-1.58.0.jar;C:\Users\<USER>\.m2\repository\com\google\protobuf\protobuf-java-util\3.24.0\protobuf-java-util-3.24.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\2.8\j2objc-annotations-2.8.jar;C:\Users\<USER>\.m2\repository\net\devh\grpc-spring-boot-starter\2.15.0.RELEASE\grpc-spring-boot-starter-2.15.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\net\devh\grpc-server-spring-boot-starter\2.15.0.RELEASE\grpc-server-spring-boot-starter-2.15.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\net\devh\grpc-server-spring-boot-autoconfigure\2.15.0.RELEASE\grpc-server-spring-boot-autoconfigure-2.15.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\net\devh\grpc-common-spring-boot\2.15.0.RELEASE\grpc-common-spring-boot-2.15.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\grpc\grpc-inprocess\1.58.0\grpc-inprocess-1.58.0.jar;C:\Users\<USER>\.m2\repository\net\devh\grpc-client-spring-boot-starter\2.15.0.RELEASE\grpc-client-spring-boot-starter-2.15.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\net\devh\grpc-client-spring-boot-autoconfigure\2.15.0.RELEASE\grpc-client-spring-boot-autoconfigure-2.15.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\mysql\mysql-connector-java\8.0.16\mysql-connector-java-8.0.16.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-webflux\2.7.18\spring-boot-starter-webflux-2.7.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.7.18\spring-boot-starter-json-2.7.18.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.5\jackson-datatype-jdk8-2.13.5.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.5\jackson-module-parameter-names-2.13.5.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-reactor-netty\2.7.18\spring-boot-starter-reactor-netty-2.7.18.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-test\3.4.41\reactor-test-3.4.41.jar;C:\Users\<USER>\.m2\repository\javax\inject\javax.inject\1\javax.inject-1.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.26\lombok-1.18.26.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger2\3.0.0\springfox-swagger2-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spi\3.0.0\springfox-spi-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-core\3.0.0\springfox-core-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-schema\3.0.0\springfox-schema-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-common\3.0.0\springfox-swagger-common-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-annotations\2.1.2\swagger-annotations-2.1.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-web\3.0.0\springfox-spring-web-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\github\classgraph\classgraph\4.8.83\classgraph-4.8.83.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-webmvc\3.0.0\springfox-spring-webmvc-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-webflux\3.0.0\springfox-spring-webflux-3.0.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\2.0.0.RELEASE\spring-plugin-core-2.0.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.3.31\spring-context-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.3.31\spring-aop-5.3.31.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-metadata\2.0.0.RELEASE\spring-plugin-metadata-2.0.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-annotations\1.5.20\swagger-annotations-1.5.20.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-models\1.5.20\swagger-models-1.5.20.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.3.1.Final\mapstruct-1.3.1.Final.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-boot-starter\3.0.0\springfox-boot-starter-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-oas\3.0.0\springfox-oas-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\core\v3\swagger-models\2.1.2\swagger-models-2.1.2.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-data-rest\3.0.0\springfox-data-rest-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-bean-validators\3.0.0\springfox-bean-validators-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-ui\3.0.0\springfox-swagger-ui-3.0.0.jar;C:\Users\<USER>\.m2\repository\io\lettuce\lettuce-core\6.1.6.RELEASE\lettuce-core-6.1.6.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\5.3.37\spring-test-5.3.37.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\junit\vintage\junit-vintage-engine\5.8.2\junit-vintage-engine-5.8.2.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.8.2\junit-platform-engine-1.8.2.jar;C:\Users\<USER>\.m2\repository\junit\junit\4.13.2\junit-4.13.2.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-core\2.2\hamcrest-core-2.2.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\5.2.0\mockito-core-5.2.0.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-inline\5.2.0\mockito-inline-5.2.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.14.11\byte-buddy-1.14.11.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.14.11\byte-buddy-agent-1.14.11.jar;C:\Users\<USER>\.m2\repository\com\amazonaws\aws-java-sdk-lambda\1.12.120\aws-java-sdk-lambda-1.12.120.jar;C:\Users\<USER>\.m2\repository\com\amazonaws\aws-java-sdk-core\1.12.120\aws-java-sdk-core-1.12.120.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.1.3\commons-logging-1.1.3.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;C:\Users\<USER>\.m2\repository\software\amazon\ion\ion-java\1.0.2\ion-java-1.0.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-cbor\2.13.5\jackson-dataformat-cbor-2.13.5.jar;C:\Users\<USER>\.m2\repository\com\amazonaws\jmespath-java\1.12.120\jmespath-java-1.12.120.jar;C:\Users\<USER>\.m2\repository\com\morningstar\content-service-data-point-columns-util\1.0.2-RELEASE\content-service-data-point-columns-util-1.0.2-RELEASE.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\dynamodb\2.25.27\dynamodb-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\aws-json-protocol\2.25.27\aws-json-protocol-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\third-party-jackson-core\2.25.27\third-party-jackson-core-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\protocol-core\2.25.27\protocol-core-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\profiles\2.25.27\profiles-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\http-auth-aws\2.25.27\http-auth-aws-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\checksums-spi\2.25.27\checksums-spi-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\checksums\2.25.27\checksums-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\sdk-core\2.25.27\sdk-core-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\auth\2.25.27\auth-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\eventstream\eventstream\1.0.1\eventstream-1.0.1.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\http-auth-spi\2.25.27\http-auth-spi-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\http-auth\2.25.27\http-auth-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\identity-spi\2.25.27\identity-spi-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\http-client-spi\2.25.27\http-client-spi-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\regions\2.25.27\regions-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\annotations\2.25.27\annotations-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\utils\2.25.27\utils-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\aws-core\2.25.27\aws-core-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\metrics-spi\2.25.27\metrics-spi-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\json-utils\2.25.27\json-utils-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\endpoints-spi\2.25.27\endpoints-spi-2.25.27.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\apache-client\2.25.27\apache-client-2.25.27.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\netty-nio-client\2.25.27\netty-nio-client-2.25.27.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http\4.1.101.Final\netty-codec-http-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec-http2\4.1.101.Final\netty-codec-http2-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.101.Final\netty-codec-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.101.Final\netty-transport-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.101.Final\netty-common-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.101.Final\netty-buffer-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.101.Final\netty-handler-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-native-unix-common\4.1.101.Final\netty-transport-native-unix-common-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport-classes-epoll\4.1.101.Final\netty-transport-classes-epoll-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.101.Final\netty-resolver-4.1.101.Final.jar;C:\Users\<USER>\.m2\repository\software\amazon\awssdk\dynamodb-enhanced\2.25.27\dynamodb-enhanced-2.25.27.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Program Files\Java\jdk-17"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="C:\Zeng\dataac\msstash1\mart-api\martapi"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="FILE_LOG_CHARSET" value="windows-1252"/>
    <property name="java.awt.headless" value="true"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="17.0.9+11-LTS-201"/>
    <property name="user.name" value="wzeng1"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="Cp1252"/>
    <property name="spring.beaninfo.ignore" value="true"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="C:\Users\<USER>\.m2\repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="17.0.9"/>
    <property name="user.dir" value="C:\Zeng\dataac\msstash1\mart-api\martapi"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="PID" value="55228"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="CONSOLE_LOG_CHARSET" value="windows-1252"/>
    <property name="native.encoding" value="Cp1252"/>
    <property name="java.library.path" value="C:\Program Files\Java\jdk-17\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\Program Files\PowerShell\7;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Amazon\AWSCLIV2\;C:\Program Files (x86)\Morningstar\Office BOS\Bin;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\dotnet\;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Zeng\develop\glue\spark-3.3.2-bin-hadoop3\bin;C:\Zeng\develop\glue\aws-glue-libs-3.0\bin;C:\Program Files\Amazon\AWSSAMCLI\bin\;C:\Program Files (x86)\Microsoft SQL Server\160\DTS\Binn\;C:\Program Files\Azure Data Studio\bin;C:\Program Files (x86)\Gow\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Zeng\msstash\mine-code\scripts\sh\;C:\Program Files (x86)\GitExtensions\;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\Microsoft SQL Server\130\Tools\Binn\;C:\Program Files (x86)\Windows Kits\8.1\Windows Performance Toolkit\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\Zeng\msstashuis\uis\DebuggerPath;C:\Users\<USER>\Zeng\msstashuis\uis\DebuggerPath\Bin;C:\Program Files (x86)\Morningstar\Office BOS\Bin;C:\Program Files\PowerShell\7\;C:\Users\<USER>\.dnx\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Git\cmd;C:\cmder;c:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.9_qbz5n2kfra8p0\LocalCache\local-packages\Python39\Scripts;C:\apache-maven-3.8.6\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\terraform_1.5.7;C:\Program Files\Azure Data Studio\bin;C:\Users\<USER>\AppData\Roaming\Programs\Zero Install;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="17.0.9+11-LTS-201"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="contextLoads" classname="com.morningstar.martapi.grpc.TimeSeriesGrpcServiceTest" time="0.011">
    <error message="Failed to load ApplicationContext" type="java.lang.IllegalStateException"><![CDATA[java.lang.IllegalStateException: Failed to load ApplicationContext
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:98)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:124)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:248)
	at org.springframework.test.context.junit.jupiter.SpringExtension.postProcessTestInstance(SpringExtension.java:138)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$8(ClassBasedTestDescriptor.java:363)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.executeAndMaskThrowable(ClassBasedTestDescriptor.java:368)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$9(ClassBasedTestDescriptor.java:363)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.StreamSpliterators$WrappingSpliterator.forEachRemaining(StreamSpliterators.java:310)
	at java.base/java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:735)
	at java.base/java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:734)
	at java.base/java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:762)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.invokeTestInstancePostProcessors(ClassBasedTestDescriptor.java:362)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$instantiateAndPostProcessTestInstance$6(ClassBasedTestDescriptor.java:283)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.instantiateAndPostProcessTestInstance(ClassBasedTestDescriptor.java:282)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$4(ClassBasedTestDescriptor.java:272)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$5(ClassBasedTestDescriptor.java:271)
	at org.junit.jupiter.engine.execution.TestInstancesProvider.getTestInstances(TestInstancesProvider.java:31)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$prepare$0(TestMethodTestDescriptor.java:102)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:101)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:66)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$prepare$2(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.prepare(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:90)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:107)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:88)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:67)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:52)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:114)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:86)
	at org.junit.platform.launcher.core.DefaultLauncherSession$DelegatingLauncher.execute(DefaultLauncherSession.java:86)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisMessageConfig' defined in file [C:\Zeng\dataac\msstash1\mart-api\martapi\target\classes\com\morningstar\martapi\config\RedisMessageConfig.class]: Unexpected exception during bean creation; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'topic.sync' in value "${topic.sync}"
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:555)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:136)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:141)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:90)
	... 72 more
Caused by: java.lang.IllegalArgumentException: Could not resolve placeholder 'topic.sync' in value "${topic.sync}"
	at org.springframework.util.PropertyPlaceholderHelper.parseStringValue(PropertyPlaceholderHelper.java:180)
	at org.springframework.util.PropertyPlaceholderHelper.replacePlaceholders(PropertyPlaceholderHelper.java:126)
	at org.springframework.core.env.AbstractPropertyResolver.doResolvePlaceholders(AbstractPropertyResolver.java:239)
	at org.springframework.core.env.AbstractPropertyResolver.resolveRequiredPlaceholders(AbstractPropertyResolver.java:210)
	at org.springframework.context.support.PropertySourcesPlaceholderConfigurer.lambda$processProperties$0(PropertySourcesPlaceholderConfigurer.java:191)
	at org.springframework.beans.factory.support.AbstractBeanFactory.resolveEmbeddedValue(AbstractBeanFactory.java:936)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1332)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:224)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	... 85 more
]]></error>
    <system-out><![CDATA[2025-06-24 18:25:56.410 509  INFO  [main           ] : service_name="Mart API" request_id= Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.morningstar.martapi.grpc.TimeSeriesGrpcServiceTest], using SpringBootContextLoader 
2025-06-24 18:25:56.425 524  INFO  [main           ] : service_name="Mart API" request_id= Could not detect default resource locations for test class [com.morningstar.martapi.grpc.TimeSeriesGrpcServiceTest]: no resource found for suffixes {-context.xml, Context.groovy}. 
2025-06-24 18:25:56.427 526  INFO  [main           ] : service_name="Mart API" request_id= Could not detect default configuration classes for test class [com.morningstar.martapi.grpc.TimeSeriesGrpcServiceTest]: TimeSeriesGrpcServiceTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration. 
2025-06-24 18:25:56.742 841  INFO  [main           ] : service_name="Mart API" request_id= Found @SpringBootConfiguration com.morningstar.martapi.MartAPIApplication for test class com.morningstar.martapi.grpc.TimeSeriesGrpcServiceTest 
2025-06-24 18:25:57.028 1127 INFO  [main           ] : service_name="Mart API" request_id= Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener] 
2025-06-24 18:25:57.057 1156 INFO  [main           ] : service_name="Mart API" request_id= Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@1a464fa3, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@4215e133, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@5ccb85d6, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@d88f893, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@259b85d6, org.springframework.test.context.support.DirtiesContextTestExecutionListener@48eaf42f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@488f3dd1, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2091833, org.springframework.test.context.event.EventPublishingTestExecutionListener@7bc58891, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@6411504a, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@554188ac, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@1f43cab7, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@481558ce, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@2668c286, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@7f353a0f] 



███╗   ███╗ █████╗ ██████╗ ████████╗     ██████╗  █████╗ ████████╗███████╗██╗    ██╗ █████╗ ██╗   ██╗
████╗ ████║██╔══██╗██╔══██╗╚══██╔══╝    ██╔════╝ ██╔══██╗╚══██╔══╝██╔════╝██║    ██║██╔══██╗╚██╗ ██╔╝
██╔████╔██║███████║██████╔╝   ██║       ██║  ███╗███████║   ██║   █████╗  ██║ █╗ ██║███████║ ╚████╔╝
██║╚██╔╝██║██╔══██║██╔══██╗   ██║       ██║   ██║██╔══██║   ██║   ██╔══╝  ██║███╗██║██╔══██║  ╚██╔╝
██║ ╚═╝ ██║██║  ██║██║  ██║   ██║       ╚██████╔╝██║  ██║   ██║   ███████╗╚███╔███╔╝██║  ██║   ██║
╚═╝     ╚═╝╚═╝  ╚═╝╚═╝  ╚═╝   ╚═╝        ╚═════╝ ╚═╝  ╚═╝   ╚═╝   ╚══════╝ ╚══╝╚══╝ ╚═╝  ╚═╝   ╚═╝
© Copyright 2024 Morningstar, Inc. All rights reserved.
application name: mart-api  version: ${martgateway.version}  |  spring-boot version: 2.7.18


2025-06-24 18:25:57.701 1800 INFO  [main           ] : service_name="Mart API" request_id= Starting TimeSeriesGrpcServiceTest using Java 17.0.9 on CA-JT26LR3 with PID 55228 (started by wzeng1 in C:\Zeng\dataac\msstash1\mart-api\martapi) 
2025-06-24 18:25:57.702 1801 INFO  [main           ] : service_name="Mart API" request_id= The following 1 profile is active: "test" 
2025-06-24 18:25:57.770 1869 WARN  [main           ] : service_name="Mart API" request_id= Property 'spring.profiles' imported from location 'class path resource [application.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [application.yml] - 546:13] 
2025-06-24 18:25:57.770 1869 WARN  [main           ] : service_name="Mart API" request_id= Property 'spring.profiles' imported from location 'class path resource [application.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [application.yml] - 491:13] 
2025-06-24 18:25:57.770 1869 WARN  [main           ] : service_name="Mart API" request_id= Property 'spring.profiles' imported from location 'class path resource [application.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [application.yml] - 430:13] 
2025-06-24 18:25:57.770 1869 WARN  [main           ] : service_name="Mart API" request_id= Property 'spring.profiles' imported from location 'class path resource [application.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [application.yml] - 371:13] 
2025-06-24 18:25:57.770 1869 WARN  [main           ] : service_name="Mart API" request_id= Property 'spring.profiles' imported from location 'class path resource [application.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [application.yml] - 317:13] 
2025-06-24 18:25:57.770 1869 WARN  [main           ] : service_name="Mart API" request_id= Property 'spring.profiles' imported from location 'class path resource [application.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [application.yml] - 257:13] 
2025-06-24 18:25:57.770 1869 WARN  [main           ] : service_name="Mart API" request_id= Property 'spring.profiles' imported from location 'class path resource [application.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [application.yml] - 203:13] 
2025-06-24 18:25:57.770 1869 WARN  [main           ] : service_name="Mart API" request_id= Property 'spring.profiles' imported from location 'class path resource [application.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [application.yml] - 150:13] 
2025-06-24 18:25:57.770 1869 WARN  [main           ] : service_name="Mart API" request_id= Property 'spring.profiles' imported from location 'class path resource [application.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [application.yml] - 96:13] 
2025-06-24 18:25:57.770 1869 WARN  [main           ] : service_name="Mart API" request_id= Property 'spring.profiles' imported from location 'class path resource [application.yml]' is invalid and should be replaced with 'spring.config.activate.on-profile' [origin: class path resource [application.yml] - 36:13] 
2025-06-24 18:26:00.876 4975 INFO  [main           ] : service_name="Mart API" request_id= DataPointLoaderRegistrar is running 
2025-06-24 18:26:00.888 4987 INFO  [main           ] : service_name="Mart API" request_id= EntitlementFilterLoaderRegistrar is running 
2025-06-24 18:26:00.891 4990 INFO  [main           ] : service_name="Mart API" request_id= DataPointLoaderRegistrar is running 
2025-06-24 18:26:00.943 5042 INFO  [main           ] : service_name="Mart API" request_id= EntitlementFilterLoaderRegistrar is running 
2025-06-24 18:26:01.021 5120 INFO  [main           ] : service_name="Mart API" request_id= DataPointLoaderRegistrar is running 
2025-06-24 18:26:01.023 5122 INFO  [main           ] : service_name="Mart API" request_id= EntitlementFilterLoaderRegistrar is running 
2025-06-24 18:26:01.353 5452 INFO  [main           ] : service_name="Mart API" request_id= DataPointLoaderRegistrar is running 
2025-06-24 18:26:02.055 6154 INFO  [main           ] : service_name="Mart API" request_id= Multiple Spring Data modules found, entering strict repository configuration mode 
2025-06-24 18:26:02.061 6160 INFO  [main           ] : service_name="Mart API" request_id= Bootstrapping Spring Data Elasticsearch repositories in DEFAULT mode. 
2025-06-24 18:26:02.130 6229 INFO  [main           ] : service_name="Mart API" request_id= Finished Spring Data repository scanning in 52 ms. Found 0 Elasticsearch repository interfaces. 
2025-06-24 18:26:02.139 6238 INFO  [main           ] : service_name="Mart API" request_id= Multiple Spring Data modules found, entering strict repository configuration mode 
2025-06-24 18:26:02.141 6240 INFO  [main           ] : service_name="Mart API" request_id= Bootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode. 
2025-06-24 18:26:02.162 6261 INFO  [main           ] : service_name="Mart API" request_id= Finished Spring Data repository scanning in 20 ms. Found 0 Reactive Elasticsearch repository interfaces. 
2025-06-24 18:26:02.173 6272 INFO  [main           ] : service_name="Mart API" request_id= Multiple Spring Data modules found, entering strict repository configuration mode 
2025-06-24 18:26:02.174 6273 INFO  [main           ] : service_name="Mart API" request_id= Bootstrapping Spring Data MongoDB repositories in DEFAULT mode. 
2025-06-24 18:26:02.196 6295 INFO  [main           ] : service_name="Mart API" request_id= Finished Spring Data repository scanning in 22 ms. Found 0 MongoDB repository interfaces. 
2025-06-24 18:26:02.222 6321 INFO  [main           ] : service_name="Mart API" request_id= Multiple Spring Data modules found, entering strict repository configuration mode 
2025-06-24 18:26:02.228 6327 INFO  [main           ] : service_name="Mart API" request_id= Bootstrapping Spring Data Redis repositories in DEFAULT mode. 
2025-06-24 18:26:02.294 6393 INFO  [main           ] : service_name="Mart API" request_id= Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces. 
2025-06-24 18:26:02.636 6735 TRACE [main           ] : service_name="Mart API" request_id= Scanning URL [jar:file:/C:/Users/<USER>/.m2/repository/com/morningstar/mart-gateway/2.41.1-RELEASE/mart-gateway-2.41.1-RELEASE.jar!/com/morningstar/martgateway/domains/eod/repository/EODMongodbRepo.class] 
2025-06-24 18:26:02.636 6735 DEBUG [main           ] : service_name="Mart API" request_id= Ignored because not a concrete top-level class: URL [jar:file:/C:/Users/<USER>/.m2/repository/com/morningstar/mart-gateway/2.41.1-RELEASE/mart-gateway-2.41.1-RELEASE.jar!/com/morningstar/martgateway/domains/eod/repository/EODMongodbRepo.class] 
2025-06-24 18:26:02.636 6735 TRACE [main           ] : service_name="Mart API" request_id= Scanning URL [jar:file:/C:/Users/<USER>/.m2/repository/com/morningstar/mart-gateway/2.41.1-RELEASE/mart-gateway-2.41.1-RELEASE.jar!/com/morningstar/martgateway/domains/eod/repository/EODRepo.class] 
2025-06-24 18:26:02.636 6735 DEBUG [main           ] : service_name="Mart API" request_id= Identified candidate component class: URL [jar:file:/C:/Users/<USER>/.m2/repository/com/morningstar/mart-gateway/2.41.1-RELEASE/mart-gateway-2.41.1-RELEASE.jar!/com/morningstar/martgateway/domains/eod/repository/EODRepo.class] 
2025-06-24 18:26:02.636 6735 TRACE [main           ] : service_name="Mart API" request_id= Scanning URL [jar:file:/C:/Users/<USER>/.m2/repository/com/morningstar/mart-gateway/2.41.1-RELEASE/mart-gateway-2.41.1-RELEASE.jar!/com/morningstar/martgateway/domains/eod/repository/EODAsyncRepo.class] 
2025-06-24 18:26:02.636 6735 DEBUG [main           ] : service_name="Mart API" request_id= Ignored because not a concrete top-level class: URL [jar:file:/C:/Users/<USER>/.m2/repository/com/morningstar/mart-gateway/2.41.1-RELEASE/mart-gateway-2.41.1-RELEASE.jar!/com/morningstar/martgateway/domains/eod/repository/EODAsyncRepo.class] 
2025-06-24 18:26:02.636 6735 TRACE [main           ] : service_name="Mart API" request_id= Scanning URL [jar:file:/C:/Users/<USER>/.m2/repository/com/morningstar/mart-gateway/2.41.1-RELEASE/mart-gateway-2.41.1-RELEASE.jar!/com/morningstar/martgateway/domains/eod/repository/EODMongodbAsyncRepo.class] 
2025-06-24 18:26:02.636 6735 DEBUG [main           ] : service_name="Mart API" request_id= Ignored because not a concrete top-level class: URL [jar:file:/C:/Users/<USER>/.m2/repository/com/morningstar/mart-gateway/2.41.1-RELEASE/mart-gateway-2.41.1-RELEASE.jar!/com/morningstar/martgateway/domains/eod/repository/EODMongodbAsyncRepo.class] 
2025-06-24 18:26:02.638 6737 DEBUG [main           ] : service_name="Mart API" request_id= Creating MapperFactoryBean with name 'EODRepo' and 'com.morningstar.martgateway.domains.eod.repository.EODRepo' mapperInterface 
2025-06-24 18:26:02.641 6740 TRACE [main           ] : service_name="Mart API" request_id= Scanning URL [jar:file:/C:/Users/<USER>/.m2/repository/com/morningstar/mart-gateway/2.41.1-RELEASE/mart-gateway-2.41.1-RELEASE.jar!/com/morningstar/martgateway/infrastructures/repo/data/RdbDataAsyncRepo.class] 
2025-06-24 18:26:02.641 6740 DEBUG [main           ] : service_name="Mart API" request_id= Ignored because not a concrete top-level class: URL [jar:file:/C:/Users/<USER>/.m2/repository/com/morningstar/mart-gateway/2.41.1-RELEASE/mart-gateway-2.41.1-RELEASE.jar!/com/morningstar/martgateway/infrastructures/repo/data/RdbDataAsyncRepo.class] 
2025-06-24 18:26:02.641 6740 TRACE [main           ] : service_name="Mart API" request_id= Scanning URL [jar:file:/C:/Users/<USER>/.m2/repository/com/morningstar/mart-gateway/2.41.1-RELEASE/mart-gateway-2.41.1-RELEASE.jar!/com/morningstar/martgateway/infrastructures/repo/data/RdbDataRepo.class] 
2025-06-24 18:26:02.641 6740 DEBUG [main           ] : service_name="Mart API" request_id= Identified candidate component class: URL [jar:file:/C:/Users/<USER>/.m2/repository/com/morningstar/mart-gateway/2.41.1-RELEASE/mart-gateway-2.41.1-RELEASE.jar!/com/morningstar/martgateway/infrastructures/repo/data/RdbDataRepo.class] 
2025-06-24 18:26:02.642 6741 TRACE [main           ] : service_name="Mart API" request_id= Scanning URL [jar:file:/C:/Users/<USER>/.m2/repository/com/morningstar/mart-gateway/2.41.1-RELEASE/mart-gateway-2.41.1-RELEASE.jar!/com/morningstar/martgateway/infrastructures/repo/data/S3DataRepo.class] 
2025-06-24 18:26:02.642 6741 DEBUG [main           ] : service_name="Mart API" request_id= Ignored because not a concrete top-level class: URL [jar:file:/C:/Users/<USER>/.m2/repository/com/morningstar/mart-gateway/2.41.1-RELEASE/mart-gateway-2.41.1-RELEASE.jar!/com/morningstar/martgateway/infrastructures/repo/data/S3DataRepo.class] 
2025-06-24 18:26:02.642 6741 TRACE [main           ] : service_name="Mart API" request_id= Scanning URL [jar:file:/C:/Users/<USER>/.m2/repository/com/morningstar/mart-gateway/2.41.1-RELEASE/mart-gateway-2.41.1-RELEASE.jar!/com/morningstar/martgateway/infrastructures/repo/data/AWSRequestSigningApacheInterceptor.class] 
2025-06-24 18:26:02.642 6741 DEBUG [main           ] : service_name="Mart API" request_id= Ignored because not a concrete top-level class: URL [jar:file:/C:/Users/<USER>/.m2/repository/com/morningstar/mart-gateway/2.41.1-RELEASE/mart-gateway-2.41.1-RELEASE.jar!/com/morningstar/martgateway/infrastructures/repo/data/AWSRequestSigningApacheInterceptor.class] 
2025-06-24 18:26:02.642 6741 DEBUG [main           ] : service_name="Mart API" request_id= Creating MapperFactoryBean with name 'rdbDataRepo' and 'com.morningstar.martgateway.infrastructures.repo.data.RdbDataRepo' mapperInterface 
2025-06-24 18:26:02.643 6742 DEBUG [main           ] : service_name="Mart API" request_id= Enabling autowire by type for MapperFactoryBean with name 'rdbDataRepo'. 
2025-06-24 18:26:09.775 13874 DEBUG [main           ] : service_name="Mart API" request_id= Property 'configuration' or 'configLocation' not specified, using default MyBatis Configuration 
2025-06-24 18:26:09.891 13990 DEBUG [main           ] : service_name="Mart API" request_id= Property 'mapperLocations' was not specified. 
2025-06-24 18:26:09.958 14057 DEBUG [main           ] : service_name="Mart API" request_id= Property 'configuration' or 'configLocation' not specified, using default MyBatis Configuration 
2025-06-24 18:26:09.958 14057 DEBUG [main           ] : service_name="Mart API" request_id= Property 'mapperLocations' was not specified. 
2025-06-24 18:26:12.968 17067 WARN  [main           ] : service_name="Mart API" request_id= Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisMessageConfig' defined in file [C:\Zeng\dataac\msstash1\mart-api\martapi\target\classes\com\morningstar\martapi\config\RedisMessageConfig.class]: Unexpected exception during bean creation; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'topic.sync' in value "${topic.sync}" 
2025-06-24 18:26:13.053 17152 INFO  [main           ] : service_name="Mart API" request_id= 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled. 
2025-06-24 18:26:13.095 17194 ERROR [main           ] : service_name="Mart API" request_id= Application run failed 
java.lang.IllegalArgumentException: Could not resolve placeholder 'topic.sync' in value "${topic.sync}"
	at org.springframework.util.PropertyPlaceholderHelper.parseStringValue(PropertyPlaceholderHelper.java:180)
	at org.springframework.util.PropertyPlaceholderHelper.replacePlaceholders(PropertyPlaceholderHelper.java:126)
	at org.springframework.core.env.AbstractPropertyResolver.doResolvePlaceholders(AbstractPropertyResolver.java:239)
	at org.springframework.core.env.AbstractPropertyResolver.resolveRequiredPlaceholders(AbstractPropertyResolver.java:210)
	at org.springframework.context.support.PropertySourcesPlaceholderConfigurer.lambda$processProperties$0(PropertySourcesPlaceholderConfigurer.java:191)
	at org.springframework.beans.factory.support.AbstractBeanFactory.resolveEmbeddedValue(AbstractBeanFactory.java:936)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1332)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:224)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	... 85 common frames omitted
Wrapped by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisMessageConfig' defined in file [C:\Zeng\dataac\msstash1\mart-api\martapi\target\classes\com\morningstar\martapi\config\RedisMessageConfig.class]: Unexpected exception during bean creation; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'topic.sync' in value "${topic.sync}"
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:555)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:136)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:141)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:90)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:124)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:248)
	at org.springframework.test.context.junit.jupiter.SpringExtension.postProcessTestInstance(SpringExtension.java:138)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$8(ClassBasedTestDescriptor.java:363)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.executeAndMaskThrowable(ClassBasedTestDescriptor.java:368)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$9(ClassBasedTestDescriptor.java:363)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.StreamSpliterators$WrappingSpliterator.forEachRemaining(StreamSpliterators.java:310)
	at java.base/java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:735)
	at java.base/java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:734)
	at java.base/java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:762)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.invokeTestInstancePostProcessors(ClassBasedTestDescriptor.java:362)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$instantiateAndPostProcessTestInstance$6(ClassBasedTestDescriptor.java:283)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.instantiateAndPostProcessTestInstance(ClassBasedTestDescriptor.java:282)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$4(ClassBasedTestDescriptor.java:272)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$5(ClassBasedTestDescriptor.java:271)
	at org.junit.jupiter.engine.execution.TestInstancesProvider.getTestInstances(TestInstancesProvider.java:31)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$prepare$0(TestMethodTestDescriptor.java:102)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:101)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:66)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$prepare$2(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.prepare(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:90)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:107)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:88)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:67)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:52)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:114)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:86)
	at org.junit.platform.launcher.core.DefaultLauncherSession$DelegatingLauncher.execute(DefaultLauncherSession.java:86)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
2025-06-24 18:26:13.097 17196 ERROR [main           ] : service_name="Mart API" request_id= Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@1a464fa3] to prepare test instance [com.morningstar.martapi.grpc.TimeSeriesGrpcServiceTest@539ef2aa] 
java.lang.IllegalArgumentException: Could not resolve placeholder 'topic.sync' in value "${topic.sync}"
	at org.springframework.util.PropertyPlaceholderHelper.parseStringValue(PropertyPlaceholderHelper.java:180)
	at org.springframework.util.PropertyPlaceholderHelper.replacePlaceholders(PropertyPlaceholderHelper.java:126)
	at org.springframework.core.env.AbstractPropertyResolver.doResolvePlaceholders(AbstractPropertyResolver.java:239)
	at org.springframework.core.env.AbstractPropertyResolver.resolveRequiredPlaceholders(AbstractPropertyResolver.java:210)
	at org.springframework.context.support.PropertySourcesPlaceholderConfigurer.lambda$processProperties$0(PropertySourcesPlaceholderConfigurer.java:191)
	at org.springframework.beans.factory.support.AbstractBeanFactory.resolveEmbeddedValue(AbstractBeanFactory.java:936)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1332)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:224)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	... 85 common frames omitted
Wrapped by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'redisMessageConfig' defined in file [C:\Zeng\dataac\msstash1\mart-api\martapi\target\classes\com\morningstar\martapi\config\RedisMessageConfig.class]: Unexpected exception during bean creation; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'topic.sync' in value "${topic.sync}"
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:555)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:136)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:141)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:90)
	... 72 common frames omitted
Wrapped by: java.lang.IllegalStateException: Failed to load ApplicationContext
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:98)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:124)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:248)
	at org.springframework.test.context.junit.jupiter.SpringExtension.postProcessTestInstance(SpringExtension.java:138)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$8(ClassBasedTestDescriptor.java:363)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.executeAndMaskThrowable(ClassBasedTestDescriptor.java:368)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$9(ClassBasedTestDescriptor.java:363)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179)
	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1625)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.StreamSpliterators$WrappingSpliterator.forEachRemaining(StreamSpliterators.java:310)
	at java.base/java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:735)
	at java.base/java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:734)
	at java.base/java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:762)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.invokeTestInstancePostProcessors(ClassBasedTestDescriptor.java:362)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$instantiateAndPostProcessTestInstance$6(ClassBasedTestDescriptor.java:283)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.instantiateAndPostProcessTestInstance(ClassBasedTestDescriptor.java:282)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$4(ClassBasedTestDescriptor.java:272)
	at java.base/java.util.Optional.orElseGet(Optional.java:364)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$5(ClassBasedTestDescriptor.java:271)
	at org.junit.jupiter.engine.execution.TestInstancesProvider.getTestInstances(TestInstancesProvider.java:31)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$prepare$0(TestMethodTestDescriptor.java:102)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:101)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:66)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$prepare$2(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.prepare(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:90)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:107)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:88)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:67)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:52)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:114)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:86)
	at org.junit.platform.launcher.core.DefaultLauncherSession$DelegatingLauncher.execute(DefaultLauncherSession.java:86)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:56)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)
]]></system-out>
  </testcase>
</testsuite>