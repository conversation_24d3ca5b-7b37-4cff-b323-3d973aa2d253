<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SecurityController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <a href="index.source.html" class="el_package">com.morningstar.martapi.controller</a> &gt; <span class="el_source">SecurityController.java</span></div><h1>SecurityController.java</h1><pre class="source lang-java linenums">package com.morningstar.martapi.controller;

import com.morningstar.martapi.util.DateParamInputUtil;
import com.morningstar.martgateway.domains.core.entity.response.MartResponse;
import com.morningstar.martgateway.infrastructures.log.LogAttribute;
import com.morningstar.martgateway.infrastructures.log.LogEntity;
import com.morningstar.martgateway.infrastructures.log.LogEntry;
import com.morningstar.martgateway.infrastructures.log.LogHelper;
import com.morningstar.martgateway.interfaces.MartGateway;
import com.morningstar.martcommon.entity.result.request.MartRequest;
import com.morningstar.martgateway.util.JsonUtils;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import javax.inject.Inject;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.morningstar.martapi.config.Constant.RESPONSE;
import static com.morningstar.martapi.config.Constant.RESPONSE_ERROR;
import static com.morningstar.martapi.config.Constant.SUCCESSFUL_RESPONSE;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.EVENT_DESCRIPTION;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.EVENT_TYPE;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.EXCEPTION_TYPE;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.EXECUTE_TIME;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.PRODUCT_ID;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.REQUEST_PARAM;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.REQUEST_PAYLOAD;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.REQUEST_TYPE;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.USER_ID;

@RestController
@RequestMapping(value = {&quot;/v1/security-data&quot;,&quot;/investment-api/v1/security-data&quot;},
        produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE})
public class SecurityController {

<span class="nc" id="L57">    private static final Logger log = LoggerFactory.getLogger(SecurityController.class);</span>

    private MartGateway martGateway;

    private static final String REQUEST_TYPE_VALUE = &quot;martapi&quot;;


    @Inject
<span class="nc" id="L65">    public SecurityController(MartGateway martGateway) {</span>
<span class="nc" id="L66">        this.martGateway = martGateway;</span>
<span class="nc" id="L67">    }</span>

    @GetMapping(value = &quot;/investment-id/{idList}&quot;)
    public Mono&lt;MartResponse&gt; getDatapointValues(
            @ApiParam(example = &quot;0P00000AWG,0P00000AYI,0P00000AZ0&quot;)
            @PathVariable(&quot;idList&quot;) String idList,
            @ApiParam(example = &quot;119114,119115&quot;)
            @RequestParam(value = &quot;dps&quot;, required = true) String dpList,
            @ApiParam(example = &quot;2022-10-31&quot;)
            @RequestParam(value = &quot;start-date&quot;, required = false, defaultValue = &quot;&quot;) String startDate,
            @ApiParam(example = &quot;2022-11-30&quot;)
            @RequestParam(value = &quot;end-date&quot;, required = false, defaultValue = &quot;&quot;) String endDate,
            @ApiParam(example = &quot;EUR&quot;)
            @RequestParam(value = &quot;currency&quot;, required = false, defaultValue = &quot;&quot;) String currency,
            @ApiParam(example = &quot;USD&quot;)
            @RequestParam(value = &quot;precurrency&quot;, required = false, defaultValue = &quot;&quot;) String preCurrency,
            @ApiParam(example = &quot;false&quot;)
            @RequestParam(value = &quot;read-cache&quot;, required = false, defaultValue = &quot;&quot;) String readCache,
            @RequestParam(value = &quot;due-stage&quot;, required = false, defaultValue = &quot;0&quot;) String dueStage,
            @RequestParam(value= &quot;date-format&quot;, required = false, defaultValue = &quot;&quot;) String dateFormat,
            @RequestParam(value= &quot;decimal-format&quot;, required = false, defaultValue = &quot;&quot;) String decimalFormat,
            @RequestParam(value= &quot;post-tax&quot;, required = false, defaultValue = &quot;&quot;) String postTax,
            @RequestParam(value= &quot;useRequireId&quot;, required = false, defaultValue = &quot;false&quot;) String useRequireId,
            @RequestParam(value= &quot;useNewCCS&quot;, required = false, defaultValue = &quot;false&quot;) String useNewCCS,
            @RequestHeader(value = &quot;X-Api-ProductId&quot;, required = false, defaultValue = &quot;&quot;) String productId,
            @RequestHeader(value = &quot;X-API-RequestId&quot;, required = false, defaultValue = &quot;&quot;) String requestId,
            @RequestHeader(value = &quot;X-API-UserId&quot;, required = false, defaultValue = &quot;&quot;) String userId,
            ServerHttpRequest request
    ) {
<span class="nc" id="L96">        long startTime = System.currentTimeMillis();</span>
<span class="nc" id="L97">        List&lt;String&gt; rawList = Arrays.asList(idList.split(&quot;,&quot;));</span>
<span class="nc" id="L98">        MartRequest martRequest = MartRequest.builder()</span>
<span class="nc" id="L99">                .currency(currency)</span>
<span class="nc" id="L100">                .dps(Arrays.asList(dpList.split(&quot;,&quot;)))</span>
<span class="nc" id="L101">                .ids(rawList)</span>
<span class="nc" id="L102">                .startDate(DateParamInputUtil.formatDateParameter(startDate))</span>
<span class="nc" id="L103">                .endDate(DateParamInputUtil.formatDateParameter(endDate))</span>
<span class="nc" id="L104">                .preCurrency(preCurrency)</span>
<span class="nc" id="L105">                .readCache(readCache)</span>
<span class="nc" id="L106">                .productId(productId)</span>
<span class="nc" id="L107">                .requestId(requestId)</span>
<span class="nc" id="L108">                .userId(userId)</span>
<span class="nc" id="L109">                .dueStage(dueStage)</span>
<span class="nc" id="L110">                .dateFormat(dateFormat)</span>
<span class="nc" id="L111">                .decimalFormat(decimalFormat)</span>
<span class="nc" id="L112">                .postTax(postTax)</span>
<span class="nc" id="L113">                .useRequireId(&quot;true&quot;.equalsIgnoreCase(useRequireId))</span>
<span class="nc" id="L114">                .useNewCCS(Boolean.parseBoolean(useNewCCS))</span>
<span class="nc" id="L115">                .build();</span>
<span class="nc" id="L116">        return martGateway.asyncRetrieveSecurities(martRequest).doOnEach(LogHelper.logOnNext(list -&gt; {</span>
<span class="nc" id="L117">                    List&lt;LogEntity&gt; logEntities = logAccess(martRequest, request, startTime, requestId, productId, userId);</span>
<span class="nc" id="L118">                    LogEntry.info(logEntities.toArray(LogEntity[]::new));</span>
<span class="nc" id="L119">                })).doOnEach(LogHelper.logOnError(e -&gt; {</span>
<span class="nc" id="L120">                    List&lt;LogEntity&gt; logEntities = logError(martRequest, request, startTime, requestId, e, productId, userId);</span>
<span class="nc" id="L121">                    LogEntry.error(logEntities.toArray(LogEntity[]::new));</span>
<span class="nc" id="L122">                }));</span>
    }

    @PostMapping(value = &quot;/investment-id/&quot;, consumes = MediaType.APPLICATION_JSON_VALUE)
    public Mono&lt;MartResponse&gt; getDataPointsForAsOfDate(
            @RequestBody MartRequest martRequest,
            ServerHttpRequest request) {

<span class="nc" id="L130">        long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L131" title="All 2 branches missed.">        String reqId = StringUtils.isEmpty(martRequest.getRequestId()) ? UUID.randomUUID().toString() : martRequest.getRequestId();</span>

<span class="nc" id="L133">        return martGateway.asyncRetrieveSecurities(martRequest).doOnEach(LogHelper.logOnNext(list -&gt; {</span>
<span class="nc" id="L134">                List&lt;LogEntity&gt; logEntities = logAccess(martRequest, request, startTime, reqId);</span>
<span class="nc" id="L135">                    LogEntry.info(logEntities.toArray(LogEntity[]::new));</span>
<span class="nc" id="L136">                })).doOnEach(LogHelper.logOnError(e -&gt; {</span>
<span class="nc" id="L137">                    List&lt;LogEntity&gt; logEntities = logError(martRequest, request, startTime, reqId, e);</span>
<span class="nc" id="L138">                    LogEntry.error(logEntities.toArray(LogEntity[]::new));</span>
<span class="nc" id="L139">                }));</span>

    }

    @GetMapping(value = &quot;/category-data&quot;)
    public Mono&lt;MartResponse&gt; getCategoryDatapointValues(
            @ApiParam(example = &quot;57348,57367&quot;)
            @RequestParam(value = &quot;dps&quot;, required = true) String dpList,
            @ApiParam(example = &quot;1&quot;)
            @RequestParam(value = &quot;peer-group-id&quot;, required = false, defaultValue = &quot;&quot;) String peerGroupId,
            @ApiParam(example = &quot;1&quot;)
            @RequestParam(value = &quot;region-id&quot;, required = false, defaultValue = &quot;&quot;) String regionId,
            @ApiParam(example = &quot;$FOCA$LG$$&quot;)
            @RequestParam(value = &quot;category-code&quot;, required = false, defaultValue = &quot;&quot;) String categoryCode,
            @ApiParam(example = &quot;2022-10-31&quot;)
            @RequestParam(value = &quot;start-date&quot;, required = false, defaultValue = &quot;&quot;) String startDate,
            @ApiParam(example = &quot;2022-11-30&quot;)
            @RequestParam(value = &quot;end-date&quot;, required = false, defaultValue = &quot;&quot;) String endDate,
            @ApiParam(example = &quot;FO&quot;)
            @RequestParam(value = &quot;universe&quot;, required = false, defaultValue = &quot;&quot;) String universe,
            @ApiParam(example = &quot;false&quot;)
            @RequestParam(value = &quot;read-cache&quot;, required = false, defaultValue = &quot;&quot;) String readCache,
            @RequestParam(value= &quot;post-tax&quot;, required = false, defaultValue = &quot;&quot;) String postTax,
            @RequestParam(value= &quot;useNewCCS&quot;, required = false, defaultValue = &quot;false&quot;) String useNewCCS,
            @RequestHeader(value = &quot;X-Api-ProductId&quot;, required = false, defaultValue = &quot;&quot;) String productId,
            @RequestHeader(value = &quot;X-API-RequestId&quot;, required = false, defaultValue = &quot;&quot;) String requestId,
            @RequestHeader(value = &quot;X-API-UserId&quot;, required = false, defaultValue = &quot;&quot;) String userId,
            ServerHttpRequest request
    ) {
<span class="nc" id="L168">        long startTime = System.currentTimeMillis();</span>
<span class="nc" id="L169">        MartRequest martRequest = MartRequest.builder()</span>
<span class="nc" id="L170">                .categoryCode(categoryCode)</span>
<span class="nc" id="L171">                .peerGroupId(peerGroupId)</span>
<span class="nc" id="L172">                .regionId(regionId)</span>
<span class="nc" id="L173">                .dps(Arrays.asList(dpList.split(&quot;,&quot;)))</span>
<span class="nc" id="L174">                .startDate(startDate)</span>
<span class="nc" id="L175">                .endDate(endDate)</span>
<span class="nc" id="L176">                .universe(universe)</span>
<span class="nc" id="L177">                .readCache(readCache)</span>
<span class="nc" id="L178">                .postTax(postTax)</span>
<span class="nc" id="L179">                .productId(productId)</span>
<span class="nc" id="L180">                .requestId(requestId)</span>
<span class="nc" id="L181">                .useNewCCS(Boolean.parseBoolean(useNewCCS))</span>
<span class="nc" id="L182">                .build();</span>
<span class="nc" id="L183">        return martGateway.asyncRetrieveSecurities(martRequest).doOnEach(LogHelper.logOnNext(list -&gt; {</span>
<span class="nc" id="L184">               List&lt;LogEntity&gt; logEntities = logAccess(martRequest, request, startTime, requestId, productId, userId);</span>
<span class="nc" id="L185">               LogEntry.info(logEntities.toArray(LogEntity[]::new));</span>
<span class="nc" id="L186">               })).doOnEach(LogHelper.logOnError(e -&gt; {</span>
<span class="nc" id="L187">                    List&lt;LogEntity&gt; logEntities = logError(martRequest, request, startTime, requestId, e, productId, userId);</span>
<span class="nc" id="L188">                    LogEntry.error(logEntities.toArray(LogEntity[]::new));</span>
<span class="nc" id="L189">                }));</span>
    }

    private List&lt;LogEntity&gt; logAccess(
            MartRequest martRequest,
            ServerHttpRequest request,
            long startTime,
            String requestId,
            String productId,
            String userId
    ) {
<span class="nc" id="L200">        List&lt;LogEntity&gt; logEntities = logAccess(martRequest, request, startTime, requestId);</span>
<span class="nc" id="L201">        logEntities.add(new LogEntity(PRODUCT_ID, productId));</span>
<span class="nc" id="L202">        logEntities.add(new LogEntity(USER_ID, userId));</span>
<span class="nc" id="L203">        return logEntities;</span>
    }

    private List&lt;LogEntity&gt; logAccess(
            MartRequest martRequest,
            ServerHttpRequest request,
            long startTime,
            String requestId
    ) {
<span class="nc" id="L212">        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), requestId);</span>
<span class="nc" id="L213">        long executionTime = System.currentTimeMillis() - startTime;</span>
<span class="nc" id="L214">        List&lt;LogEntity&gt; logEntities = Stream.of(</span>
                new LogEntity(EVENT_TYPE, RESPONSE),
                new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
<span class="nc" id="L218">                new LogEntity(REQUEST_PARAM, martRequest.getRequestParam(request.getURI().getPath(), request.getURI().getQuery())),</span>
<span class="nc" id="L219">                new LogEntity(EXECUTE_TIME, executionTime)</span>
<span class="nc" id="L220">        ).collect(Collectors.toCollection(ArrayList::new));</span>
<span class="nc bnc" id="L221" title="All 2 branches missed.">        if (Objects.requireNonNull(request.getMethod()).matches(HttpMethod.POST.name())) {</span>
<span class="nc" id="L222">            logEntities.add(new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(martRequest)));</span>
        }
<span class="nc" id="L224">        return logEntities;</span>
    }

    private List&lt;LogEntity&gt; logError(
            MartRequest martRequest,
            ServerHttpRequest request,
            long startTime,
            String requestId,
            Throwable e
    ) {
<span class="nc" id="L234">        MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), requestId);</span>
<span class="nc" id="L235">        return Stream.of(</span>
                new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                new LogEntity(EVENT_DESCRIPTION, e),
                new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
<span class="nc" id="L239">                new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(martRequest)),</span>
<span class="nc" id="L240">                new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime),</span>
<span class="nc" id="L241">                new LogEntity(EXCEPTION_TYPE, e.getClass()),</span>
<span class="nc" id="L242">                new LogEntity(REQUEST_PARAM, martRequest.getRequestParam(request.getURI().getPath(), request.getURI().getQuery()))</span>
<span class="nc" id="L243">        ).collect(Collectors.toCollection(ArrayList::new));</span>
    }

    private List&lt;LogEntity&gt; logError(
            MartRequest martRequest,
            ServerHttpRequest request,
            long startTime,
            String requestId,
            Throwable e,
            String productId,
            String userId
    ) {
<span class="nc" id="L255">        List&lt;LogEntity&gt; logEntities = logError(martRequest, request, startTime, requestId, e);</span>
<span class="nc" id="L256">        logEntities.add(new LogEntity(PRODUCT_ID, productId));</span>
<span class="nc" id="L257">        logEntities.add(new LogEntity(USER_ID, userId));</span>
<span class="nc" id="L258">        return logEntities;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>