package com.morningstar.martapi.grpc;

import com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Disabled;
import org.springframework.boot.test.context.SpringBootTest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

/**
 * gRPC客户端测试类
 * 演示如何调用TimeSeriesService
 */
@Disabled("Integration test - requires running server")
public class TimeSeriesGrpcClientTest {

    private static final Logger log = LoggerFactory.getLogger(TimeSeriesGrpcClientTest.class);
    
    private ManagedChannel channel;
    private TimeSeriesServiceGrpc.TimeSeriesServiceBlockingStub blockingStub;

    @BeforeEach
    void setUp() {
        // 创建gRPC通道
        channel = ManagedChannelBuilder.forAddress("localhost", 9090)
                .usePlaintext()
                .build();
        
        // 创建阻塞式客户端存根
        blockingStub = TimeSeriesServiceGrpc.newBlockingStub(channel);
    }

    @AfterEach
    void tearDown() throws InterruptedException {
        // 关闭通道
        channel.shutdown().awaitTermination(5, TimeUnit.SECONDS);
    }

    @Test
    void testGetTimeSeriesData() {
        // 构建请求
        TimeSeriesRequest request = TimeSeriesRequest.newBuilder()
                .addInvestmentIds("0P00000AWG")
                .addInvestmentIds("0P00000AYI")
                .addDataPoints("119114")
                .addDataPoints("119115")
                .setStartDate("2022-10-31")
                .setEndDate("2022-11-30")
                .setCurrency("EUR")
                .setPreCurrency("USD")
                .setReadCache("false")
                .setDateFormat("")
                .setDecimalFormat("")
                .setExtendPerformance("")
                .setPostTax("")
                .setUseRequireId(true)
                .setUseCase("feed")
                .setUseNewCcs(false)
                .setProductId("MDS")
                .setRequestId("test-grpc-request-001")
                .setTraceId("test-trace-001")
                .setUserId("test-user")
                .setAuthorization("Bearer test-token")
                .setCheckEntitlement(true)
                .setEntitlementProductId("MDS")
                .build();

        try {
            // 调用gRPC服务
            log.info("Sending gRPC request for investment IDs: {}", request.getInvestmentIdsList());
            
            TsCacheDataForProtoBuf.TimeSeriesDatas response = blockingStub.getTimeSeriesData(request);
            
            // 验证响应
            log.info("Received response with {} time series data entries", response.getValuesCount());
            log.info("Response retcode: {}", response.getRetcode());
            log.info("Response message: {}", response.getMsg());
            
            // 打印每个时间序列数据
            for (TsCacheDataForProtoBuf.TimeSeriesData data : response.getValuesList()) {
                log.info("Security ID: {}, Universe: {}, Values count: {}", 
                        data.getSecId(), data.getUniverse(), data.getValuesCount());
                
                if (data.hasErrorCode()) {
                    log.warn("Error code for {}: {}", data.getSecId(), data.getErrorCode());
                }
            }
            
        } catch (Exception e) {
            log.error("gRPC call failed", e);
            throw e;
        }
    }

    @Test
    void testGetTimeSeriesDataWithMinimalRequest() {
        // 构建最小请求
        TimeSeriesRequest request = TimeSeriesRequest.newBuilder()
                .addInvestmentIds("0P00000AWG")
                .addDataPoints("119114")
                .setProductId("MDS")
                .setUseCase("feed")
                .build();

        try {
            log.info("Sending minimal gRPC request");
            
            TsCacheDataForProtoBuf.TimeSeriesDatas response = blockingStub.getTimeSeriesData(request);
            
            log.info("Minimal request response - Values count: {}", response.getValuesCount());
            
        } catch (Exception e) {
            log.error("Minimal gRPC call failed", e);
            throw e;
        }
    }
}
