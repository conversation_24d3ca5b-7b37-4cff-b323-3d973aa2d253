@echo off
echo Starting mart-api with gRPC support...
echo.

echo Step 1: Compiling protobuf files and Java sources...
call mvn clean compile
if %ERRORLEVEL% neq 0 (
    echo ERROR: Compilation failed!
    pause
    exit /b 1
)

echo.
echo Step 2: Starting Spring Boot application...
echo REST API will be available at: http://localhost:8080
echo gRPC API will be available at: localhost:9090
echo.
echo Press Ctrl+C to stop the service
echo.

call mvn spring-boot:run
