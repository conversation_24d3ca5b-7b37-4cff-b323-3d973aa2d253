package com.morningstar.martapi.service;

import com.auth0.jwk.Jwk;
import com.auth0.jwk.JwkException;
import com.auth0.jwk.JwkProvider;
import com.auth0.jwk.JwkProviderBuilder;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.auth0.jwt.interfaces.JWTVerifier;
import com.auth0.jwt.interfaces.RSAKeyProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.security.interfaces.RSAPublicKey;
import java.util.concurrent.TimeUnit;

@Service
public class JwtVerificationService {

    @Value("${jwt.jwks.url}")
    private String jwksUrl;

    private JwkProvider jwkProvider;

    @PostConstruct
    public void init() {

        jwkProvider = new JwkProviderBuilder(jwksUrl).cached(10, 24,
                TimeUnit.HOURS).build();
    }

    public boolean verifyJwtToken(DecodedJWT jwt){
        try
        {
            Jwk jwk = jwkProvider.get(jwt.getKeyId());
            Algorithm algorithm = Algorithm.RSA256((RSAPublicKey) jwk.getPublicKey(), null);
            JWTVerifier verifier = JWT.require(algorithm).build();
            verifier.verify(jwt);
            return true;
        } catch (Exception e) {
            return false;
        }


    }
}
