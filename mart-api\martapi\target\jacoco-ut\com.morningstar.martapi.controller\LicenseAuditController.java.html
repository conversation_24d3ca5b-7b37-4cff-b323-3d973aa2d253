<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>LicenseAuditController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <a href="index.source.html" class="el_package">com.morningstar.martapi.controller</a> &gt; <span class="el_source">LicenseAuditController.java</span></div><h1>LicenseAuditController.java</h1><pre class="source lang-java linenums">package com.morningstar.martapi.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.morningstar.martapi.entity.LicenseAuditEntity;
import com.morningstar.martapi.entity.LicenseAuditResponse;
import com.morningstar.martapi.entity.LicenseCellEntity;
import com.morningstar.martapi.service.LicenseAuditService;
import com.morningstar.martapi.util.LoggerUtil;
import com.morningstar.martapi.util.TokenUtil;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.entitlement.entity.LicenseCellResponse;
import com.mysql.cj.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.inject.Inject;
import java.util.UUID;

@RestController
@RequestMapping(value = {&quot;/v1&quot;},
        produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE})
public class LicenseAuditController {
    private LicenseAuditService licenseAuditService;
    private final RequestValidationHandler&lt;HeadersAndParams,LicenseAuditEntity&gt; licenseAuditValidator;
    private final RequestValidationHandler&lt;HeadersAndParams,LicenseCellEntity&gt; licenseCellValidator;

<span class="nc" id="L38">    private static final Logger log = LoggerFactory.getLogger(LicenseAuditController.class);</span>

    @Inject
<span class="nc" id="L41">    public LicenseAuditController(LicenseAuditService licenseAuditService, @Qualifier(&quot;licenseAuditValidator&quot;)RequestValidationHandler&lt;HeadersAndParams,LicenseAuditEntity&gt; licenseAuditValidator, @Qualifier(&quot;licenseCellValidator&quot;)RequestValidationHandler&lt;HeadersAndParams,LicenseCellEntity&gt; licenseCellValidator) {</span>
<span class="nc" id="L42">        this.licenseAuditService = licenseAuditService;</span>
<span class="nc" id="L43">        this.licenseAuditValidator = licenseAuditValidator;</span>
<span class="nc" id="L44">        this.licenseCellValidator = licenseCellValidator;</span>
<span class="nc" id="L45">    }</span>

    /***
     *
     * @param auditRequest
     * @param token
     * @param requestId
     * @return
     * @throws JsonProcessingException
     */
    @PostMapping(value = &quot;license-audit&quot;, consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity&lt;?&gt; getAudit(
            @RequestBody LicenseAuditEntity auditRequest,
            @RequestHeader(value = &quot;Authorization&quot;, required = false, defaultValue = &quot;&quot;) String token,
            @RequestHeader(value = &quot;X-API-ProductId&quot;, required = false, defaultValue = &quot;&quot;) String productId,
            @RequestHeader(value = &quot;X-API-RequestId&quot;, required = false, defaultValue = &quot;&quot;) String requestId) {
<span class="nc" id="L61">        LoggerUtil loggerUtil = new LoggerUtil();</span>
<span class="nc" id="L62">        long startTime = System.currentTimeMillis();</span>
<span class="nc" id="L63">        String userId = TokenUtil.getUserId(token);</span>
<span class="nc" id="L64">        String configId = TokenUtil.getConfigId(token);</span>
<span class="nc bnc" id="L65" title="All 2 branches missed.">        String request = StringUtils.isEmptyOrWhitespaceOnly(requestId) ? UUID.randomUUID().toString() : requestId;</span>
        try {
<span class="nc" id="L67">           validateLicenseAuditRequest(auditRequest, token, productId);</span>
<span class="nc" id="L68">            MDC.put(&quot;request_id&quot;, request);</span>
<span class="nc" id="L69">            log.info(&quot;event_type=\&quot;License Audit API\&quot;, event_description=\&quot;Submit Audit request\&quot;, user_id=\&quot;{}\&quot;, request_id=\&quot;{}\&quot;&quot;, userId, request);</span>
<span class="nc" id="L70">            LicenseAuditResponse response = licenseAuditService.processAudit(userId, productId, configId, auditRequest, false);</span>
<span class="nc" id="L71">            loggerUtil.logAccess(auditRequest, &quot;&quot;, request, userId, startTime, false);</span>
<span class="nc" id="L72">            return new ResponseEntity&lt;&gt;(response, HttpStatus.OK);</span>
<span class="nc" id="L73">        } catch(Exception e) {</span>
<span class="nc" id="L74">            loggerUtil.logError(auditRequest, &quot;&quot;, request, userId, startTime, false, e);</span>
<span class="nc" id="L75">            throw e;</span>
        }
        finally {
<span class="nc" id="L78">            MDC.clear();</span>
        }
    }

    /***
     *
     * @param auditRequest
     * @param token
     * @param requestId
     * @return
     * @throws JsonProcessingException
     */
    @PostMapping(value = &quot;license-audit-summary&quot;, consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity&lt;?&gt; getAuditSummary(
            @RequestBody LicenseAuditEntity auditRequest,
            @RequestHeader(value = &quot;Authorization&quot;, required = false, defaultValue = &quot;&quot;) String token,
            @RequestHeader(value = &quot;X-API-ProductId&quot;, required = false, defaultValue = &quot;&quot;) String productId,
            @RequestHeader(value = &quot;X-API-RequestId&quot;, required = false, defaultValue = &quot;&quot;) String requestId) {
<span class="nc" id="L96">        LoggerUtil loggerUtil = new LoggerUtil();</span>
<span class="nc" id="L97">        long startTime = System.currentTimeMillis();</span>
<span class="nc" id="L98">        String userId = TokenUtil.getUserId(token);</span>
<span class="nc" id="L99">        String configId = TokenUtil.getConfigId(token);</span>
<span class="nc bnc" id="L100" title="All 2 branches missed.">        String request = StringUtils.isEmptyOrWhitespaceOnly(requestId) ? UUID.randomUUID().toString() : requestId;</span>
        try {
<span class="nc" id="L102">            validateLicenseAuditRequest(auditRequest, token, productId);</span>
<span class="nc" id="L103">            MDC.put(&quot;request_id&quot;, request);</span>
<span class="nc" id="L104">            log.info(&quot;event_type=\&quot;License Audit API\&quot;, event_description=\&quot;Submit Summary request\&quot;, user_id=\&quot;{}\&quot;, request_id=\&quot;{}\&quot;&quot;, userId, request);</span>
<span class="nc" id="L105">            LicenseAuditResponse response = licenseAuditService.processAudit(userId, productId, configId, auditRequest, true);</span>
<span class="nc" id="L106">            loggerUtil.logAccess(auditRequest, &quot;&quot;, request, userId, startTime, true);</span>
<span class="nc" id="L107">            return new ResponseEntity&lt;&gt;(response, HttpStatus.OK);</span>
<span class="nc" id="L108">        } catch(Exception e) {</span>
<span class="nc" id="L109">            loggerUtil.logError(auditRequest, &quot;&quot;, request, userId, startTime, true, e);</span>
<span class="nc" id="L110">            throw e;</span>
        }
        finally {
<span class="nc" id="L113">            MDC.clear();</span>
        }
    }

    @PostMapping(value = &quot;license-audit-details&quot;, consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity&lt;?&gt; getCellInspection(
            @RequestBody LicenseCellEntity cellRequest,
            @RequestHeader(value = &quot;Authorization&quot;, required = false, defaultValue = &quot;&quot;) String token,
            @RequestHeader(value = &quot;X-API-ProductId&quot;, required = false, defaultValue = &quot;&quot;) String productId,
            @RequestHeader(value = &quot;X-API-RequestId&quot;, required = false, defaultValue = &quot;&quot;) String requestId) {
<span class="nc" id="L123">        LoggerUtil loggerUtil = new LoggerUtil();</span>
<span class="nc" id="L124">        long startTime = System.currentTimeMillis();</span>
<span class="nc" id="L125">        String userId = TokenUtil.getUserId(token);</span>
<span class="nc" id="L126">        String configId = TokenUtil.getConfigId(token);</span>
<span class="nc bnc" id="L127" title="All 2 branches missed.">        String request = StringUtils.isEmptyOrWhitespaceOnly(requestId) ? UUID.randomUUID().toString() : requestId;</span>
        try {
<span class="nc" id="L129">            validateLicenseCellRequest(cellRequest, token, productId, requestId);</span>
<span class="nc" id="L130">            MDC.put(&quot;request_id&quot;, request);</span>
<span class="nc" id="L131">            log.info(&quot;event_type=\&quot;License Cell Inspection API\&quot;, event_description=\&quot;Submit License cell inspection request\&quot;, user_id=\&quot;{}\&quot;, request_id=\&quot;{}\&quot;&quot;, userId, request);</span>
<span class="nc" id="L132">            LicenseCellResponse response = licenseAuditService.processCellInspection(userId, configId, productId, cellRequest);</span>
<span class="nc" id="L133">            loggerUtil.logAccess(cellRequest, productId, request, userId, startTime, true);</span>
<span class="nc" id="L134">            return new ResponseEntity&lt;&gt;(response, HttpStatus.OK);</span>
<span class="nc" id="L135">        } catch(Exception e) {</span>
<span class="nc" id="L136">            loggerUtil.logError(cellRequest, productId, request, userId, startTime, e);</span>
<span class="nc" id="L137">            throw e;</span>
        }
        finally {
<span class="nc" id="L140">            MDC.clear();</span>
        }
    }
    private void validateLicenseAuditRequest(LicenseAuditEntity licenseAuditEntity, String token, String productId) {
<span class="nc" id="L144">        HeadersAndParams headersAndParams = HeadersAndParams.builder().authorizationToken(token)</span>
<span class="nc" id="L145">                .productId(productId)</span>
<span class="nc" id="L146">                .build();</span>
<span class="nc" id="L147">        licenseAuditValidator.validateHeadersAndParams(headersAndParams);</span>
<span class="nc" id="L148">        licenseAuditValidator.validateRequestBody(licenseAuditEntity);</span>
<span class="nc" id="L149">    }</span>
    private void validateLicenseCellRequest(LicenseCellEntity licenseCellEntity, String token, String productId, String requestId) {
<span class="nc" id="L151">        HeadersAndParams headersAndParams = HeadersAndParams.builder().authorizationToken(token)</span>
<span class="nc" id="L152">                .productId(productId)</span>
<span class="nc" id="L153">                .requestId(requestId)</span>
<span class="nc" id="L154">                .build();</span>
<span class="nc" id="L155">        licenseCellValidator.validateHeadersAndParams(headersAndParams);</span>
<span class="nc" id="L156">        licenseCellValidator.validateRequestBody(licenseCellEntity);</span>
<span class="nc" id="L157">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>