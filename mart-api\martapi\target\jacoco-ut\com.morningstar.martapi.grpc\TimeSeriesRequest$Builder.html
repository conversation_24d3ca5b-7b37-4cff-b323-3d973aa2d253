<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TimeSeriesRequest.Builder</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <a href="index.html" class="el_package">com.morningstar.martapi.grpc</a> &gt; <span class="el_class">TimeSeriesRequest.Builder</span></div><h1>TimeSeriesRequest.Builder</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">2,913 of 2,913</td><td class="ctr2">0%</td><td class="bar">269 of 269</td><td class="ctr2">0%</td><td class="ctr1">276</td><td class="ctr2">276</td><td class="ctr1">857</td><td class="ctr2">857</td><td class="ctr1">131</td><td class="ctr2">131</td></tr></tfoot><tbody><tr><td id="a87"><a href="TimeSeriesRequest.java.html#L1453" class="el_method">mergeFrom(TimeSeriesRequest)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="352" alt="352"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="48" alt="48"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f1">25</td><td class="ctr2" id="g1">25</td><td class="ctr1" id="h0">88</td><td class="ctr2" id="i0">88</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a85"><a href="TimeSeriesRequest.java.html#L1578" class="el_method">mergeFrom(CodedInputStream, ExtensionRegistryLite)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="90" height="10" title="266" alt="266"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="72" height="10" title="29" alt="29"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">26</td><td class="ctr2" id="g0">26</td><td class="ctr1" id="h1">80</td><td class="ctr2" id="i1">80</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a9"><a href="TimeSeriesRequest.java.html#L1342" class="el_method">buildPartial0(TimeSeriesRequest)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="178" alt="178"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="105" height="10" title="42" alt="42"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">22</td><td class="ctr2" id="g2">22</td><td class="ctr1" id="h2">46</td><td class="ctr2" id="i2">46</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a10"><a href="TimeSeriesRequest.java.html#L1285" class="el_method">clear()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="71" alt="71"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d78"/><td class="ctr2" id="e78">n/a</td><td class="ctr1" id="f78">1</td><td class="ctr2" id="g78">1</td><td class="ctr1" id="h3">26</td><td class="ctr2" id="i3">26</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a130"><a href="TimeSeriesRequest.java.html#L1280" class="el_method">TimeSeriesRequest.Builder(GeneratedMessageV3.BuilderParent)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="58" alt="58"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d79"/><td class="ctr2" id="e79">n/a</td><td class="ctr1" id="f79">1</td><td class="ctr2" id="g79">1</td><td class="ctr1" id="h4">22</td><td class="ctr2" id="i4">22</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a129"><a href="TimeSeriesRequest.java.html#L1274" class="el_method">TimeSeriesRequest.Builder()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="57" alt="57"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d80"/><td class="ctr2" id="e80">n/a</td><td class="ctr1" id="f80">1</td><td class="ctr2" id="g80">1</td><td class="ctr1" id="h5">22</td><td class="ctr2" id="i5">22</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a106"><a href="TimeSeriesRequest.java.html#L1782" class="el_method">setInvestmentIds(int, String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="24" alt="24"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">2</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h40">6</td><td class="ctr2" id="i40">6</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a5"><a href="TimeSeriesRequest.java.html#L1851" class="el_method">addInvestmentIdsBytes(ByteString)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="24" alt="24"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h6">7</td><td class="ctr2" id="i6">7</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a94"><a href="TimeSeriesRequest.java.html#L1929" class="el_method">setDataPoints(int, String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="24" alt="24"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h41">6</td><td class="ctr2" id="i41">6</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a3"><a href="TimeSeriesRequest.java.html#L1998" class="el_method">addDataPointsBytes(ByteString)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="24" alt="24"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h7">7</td><td class="ctr2" id="i7">7</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a4"><a href="TimeSeriesRequest.java.html#L1800" class="el_method">addInvestmentIds(String)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="23" alt="23"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f7">2</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h42">6</td><td class="ctr2" id="i42">6</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a2"><a href="TimeSeriesRequest.java.html#L1947" class="el_method">addDataPoints(String)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="23" alt="23"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h43">6</td><td class="ctr2" id="i43">6</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a119"><a href="TimeSeriesRequest.java.html#L2091" class="el_method">setStartDateBytes(ByteString)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="21" alt="21"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f9">2</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h44">6</td><td class="ctr2" id="i44">6</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a100"><a href="TimeSeriesRequest.java.html#L2163" class="el_method">setEndDateBytes(ByteString)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="21" alt="21"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f10">2</td><td class="ctr2" id="g10">2</td><td class="ctr1" id="h45">6</td><td class="ctr2" id="i45">6</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a93"><a href="TimeSeriesRequest.java.html#L2255" class="el_method">setCurrencyBytes(ByteString)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="21" alt="21"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f11">2</td><td class="ctr2" id="g11">2</td><td class="ctr1" id="h46">6</td><td class="ctr2" id="i46">6</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a110"><a href="TimeSeriesRequest.java.html#L2327" class="el_method">setPreCurrencyBytes(ByteString)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="21" alt="21"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f12">2</td><td class="ctr2" id="g12">2</td><td class="ctr1" id="h47">6</td><td class="ctr2" id="i47">6</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a114"><a href="TimeSeriesRequest.java.html#L2419" class="el_method">setReadCacheBytes(ByteString)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="21" alt="21"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d13"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e13">0%</td><td class="ctr1" id="f13">2</td><td class="ctr2" id="g13">2</td><td class="ctr1" id="h48">6</td><td class="ctr2" id="i48">6</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a96"><a href="TimeSeriesRequest.java.html#L2491" class="el_method">setDateFormatBytes(ByteString)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="21" alt="21"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d14"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e14">0%</td><td class="ctr1" id="f14">2</td><td class="ctr2" id="g14">2</td><td class="ctr1" id="h49">6</td><td class="ctr2" id="i49">6</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a98"><a href="TimeSeriesRequest.java.html#L2563" class="el_method">setDecimalFormatBytes(ByteString)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="21" alt="21"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d15"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e15">0%</td><td class="ctr1" id="f15">2</td><td class="ctr2" id="g15">2</td><td class="ctr1" id="h50">6</td><td class="ctr2" id="i50">6</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a104"><a href="TimeSeriesRequest.java.html#L2655" class="el_method">setExtendPerformanceBytes(ByteString)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="21" alt="21"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d16"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e16">0%</td><td class="ctr1" id="f16">2</td><td class="ctr2" id="g16">2</td><td class="ctr1" id="h51">6</td><td class="ctr2" id="i51">6</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a108"><a href="TimeSeriesRequest.java.html#L2727" class="el_method">setPostTaxBytes(ByteString)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="21" alt="21"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d17"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e17">0%</td><td class="ctr1" id="f17">2</td><td class="ctr2" id="g17">2</td><td class="ctr1" id="h52">6</td><td class="ctr2" id="i52">6</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a124"><a href="TimeSeriesRequest.java.html#L2843" class="el_method">setUseCaseBytes(ByteString)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="21" alt="21"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d18"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e18">0%</td><td class="ctr1" id="f18">2</td><td class="ctr2" id="g18">2</td><td class="ctr1" id="h53">6</td><td class="ctr2" id="i53">6</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a112"><a href="TimeSeriesRequest.java.html#L2967" class="el_method">setProductIdBytes(ByteString)</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="21" alt="21"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d19"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e19">0%</td><td class="ctr1" id="f19">2</td><td class="ctr2" id="g19">2</td><td class="ctr1" id="h54">6</td><td class="ctr2" id="i54">6</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a117"><a href="TimeSeriesRequest.java.html#L3039" class="el_method">setRequestIdBytes(ByteString)</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="21" alt="21"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d20"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e20">0%</td><td class="ctr1" id="f20">2</td><td class="ctr2" id="g20">2</td><td class="ctr1" id="h55">6</td><td class="ctr2" id="i55">6</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a121"><a href="TimeSeriesRequest.java.html#L3111" class="el_method">setTraceIdBytes(ByteString)</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="21" alt="21"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d21"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e21">0%</td><td class="ctr1" id="f21">2</td><td class="ctr2" id="g21">2</td><td class="ctr1" id="h56">6</td><td class="ctr2" id="i56">6</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a128"><a href="TimeSeriesRequest.java.html#L3183" class="el_method">setUserIdBytes(ByteString)</a></td><td class="bar" id="b25"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="21" alt="21"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d22"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e22">0%</td><td class="ctr1" id="f22">2</td><td class="ctr2" id="g22">2</td><td class="ctr1" id="h57">6</td><td class="ctr2" id="i57">6</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a90"><a href="TimeSeriesRequest.java.html#L3255" class="el_method">setAuthorizationBytes(ByteString)</a></td><td class="bar" id="b26"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="21" alt="21"/></td><td class="ctr2" id="c26">0%</td><td class="bar" id="d23"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e23">0%</td><td class="ctr1" id="f23">2</td><td class="ctr2" id="g23">2</td><td class="ctr1" id="h58">6</td><td class="ctr2" id="i58">6</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a102"><a href="TimeSeriesRequest.java.html#L3359" class="el_method">setEntitlementProductIdBytes(ByteString)</a></td><td class="bar" id="b27"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="21" alt="21"/></td><td class="ctr2" id="c27">0%</td><td class="bar" id="d24"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e24">0%</td><td class="ctr1" id="f24">2</td><td class="ctr2" id="g24">2</td><td class="ctr1" id="h59">6</td><td class="ctr2" id="i59">6</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a73"><a href="TimeSeriesRequest.java.html#L2017" class="el_method">getStartDate()</a></td><td class="bar" id="b28"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="20" alt="20"/></td><td class="ctr2" id="c28">0%</td><td class="bar" id="d25"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e25">0%</td><td class="ctr1" id="f25">2</td><td class="ctr2" id="g25">2</td><td class="ctr1" id="h8">7</td><td class="ctr2" id="i8">7</td><td class="ctr1" id="j28">1</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a53"><a href="TimeSeriesRequest.java.html#L2105" class="el_method">getEndDate()</a></td><td class="bar" id="b29"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="20" alt="20"/></td><td class="ctr2" id="c29">0%</td><td class="bar" id="d26"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e26">0%</td><td class="ctr1" id="f26">2</td><td class="ctr2" id="g26">2</td><td class="ctr1" id="h9">7</td><td class="ctr2" id="i9">7</td><td class="ctr1" id="j29">1</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a40"><a href="TimeSeriesRequest.java.html#L2181" class="el_method">getCurrency()</a></td><td class="bar" id="b30"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="20" alt="20"/></td><td class="ctr2" id="c30">0%</td><td class="bar" id="d27"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e27">0%</td><td class="ctr1" id="f27">2</td><td class="ctr2" id="g27">2</td><td class="ctr1" id="h10">7</td><td class="ctr2" id="i10">7</td><td class="ctr1" id="j30">1</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a65"><a href="TimeSeriesRequest.java.html#L2269" class="el_method">getPreCurrency()</a></td><td class="bar" id="b31"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="20" alt="20"/></td><td class="ctr2" id="c31">0%</td><td class="bar" id="d28"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e28">0%</td><td class="ctr1" id="f28">2</td><td class="ctr2" id="g28">2</td><td class="ctr1" id="h11">7</td><td class="ctr2" id="i11">7</td><td class="ctr1" id="j31">1</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a69"><a href="TimeSeriesRequest.java.html#L2345" class="el_method">getReadCache()</a></td><td class="bar" id="b32"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="20" alt="20"/></td><td class="ctr2" id="c32">0%</td><td class="bar" id="d29"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e29">0%</td><td class="ctr1" id="f29">2</td><td class="ctr2" id="g29">2</td><td class="ctr1" id="h12">7</td><td class="ctr2" id="i12">7</td><td class="ctr1" id="j32">1</td><td class="ctr2" id="k32">1</td></tr><tr><td id="a46"><a href="TimeSeriesRequest.java.html#L2433" class="el_method">getDateFormat()</a></td><td class="bar" id="b33"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="20" alt="20"/></td><td class="ctr2" id="c33">0%</td><td class="bar" id="d30"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e30">0%</td><td class="ctr1" id="f30">2</td><td class="ctr2" id="g30">2</td><td class="ctr1" id="h13">7</td><td class="ctr2" id="i13">7</td><td class="ctr1" id="j33">1</td><td class="ctr2" id="k33">1</td></tr><tr><td id="a48"><a href="TimeSeriesRequest.java.html#L2505" class="el_method">getDecimalFormat()</a></td><td class="bar" id="b34"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="20" alt="20"/></td><td class="ctr2" id="c34">0%</td><td class="bar" id="d31"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e31">0%</td><td class="ctr1" id="f31">2</td><td class="ctr2" id="g31">2</td><td class="ctr1" id="h14">7</td><td class="ctr2" id="i14">7</td><td class="ctr1" id="j34">1</td><td class="ctr2" id="k34">1</td></tr><tr><td id="a57"><a href="TimeSeriesRequest.java.html#L2581" class="el_method">getExtendPerformance()</a></td><td class="bar" id="b35"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="20" alt="20"/></td><td class="ctr2" id="c35">0%</td><td class="bar" id="d32"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e32">0%</td><td class="ctr1" id="f32">2</td><td class="ctr2" id="g32">2</td><td class="ctr1" id="h15">7</td><td class="ctr2" id="i15">7</td><td class="ctr1" id="j35">1</td><td class="ctr2" id="k35">1</td></tr><tr><td id="a63"><a href="TimeSeriesRequest.java.html#L2669" class="el_method">getPostTax()</a></td><td class="bar" id="b36"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="20" alt="20"/></td><td class="ctr2" id="c36">0%</td><td class="bar" id="d33"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e33">0%</td><td class="ctr1" id="f33">2</td><td class="ctr2" id="g33">2</td><td class="ctr1" id="h16">7</td><td class="ctr2" id="i16">7</td><td class="ctr1" id="j36">1</td><td class="ctr2" id="k36">1</td></tr><tr><td id="a77"><a href="TimeSeriesRequest.java.html#L2785" class="el_method">getUseCase()</a></td><td class="bar" id="b37"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="20" alt="20"/></td><td class="ctr2" id="c37">0%</td><td class="bar" id="d34"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e34">0%</td><td class="ctr1" id="f34">2</td><td class="ctr2" id="g34">2</td><td class="ctr1" id="h17">7</td><td class="ctr2" id="i17">7</td><td class="ctr1" id="j37">1</td><td class="ctr2" id="k37">1</td></tr><tr><td id="a67"><a href="TimeSeriesRequest.java.html#L2893" class="el_method">getProductId()</a></td><td class="bar" id="b38"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="20" alt="20"/></td><td class="ctr2" id="c38">0%</td><td class="bar" id="d35"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e35">0%</td><td class="ctr1" id="f35">2</td><td class="ctr2" id="g35">2</td><td class="ctr1" id="h18">7</td><td class="ctr2" id="i18">7</td><td class="ctr1" id="j38">1</td><td class="ctr2" id="k38">1</td></tr><tr><td id="a71"><a href="TimeSeriesRequest.java.html#L2981" class="el_method">getRequestId()</a></td><td class="bar" id="b39"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="20" alt="20"/></td><td class="ctr2" id="c39">0%</td><td class="bar" id="d36"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e36">0%</td><td class="ctr1" id="f36">2</td><td class="ctr2" id="g36">2</td><td class="ctr1" id="h19">7</td><td class="ctr2" id="i19">7</td><td class="ctr1" id="j39">1</td><td class="ctr2" id="k39">1</td></tr><tr><td id="a75"><a href="TimeSeriesRequest.java.html#L3053" class="el_method">getTraceId()</a></td><td class="bar" id="b40"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="20" alt="20"/></td><td class="ctr2" id="c40">0%</td><td class="bar" id="d37"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e37">0%</td><td class="ctr1" id="f37">2</td><td class="ctr2" id="g37">2</td><td class="ctr1" id="h20">7</td><td class="ctr2" id="i20">7</td><td class="ctr1" id="j40">1</td><td class="ctr2" id="k40">1</td></tr><tr><td id="a81"><a href="TimeSeriesRequest.java.html#L3125" class="el_method">getUserId()</a></td><td class="bar" id="b41"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="20" alt="20"/></td><td class="ctr2" id="c41">0%</td><td class="bar" id="d38"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e38">0%</td><td class="ctr1" id="f38">2</td><td class="ctr2" id="g38">2</td><td class="ctr1" id="h21">7</td><td class="ctr2" id="i21">7</td><td class="ctr1" id="j41">1</td><td class="ctr2" id="k41">1</td></tr><tr><td id="a37"><a href="TimeSeriesRequest.java.html#L3197" class="el_method">getAuthorization()</a></td><td class="bar" id="b42"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="20" alt="20"/></td><td class="ctr2" id="c42">0%</td><td class="bar" id="d39"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e39">0%</td><td class="ctr1" id="f39">2</td><td class="ctr2" id="g39">2</td><td class="ctr1" id="h22">7</td><td class="ctr2" id="i22">7</td><td class="ctr1" id="j42">1</td><td class="ctr2" id="k42">1</td></tr><tr><td id="a55"><a href="TimeSeriesRequest.java.html#L3301" class="el_method">getEntitlementProductId()</a></td><td class="bar" id="b43"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="20" alt="20"/></td><td class="ctr2" id="c43">0%</td><td class="bar" id="d40"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e40">0%</td><td class="ctr1" id="f40">2</td><td class="ctr2" id="g40">2</td><td class="ctr1" id="h23">7</td><td class="ctr2" id="i23">7</td><td class="ctr1" id="j43">1</td><td class="ctr2" id="k43">1</td></tr><tr><td id="a118"><a href="TimeSeriesRequest.java.html#L2060" class="el_method">setStartDate(String)</a></td><td class="bar" id="b44"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="19" alt="19"/></td><td class="ctr2" id="c44">0%</td><td class="bar" id="d41"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e41">0%</td><td class="ctr1" id="f41">2</td><td class="ctr2" id="g41">2</td><td class="ctr1" id="h60">5</td><td class="ctr2" id="i60">5</td><td class="ctr1" id="j44">1</td><td class="ctr2" id="k44">1</td></tr><tr><td id="a99"><a href="TimeSeriesRequest.java.html#L2140" class="el_method">setEndDate(String)</a></td><td class="bar" id="b45"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="19" alt="19"/></td><td class="ctr2" id="c45">0%</td><td class="bar" id="d42"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e42">0%</td><td class="ctr1" id="f42">2</td><td class="ctr2" id="g42">2</td><td class="ctr1" id="h61">5</td><td class="ctr2" id="i61">5</td><td class="ctr1" id="j45">1</td><td class="ctr2" id="k45">1</td></tr><tr><td id="a92"><a href="TimeSeriesRequest.java.html#L2224" class="el_method">setCurrency(String)</a></td><td class="bar" id="b46"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="19" alt="19"/></td><td class="ctr2" id="c46">0%</td><td class="bar" id="d43"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e43">0%</td><td class="ctr1" id="f43">2</td><td class="ctr2" id="g43">2</td><td class="ctr1" id="h62">5</td><td class="ctr2" id="i62">5</td><td class="ctr1" id="j46">1</td><td class="ctr2" id="k46">1</td></tr><tr><td id="a109"><a href="TimeSeriesRequest.java.html#L2304" class="el_method">setPreCurrency(String)</a></td><td class="bar" id="b47"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="19" alt="19"/></td><td class="ctr2" id="c47">0%</td><td class="bar" id="d44"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e44">0%</td><td class="ctr1" id="f44">2</td><td class="ctr2" id="g44">2</td><td class="ctr1" id="h63">5</td><td class="ctr2" id="i63">5</td><td class="ctr1" id="j47">1</td><td class="ctr2" id="k47">1</td></tr><tr><td id="a113"><a href="TimeSeriesRequest.java.html#L2388" class="el_method">setReadCache(String)</a></td><td class="bar" id="b48"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="19" alt="19"/></td><td class="ctr2" id="c48">0%</td><td class="bar" id="d45"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e45">0%</td><td class="ctr1" id="f45">2</td><td class="ctr2" id="g45">2</td><td class="ctr1" id="h64">5</td><td class="ctr2" id="i64">5</td><td class="ctr1" id="j48">1</td><td class="ctr2" id="k48">1</td></tr><tr><td id="a95"><a href="TimeSeriesRequest.java.html#L2468" class="el_method">setDateFormat(String)</a></td><td class="bar" id="b49"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="19" alt="19"/></td><td class="ctr2" id="c49">0%</td><td class="bar" id="d46"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e46">0%</td><td class="ctr1" id="f46">2</td><td class="ctr2" id="g46">2</td><td class="ctr1" id="h65">5</td><td class="ctr2" id="i65">5</td><td class="ctr1" id="j49">1</td><td class="ctr2" id="k49">1</td></tr><tr><td id="a97"><a href="TimeSeriesRequest.java.html#L2540" class="el_method">setDecimalFormat(String)</a></td><td class="bar" id="b50"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="19" alt="19"/></td><td class="ctr2" id="c50">0%</td><td class="bar" id="d47"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e47">0%</td><td class="ctr1" id="f47">2</td><td class="ctr2" id="g47">2</td><td class="ctr1" id="h66">5</td><td class="ctr2" id="i66">5</td><td class="ctr1" id="j50">1</td><td class="ctr2" id="k50">1</td></tr><tr><td id="a103"><a href="TimeSeriesRequest.java.html#L2624" class="el_method">setExtendPerformance(String)</a></td><td class="bar" id="b51"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="19" alt="19"/></td><td class="ctr2" id="c51">0%</td><td class="bar" id="d48"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e48">0%</td><td class="ctr1" id="f48">2</td><td class="ctr2" id="g48">2</td><td class="ctr1" id="h67">5</td><td class="ctr2" id="i67">5</td><td class="ctr1" id="j51">1</td><td class="ctr2" id="k51">1</td></tr><tr><td id="a107"><a href="TimeSeriesRequest.java.html#L2704" class="el_method">setPostTax(String)</a></td><td class="bar" id="b52"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="19" alt="19"/></td><td class="ctr2" id="c52">0%</td><td class="bar" id="d49"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e49">0%</td><td class="ctr1" id="f49">2</td><td class="ctr2" id="g49">2</td><td class="ctr1" id="h68">5</td><td class="ctr2" id="i68">5</td><td class="ctr1" id="j52">1</td><td class="ctr2" id="k52">1</td></tr><tr><td id="a123"><a href="TimeSeriesRequest.java.html#L2820" class="el_method">setUseCase(String)</a></td><td class="bar" id="b53"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="19" alt="19"/></td><td class="ctr2" id="c53">0%</td><td class="bar" id="d50"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e50">0%</td><td class="ctr1" id="f50">2</td><td class="ctr2" id="g50">2</td><td class="ctr1" id="h69">5</td><td class="ctr2" id="i69">5</td><td class="ctr1" id="j53">1</td><td class="ctr2" id="k53">1</td></tr><tr><td id="a111"><a href="TimeSeriesRequest.java.html#L2936" class="el_method">setProductId(String)</a></td><td class="bar" id="b54"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="19" alt="19"/></td><td class="ctr2" id="c54">0%</td><td class="bar" id="d51"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e51">0%</td><td class="ctr1" id="f51">2</td><td class="ctr2" id="g51">2</td><td class="ctr1" id="h70">5</td><td class="ctr2" id="i70">5</td><td class="ctr1" id="j54">1</td><td class="ctr2" id="k54">1</td></tr><tr><td id="a116"><a href="TimeSeriesRequest.java.html#L3016" class="el_method">setRequestId(String)</a></td><td class="bar" id="b55"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="19" alt="19"/></td><td class="ctr2" id="c55">0%</td><td class="bar" id="d52"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e52">0%</td><td class="ctr1" id="f52">2</td><td class="ctr2" id="g52">2</td><td class="ctr1" id="h71">5</td><td class="ctr2" id="i71">5</td><td class="ctr1" id="j55">1</td><td class="ctr2" id="k55">1</td></tr><tr><td id="a120"><a href="TimeSeriesRequest.java.html#L3088" class="el_method">setTraceId(String)</a></td><td class="bar" id="b56"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="19" alt="19"/></td><td class="ctr2" id="c56">0%</td><td class="bar" id="d53"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e53">0%</td><td class="ctr1" id="f53">2</td><td class="ctr2" id="g53">2</td><td class="ctr1" id="h72">5</td><td class="ctr2" id="i72">5</td><td class="ctr1" id="j56">1</td><td class="ctr2" id="k56">1</td></tr><tr><td id="a127"><a href="TimeSeriesRequest.java.html#L3160" class="el_method">setUserId(String)</a></td><td class="bar" id="b57"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="19" alt="19"/></td><td class="ctr2" id="c57">0%</td><td class="bar" id="d54"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e54">0%</td><td class="ctr1" id="f54">2</td><td class="ctr2" id="g54">2</td><td class="ctr1" id="h73">5</td><td class="ctr2" id="i73">5</td><td class="ctr1" id="j57">1</td><td class="ctr2" id="k57">1</td></tr><tr><td id="a89"><a href="TimeSeriesRequest.java.html#L3232" class="el_method">setAuthorization(String)</a></td><td class="bar" id="b58"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="19" alt="19"/></td><td class="ctr2" id="c58">0%</td><td class="bar" id="d55"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e55">0%</td><td class="ctr1" id="f55">2</td><td class="ctr2" id="g55">2</td><td class="ctr1" id="h74">5</td><td class="ctr2" id="i74">5</td><td class="ctr1" id="j58">1</td><td class="ctr2" id="k58">1</td></tr><tr><td id="a101"><a href="TimeSeriesRequest.java.html#L3336" class="el_method">setEntitlementProductId(String)</a></td><td class="bar" id="b59"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="19" alt="19"/></td><td class="ctr2" id="c59">0%</td><td class="bar" id="d56"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e56">0%</td><td class="ctr1" id="f56">2</td><td class="ctr2" id="g56">2</td><td class="ctr1" id="h75">5</td><td class="ctr2" id="i75">5</td><td class="ctr1" id="j59">1</td><td class="ctr2" id="k59">1</td></tr><tr><td id="a36"><a href="TimeSeriesRequest.java.html#L1716" class="el_method">ensureInvestmentIdsIsMutable()</a></td><td class="bar" id="b60"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="18" alt="18"/></td><td class="ctr2" id="c60">0%</td><td class="bar" id="d57"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e57">0%</td><td class="ctr1" id="f57">2</td><td class="ctr2" id="g57">2</td><td class="ctr1" id="h80">4</td><td class="ctr2" id="i80">4</td><td class="ctr1" id="j60">1</td><td class="ctr2" id="k60">1</td></tr><tr><td id="a35"><a href="TimeSeriesRequest.java.html#L1863" class="el_method">ensureDataPointsIsMutable()</a></td><td class="bar" id="b61"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="18" alt="18"/></td><td class="ctr2" id="c61">0%</td><td class="bar" id="d58"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e58">0%</td><td class="ctr1" id="f58">2</td><td class="ctr2" id="g58">2</td><td class="ctr1" id="h81">4</td><td class="ctr2" id="i81">4</td><td class="ctr1" id="j61">1</td><td class="ctr2" id="k61">1</td></tr><tr><td id="a74"><a href="TimeSeriesRequest.java.html#L2038" class="el_method">getStartDateBytes()</a></td><td class="bar" id="b62"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="18" alt="18"/></td><td class="ctr2" id="c62">0%</td><td class="bar" id="d59"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e59">0%</td><td class="ctr1" id="f59">2</td><td class="ctr2" id="g59">2</td><td class="ctr1" id="h24">7</td><td class="ctr2" id="i24">7</td><td class="ctr1" id="j62">1</td><td class="ctr2" id="k62">1</td></tr><tr><td id="a54"><a href="TimeSeriesRequest.java.html#L2122" class="el_method">getEndDateBytes()</a></td><td class="bar" id="b63"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="18" alt="18"/></td><td class="ctr2" id="c63">0%</td><td class="bar" id="d60"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e60">0%</td><td class="ctr1" id="f60">2</td><td class="ctr2" id="g60">2</td><td class="ctr1" id="h25">7</td><td class="ctr2" id="i25">7</td><td class="ctr1" id="j63">1</td><td class="ctr2" id="k63">1</td></tr><tr><td id="a41"><a href="TimeSeriesRequest.java.html#L2202" class="el_method">getCurrencyBytes()</a></td><td class="bar" id="b64"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="18" alt="18"/></td><td class="ctr2" id="c64">0%</td><td class="bar" id="d61"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e61">0%</td><td class="ctr1" id="f61">2</td><td class="ctr2" id="g61">2</td><td class="ctr1" id="h26">7</td><td class="ctr2" id="i26">7</td><td class="ctr1" id="j64">1</td><td class="ctr2" id="k64">1</td></tr><tr><td id="a66"><a href="TimeSeriesRequest.java.html#L2286" class="el_method">getPreCurrencyBytes()</a></td><td class="bar" id="b65"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="18" alt="18"/></td><td class="ctr2" id="c65">0%</td><td class="bar" id="d62"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e62">0%</td><td class="ctr1" id="f62">2</td><td class="ctr2" id="g62">2</td><td class="ctr1" id="h27">7</td><td class="ctr2" id="i27">7</td><td class="ctr1" id="j65">1</td><td class="ctr2" id="k65">1</td></tr><tr><td id="a70"><a href="TimeSeriesRequest.java.html#L2366" class="el_method">getReadCacheBytes()</a></td><td class="bar" id="b66"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="18" alt="18"/></td><td class="ctr2" id="c66">0%</td><td class="bar" id="d63"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e63">0%</td><td class="ctr1" id="f63">2</td><td class="ctr2" id="g63">2</td><td class="ctr1" id="h28">7</td><td class="ctr2" id="i28">7</td><td class="ctr1" id="j66">1</td><td class="ctr2" id="k66">1</td></tr><tr><td id="a47"><a href="TimeSeriesRequest.java.html#L2450" class="el_method">getDateFormatBytes()</a></td><td class="bar" id="b67"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="18" alt="18"/></td><td class="ctr2" id="c67">0%</td><td class="bar" id="d64"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e64">0%</td><td class="ctr1" id="f64">2</td><td class="ctr2" id="g64">2</td><td class="ctr1" id="h29">7</td><td class="ctr2" id="i29">7</td><td class="ctr1" id="j67">1</td><td class="ctr2" id="k67">1</td></tr><tr><td id="a49"><a href="TimeSeriesRequest.java.html#L2522" class="el_method">getDecimalFormatBytes()</a></td><td class="bar" id="b68"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="18" alt="18"/></td><td class="ctr2" id="c68">0%</td><td class="bar" id="d65"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e65">0%</td><td class="ctr1" id="f65">2</td><td class="ctr2" id="g65">2</td><td class="ctr1" id="h30">7</td><td class="ctr2" id="i30">7</td><td class="ctr1" id="j68">1</td><td class="ctr2" id="k68">1</td></tr><tr><td id="a58"><a href="TimeSeriesRequest.java.html#L2602" class="el_method">getExtendPerformanceBytes()</a></td><td class="bar" id="b69"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="18" alt="18"/></td><td class="ctr2" id="c69">0%</td><td class="bar" id="d66"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e66">0%</td><td class="ctr1" id="f66">2</td><td class="ctr2" id="g66">2</td><td class="ctr1" id="h31">7</td><td class="ctr2" id="i31">7</td><td class="ctr1" id="j69">1</td><td class="ctr2" id="k69">1</td></tr><tr><td id="a64"><a href="TimeSeriesRequest.java.html#L2686" class="el_method">getPostTaxBytes()</a></td><td class="bar" id="b70"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="18" alt="18"/></td><td class="ctr2" id="c70">0%</td><td class="bar" id="d67"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e67">0%</td><td class="ctr1" id="f67">2</td><td class="ctr2" id="g67">2</td><td class="ctr1" id="h32">7</td><td class="ctr2" id="i32">7</td><td class="ctr1" id="j70">1</td><td class="ctr2" id="k70">1</td></tr><tr><td id="a78"><a href="TimeSeriesRequest.java.html#L2802" class="el_method">getUseCaseBytes()</a></td><td class="bar" id="b71"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="18" alt="18"/></td><td class="ctr2" id="c71">0%</td><td class="bar" id="d68"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e68">0%</td><td class="ctr1" id="f68">2</td><td class="ctr2" id="g68">2</td><td class="ctr1" id="h33">7</td><td class="ctr2" id="i33">7</td><td class="ctr1" id="j71">1</td><td class="ctr2" id="k71">1</td></tr><tr><td id="a68"><a href="TimeSeriesRequest.java.html#L2914" class="el_method">getProductIdBytes()</a></td><td class="bar" id="b72"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="18" alt="18"/></td><td class="ctr2" id="c72">0%</td><td class="bar" id="d69"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e69">0%</td><td class="ctr1" id="f69">2</td><td class="ctr2" id="g69">2</td><td class="ctr1" id="h34">7</td><td class="ctr2" id="i34">7</td><td class="ctr1" id="j72">1</td><td class="ctr2" id="k72">1</td></tr><tr><td id="a72"><a href="TimeSeriesRequest.java.html#L2998" class="el_method">getRequestIdBytes()</a></td><td class="bar" id="b73"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="18" alt="18"/></td><td class="ctr2" id="c73">0%</td><td class="bar" id="d70"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e70">0%</td><td class="ctr1" id="f70">2</td><td class="ctr2" id="g70">2</td><td class="ctr1" id="h35">7</td><td class="ctr2" id="i35">7</td><td class="ctr1" id="j73">1</td><td class="ctr2" id="k73">1</td></tr><tr><td id="a76"><a href="TimeSeriesRequest.java.html#L3070" class="el_method">getTraceIdBytes()</a></td><td class="bar" id="b74"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="18" alt="18"/></td><td class="ctr2" id="c74">0%</td><td class="bar" id="d71"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e71">0%</td><td class="ctr1" id="f71">2</td><td class="ctr2" id="g71">2</td><td class="ctr1" id="h36">7</td><td class="ctr2" id="i36">7</td><td class="ctr1" id="j74">1</td><td class="ctr2" id="k74">1</td></tr><tr><td id="a82"><a href="TimeSeriesRequest.java.html#L3142" class="el_method">getUserIdBytes()</a></td><td class="bar" id="b75"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="18" alt="18"/></td><td class="ctr2" id="c75">0%</td><td class="bar" id="d72"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e72">0%</td><td class="ctr1" id="f72">2</td><td class="ctr2" id="g72">2</td><td class="ctr1" id="h37">7</td><td class="ctr2" id="i37">7</td><td class="ctr1" id="j75">1</td><td class="ctr2" id="k75">1</td></tr><tr><td id="a38"><a href="TimeSeriesRequest.java.html#L3214" class="el_method">getAuthorizationBytes()</a></td><td class="bar" id="b76"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="18" alt="18"/></td><td class="ctr2" id="c76">0%</td><td class="bar" id="d73"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e73">0%</td><td class="ctr1" id="f73">2</td><td class="ctr2" id="g73">2</td><td class="ctr1" id="h38">7</td><td class="ctr2" id="i38">7</td><td class="ctr1" id="j76">1</td><td class="ctr2" id="k76">1</td></tr><tr><td id="a56"><a href="TimeSeriesRequest.java.html#L3318" class="el_method">getEntitlementProductIdBytes()</a></td><td class="bar" id="b77"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="18" alt="18"/></td><td class="ctr2" id="c77">0%</td><td class="bar" id="d74"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e74">0%</td><td class="ctr1" id="f74">2</td><td class="ctr2" id="g74">2</td><td class="ctr1" id="h39">7</td><td class="ctr2" id="i39">7</td><td class="ctr1" id="j77">1</td><td class="ctr2" id="k77">1</td></tr><tr><td id="a1"><a href="TimeSeriesRequest.java.html#L1818" class="el_method">addAllInvestmentIds(Iterable)</a></td><td class="bar" id="b78"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="16" alt="16"/></td><td class="ctr2" id="c78">0%</td><td class="bar" id="d81"/><td class="ctr2" id="e81">n/a</td><td class="ctr1" id="f81">1</td><td class="ctr2" id="g81">1</td><td class="ctr1" id="h76">5</td><td class="ctr2" id="i76">5</td><td class="ctr1" id="j78">1</td><td class="ctr2" id="k78">1</td></tr><tr><td id="a0"><a href="TimeSeriesRequest.java.html#L1965" class="el_method">addAllDataPoints(Iterable)</a></td><td class="bar" id="b79"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="16" alt="16"/></td><td class="ctr2" id="c79">0%</td><td class="bar" id="d82"/><td class="ctr2" id="e82">n/a</td><td class="ctr1" id="f82">1</td><td class="ctr2" id="g82">1</td><td class="ctr1" id="h77">5</td><td class="ctr2" id="i77">5</td><td class="ctr1" id="j79">1</td><td class="ctr2" id="k79">1</td></tr><tr><td id="a8"><a href="TimeSeriesRequest.java.html#L1335" class="el_method">buildPartial()</a></td><td class="bar" id="b80"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="15" alt="15"/></td><td class="ctr2" id="c80">0%</td><td class="bar" id="d75"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e75">0%</td><td class="ctr1" id="f75">2</td><td class="ctr2" id="g75">2</td><td class="ctr1" id="h82">4</td><td class="ctr2" id="i82">4</td><td class="ctr1" id="j80">1</td><td class="ctr2" id="k80">1</td></tr><tr><td id="a86"><a href="TimeSeriesRequest.java.html#L1444" class="el_method">mergeFrom(Message)</a></td><td class="bar" id="b81"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="14" alt="14"/></td><td class="ctr2" id="c81">0%</td><td class="bar" id="d76"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e76">0%</td><td class="ctr1" id="f76">2</td><td class="ctr2" id="g76">2</td><td class="ctr1" id="h83">4</td><td class="ctr2" id="i83">4</td><td class="ctr1" id="j81">1</td><td class="ctr2" id="k81">1</td></tr><tr><td id="a28"><a href="TimeSeriesRequest.java.html#L2075" class="el_method">clearStartDate()</a></td><td class="bar" id="b82"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="14" alt="14"/></td><td class="ctr2" id="c82">0%</td><td class="bar" id="d83"/><td class="ctr2" id="e83">n/a</td><td class="ctr1" id="f83">1</td><td class="ctr2" id="g83">1</td><td class="ctr1" id="h84">4</td><td class="ctr2" id="i84">4</td><td class="ctr1" id="j82">1</td><td class="ctr2" id="k82">1</td></tr><tr><td id="a17"><a href="TimeSeriesRequest.java.html#L2151" class="el_method">clearEndDate()</a></td><td class="bar" id="b83"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="14" alt="14"/></td><td class="ctr2" id="c83">0%</td><td class="bar" id="d84"/><td class="ctr2" id="e84">n/a</td><td class="ctr1" id="f84">1</td><td class="ctr2" id="g84">1</td><td class="ctr1" id="h85">4</td><td class="ctr2" id="i85">4</td><td class="ctr1" id="j83">1</td><td class="ctr2" id="k83">1</td></tr><tr><td id="a13"><a href="TimeSeriesRequest.java.html#L2239" class="el_method">clearCurrency()</a></td><td class="bar" id="b84"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="14" alt="14"/></td><td class="ctr2" id="c84">0%</td><td class="bar" id="d85"/><td class="ctr2" id="e85">n/a</td><td class="ctr1" id="f85">1</td><td class="ctr2" id="g85">1</td><td class="ctr1" id="h86">4</td><td class="ctr2" id="i86">4</td><td class="ctr1" id="j84">1</td><td class="ctr2" id="k84">1</td></tr><tr><td id="a24"><a href="TimeSeriesRequest.java.html#L2315" class="el_method">clearPreCurrency()</a></td><td class="bar" id="b85"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="14" alt="14"/></td><td class="ctr2" id="c85">0%</td><td class="bar" id="d86"/><td class="ctr2" id="e86">n/a</td><td class="ctr1" id="f86">1</td><td class="ctr2" id="g86">1</td><td class="ctr1" id="h87">4</td><td class="ctr2" id="i87">4</td><td class="ctr1" id="j85">1</td><td class="ctr2" id="k85">1</td></tr><tr><td id="a26"><a href="TimeSeriesRequest.java.html#L2403" class="el_method">clearReadCache()</a></td><td class="bar" id="b86"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="14" alt="14"/></td><td class="ctr2" id="c86">0%</td><td class="bar" id="d87"/><td class="ctr2" id="e87">n/a</td><td class="ctr1" id="f87">1</td><td class="ctr2" id="g87">1</td><td class="ctr1" id="h88">4</td><td class="ctr2" id="i88">4</td><td class="ctr1" id="j86">1</td><td class="ctr2" id="k86">1</td></tr><tr><td id="a15"><a href="TimeSeriesRequest.java.html#L2479" class="el_method">clearDateFormat()</a></td><td class="bar" id="b87"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="14" alt="14"/></td><td class="ctr2" id="c87">0%</td><td class="bar" id="d88"/><td class="ctr2" id="e88">n/a</td><td class="ctr1" id="f88">1</td><td class="ctr2" id="g88">1</td><td class="ctr1" id="h89">4</td><td class="ctr2" id="i89">4</td><td class="ctr1" id="j87">1</td><td class="ctr2" id="k87">1</td></tr><tr><td id="a16"><a href="TimeSeriesRequest.java.html#L2551" class="el_method">clearDecimalFormat()</a></td><td class="bar" id="b88"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="14" alt="14"/></td><td class="ctr2" id="c88">0%</td><td class="bar" id="d89"/><td class="ctr2" id="e89">n/a</td><td class="ctr1" id="f89">1</td><td class="ctr2" id="g89">1</td><td class="ctr1" id="h90">4</td><td class="ctr2" id="i90">4</td><td class="ctr1" id="j88">1</td><td class="ctr2" id="k88">1</td></tr><tr><td id="a19"><a href="TimeSeriesRequest.java.html#L2639" class="el_method">clearExtendPerformance()</a></td><td class="bar" id="b89"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="14" alt="14"/></td><td class="ctr2" id="c89">0%</td><td class="bar" id="d90"/><td class="ctr2" id="e90">n/a</td><td class="ctr1" id="f90">1</td><td class="ctr2" id="g90">1</td><td class="ctr1" id="h91">4</td><td class="ctr2" id="i91">4</td><td class="ctr1" id="j89">1</td><td class="ctr2" id="k89">1</td></tr><tr><td id="a23"><a href="TimeSeriesRequest.java.html#L2715" class="el_method">clearPostTax()</a></td><td class="bar" id="b90"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="14" alt="14"/></td><td class="ctr2" id="c90">0%</td><td class="bar" id="d91"/><td class="ctr2" id="e91">n/a</td><td class="ctr1" id="f91">1</td><td class="ctr2" id="g91">1</td><td class="ctr1" id="h92">4</td><td class="ctr2" id="i92">4</td><td class="ctr1" id="j90">1</td><td class="ctr2" id="k90">1</td></tr><tr><td id="a30"><a href="TimeSeriesRequest.java.html#L2831" class="el_method">clearUseCase()</a></td><td class="bar" id="b91"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="14" alt="14"/></td><td class="ctr2" id="c91">0%</td><td class="bar" id="d92"/><td class="ctr2" id="e92">n/a</td><td class="ctr1" id="f92">1</td><td class="ctr2" id="g92">1</td><td class="ctr1" id="h93">4</td><td class="ctr2" id="i93">4</td><td class="ctr1" id="j91">1</td><td class="ctr2" id="k91">1</td></tr><tr><td id="a25"><a href="TimeSeriesRequest.java.html#L2951" class="el_method">clearProductId()</a></td><td class="bar" id="b92"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="14" alt="14"/></td><td class="ctr2" id="c92">0%</td><td class="bar" id="d93"/><td class="ctr2" id="e93">n/a</td><td class="ctr1" id="f93">1</td><td class="ctr2" id="g93">1</td><td class="ctr1" id="h94">4</td><td class="ctr2" id="i94">4</td><td class="ctr1" id="j92">1</td><td class="ctr2" id="k92">1</td></tr><tr><td id="a27"><a href="TimeSeriesRequest.java.html#L3027" class="el_method">clearRequestId()</a></td><td class="bar" id="b93"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="14" alt="14"/></td><td class="ctr2" id="c93">0%</td><td class="bar" id="d94"/><td class="ctr2" id="e94">n/a</td><td class="ctr1" id="f94">1</td><td class="ctr2" id="g94">1</td><td class="ctr1" id="h95">4</td><td class="ctr2" id="i95">4</td><td class="ctr1" id="j93">1</td><td class="ctr2" id="k93">1</td></tr><tr><td id="a29"><a href="TimeSeriesRequest.java.html#L3099" class="el_method">clearTraceId()</a></td><td class="bar" id="b94"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="14" alt="14"/></td><td class="ctr2" id="c94">0%</td><td class="bar" id="d95"/><td class="ctr2" id="e95">n/a</td><td class="ctr1" id="f95">1</td><td class="ctr2" id="g95">1</td><td class="ctr1" id="h96">4</td><td class="ctr2" id="i96">4</td><td class="ctr1" id="j94">1</td><td class="ctr2" id="k94">1</td></tr><tr><td id="a33"><a href="TimeSeriesRequest.java.html#L3171" class="el_method">clearUserId()</a></td><td class="bar" id="b95"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="14" alt="14"/></td><td class="ctr2" id="c95">0%</td><td class="bar" id="d96"/><td class="ctr2" id="e96">n/a</td><td class="ctr1" id="f96">1</td><td class="ctr2" id="g96">1</td><td class="ctr1" id="h97">4</td><td class="ctr2" id="i97">4</td><td class="ctr1" id="j95">1</td><td class="ctr2" id="k95">1</td></tr><tr><td id="a11"><a href="TimeSeriesRequest.java.html#L3243" class="el_method">clearAuthorization()</a></td><td class="bar" id="b96"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="14" alt="14"/></td><td class="ctr2" id="c96">0%</td><td class="bar" id="d97"/><td class="ctr2" id="e97">n/a</td><td class="ctr1" id="f97">1</td><td class="ctr2" id="g97">1</td><td class="ctr1" id="h98">4</td><td class="ctr2" id="i98">4</td><td class="ctr1" id="j96">1</td><td class="ctr2" id="k96">1</td></tr><tr><td id="a18"><a href="TimeSeriesRequest.java.html#L3347" class="el_method">clearEntitlementProductId()</a></td><td class="bar" id="b97"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="14" alt="14"/></td><td class="ctr2" id="c97">0%</td><td class="bar" id="d98"/><td class="ctr2" id="e98">n/a</td><td class="ctr1" id="f98">1</td><td class="ctr2" id="g98">1</td><td class="ctr1" id="h99">4</td><td class="ctr2" id="i99">4</td><td class="ctr1" id="j97">1</td><td class="ctr2" id="k97">1</td></tr><tr><td id="a21"><a href="TimeSeriesRequest.java.html#L1834" class="el_method">clearInvestmentIds()</a></td><td class="bar" id="b98"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="13" alt="13"/></td><td class="ctr2" id="c98">0%</td><td class="bar" id="d99"/><td class="ctr2" id="e99">n/a</td><td class="ctr1" id="f99">1</td><td class="ctr2" id="g99">1</td><td class="ctr1" id="h78">5</td><td class="ctr2" id="i78">5</td><td class="ctr1" id="j98">1</td><td class="ctr2" id="k98">1</td></tr><tr><td id="a14"><a href="TimeSeriesRequest.java.html#L1981" class="el_method">clearDataPoints()</a></td><td class="bar" id="b99"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="13" alt="13"/></td><td class="ctr2" id="c99">0%</td><td class="bar" id="d100"/><td class="ctr2" id="e100">n/a</td><td class="ctr1" id="f100">1</td><td class="ctr2" id="g100">1</td><td class="ctr1" id="h79">5</td><td class="ctr2" id="i79">5</td><td class="ctr1" id="j99">1</td><td class="ctr2" id="k99">1</td></tr><tr><td id="a126"><a href="TimeSeriesRequest.java.html#L2759" class="el_method">setUseRequireId(boolean)</a></td><td class="bar" id="b100"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="13" alt="13"/></td><td class="ctr2" id="c100">0%</td><td class="bar" id="d101"/><td class="ctr2" id="e101">n/a</td><td class="ctr1" id="f101">1</td><td class="ctr2" id="g101">1</td><td class="ctr1" id="h100">4</td><td class="ctr2" id="i100">4</td><td class="ctr1" id="j100">1</td><td class="ctr2" id="k100">1</td></tr><tr><td id="a32"><a href="TimeSeriesRequest.java.html#L2773" class="el_method">clearUseRequireId()</a></td><td class="bar" id="b101"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="13" alt="13"/></td><td class="ctr2" id="c101">0%</td><td class="bar" id="d102"/><td class="ctr2" id="e102">n/a</td><td class="ctr1" id="f102">1</td><td class="ctr2" id="g102">1</td><td class="ctr1" id="h101">4</td><td class="ctr2" id="i101">4</td><td class="ctr1" id="j101">1</td><td class="ctr2" id="k101">1</td></tr><tr><td id="a125"><a href="TimeSeriesRequest.java.html#L2867" class="el_method">setUseNewCcs(boolean)</a></td><td class="bar" id="b102"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="13" alt="13"/></td><td class="ctr2" id="c102">0%</td><td class="bar" id="d103"/><td class="ctr2" id="e103">n/a</td><td class="ctr1" id="f103">1</td><td class="ctr2" id="g103">1</td><td class="ctr1" id="h102">4</td><td class="ctr2" id="i102">4</td><td class="ctr1" id="j102">1</td><td class="ctr2" id="k102">1</td></tr><tr><td id="a31"><a href="TimeSeriesRequest.java.html#L2877" class="el_method">clearUseNewCcs()</a></td><td class="bar" id="b103"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="13" alt="13"/></td><td class="ctr2" id="c103">0%</td><td class="bar" id="d104"/><td class="ctr2" id="e104">n/a</td><td class="ctr1" id="f104">1</td><td class="ctr2" id="g104">1</td><td class="ctr1" id="h103">4</td><td class="ctr2" id="i103">4</td><td class="ctr1" id="j103">1</td><td class="ctr2" id="k103">1</td></tr><tr><td id="a91"><a href="TimeSeriesRequest.java.html#L3279" class="el_method">setCheckEntitlement(boolean)</a></td><td class="bar" id="b104"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="13" alt="13"/></td><td class="ctr2" id="c104">0%</td><td class="bar" id="d105"/><td class="ctr2" id="e105">n/a</td><td class="ctr1" id="f105">1</td><td class="ctr2" id="g105">1</td><td class="ctr1" id="h104">4</td><td class="ctr2" id="i104">4</td><td class="ctr1" id="j104">1</td><td class="ctr2" id="k104">1</td></tr><tr><td id="a12"><a href="TimeSeriesRequest.java.html#L3289" class="el_method">clearCheckEntitlement()</a></td><td class="bar" id="b105"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="13" alt="13"/></td><td class="ctr2" id="c105">0%</td><td class="bar" id="d106"/><td class="ctr2" id="e106">n/a</td><td class="ctr1" id="f106">1</td><td class="ctr2" id="g106">1</td><td class="ctr1" id="h105">4</td><td class="ctr2" id="i105">4</td><td class="ctr1" id="j105">1</td><td class="ctr2" id="k105">1</td></tr><tr><td id="a7"><a href="TimeSeriesRequest.java.html#L1326" class="el_method">build()</a></td><td class="bar" id="b106"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="11" alt="11"/></td><td class="ctr2" id="c106">0%</td><td class="bar" id="d77"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="2" alt="2"/></td><td class="ctr2" id="e77">0%</td><td class="ctr1" id="f77">2</td><td class="ctr2" id="g77">2</td><td class="ctr1" id="h106">4</td><td class="ctr2" id="i106">4</td><td class="ctr1" id="j106">1</td><td class="ctr2" id="k106">1</td></tr><tr><td id="a115"><a href="TimeSeriesRequest.java.html#L1434" class="el_method">setRepeatedField(Descriptors.FieldDescriptor, int, Object)</a></td><td class="bar" id="b107"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="7" alt="7"/></td><td class="ctr2" id="c107">0%</td><td class="bar" id="d107"/><td class="ctr2" id="e107">n/a</td><td class="ctr1" id="f107">1</td><td class="ctr2" id="g107">1</td><td class="ctr1" id="h110">1</td><td class="ctr2" id="i110">1</td><td class="ctr1" id="j107">1</td><td class="ctr2" id="k107">1</td></tr><tr><td id="a105"><a href="TimeSeriesRequest.java.html#L1418" class="el_method">setField(Descriptors.FieldDescriptor, Object)</a></td><td class="bar" id="b108"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="6" alt="6"/></td><td class="ctr2" id="c108">0%</td><td class="bar" id="d108"/><td class="ctr2" id="e108">n/a</td><td class="ctr1" id="f108">1</td><td class="ctr2" id="g108">1</td><td class="ctr1" id="h111">1</td><td class="ctr2" id="i111">1</td><td class="ctr1" id="j108">1</td><td class="ctr2" id="k108">1</td></tr><tr><td id="a6"><a href="TimeSeriesRequest.java.html#L1440" class="el_method">addRepeatedField(Descriptors.FieldDescriptor, Object)</a></td><td class="bar" id="b109"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="6" alt="6"/></td><td class="ctr2" id="c109">0%</td><td class="bar" id="d109"/><td class="ctr2" id="e109">n/a</td><td class="ctr1" id="f109">1</td><td class="ctr2" id="g109">1</td><td class="ctr1" id="h112">1</td><td class="ctr2" id="i112">1</td><td class="ctr1" id="j109">1</td><td class="ctr2" id="k109">1</td></tr><tr><td id="a62"><a href="TimeSeriesRequest.java.html#L1731" class="el_method">getInvestmentIdsList()</a></td><td class="bar" id="b110"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="6" alt="6"/></td><td class="ctr2" id="c110">0%</td><td class="bar" id="d110"/><td class="ctr2" id="e110">n/a</td><td class="ctr1" id="f110">1</td><td class="ctr2" id="g110">1</td><td class="ctr1" id="h107">2</td><td class="ctr2" id="i107">2</td><td class="ctr1" id="j110">1</td><td class="ctr2" id="k110">1</td></tr><tr><td id="a45"><a href="TimeSeriesRequest.java.html#L1878" class="el_method">getDataPointsList()</a></td><td class="bar" id="b111"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="6" alt="6"/></td><td class="ctr2" id="c111">0%</td><td class="bar" id="d111"/><td class="ctr2" id="e111">n/a</td><td class="ctr1" id="f111">1</td><td class="ctr2" id="g111">1</td><td class="ctr1" id="h108">2</td><td class="ctr2" id="i108">2</td><td class="ctr1" id="j111">1</td><td class="ctr2" id="k111">1</td></tr><tr><td id="a83"><a href="TimeSeriesRequest.java.html#L1268" class="el_method">internalGetFieldAccessorTable()</a></td><td class="bar" id="b112"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="5" alt="5"/></td><td class="ctr2" id="c112">0%</td><td class="bar" id="d112"/><td class="ctr2" id="e112">n/a</td><td class="ctr1" id="f112">1</td><td class="ctr2" id="g112">1</td><td class="ctr1" id="h109">2</td><td class="ctr2" id="i109">2</td><td class="ctr1" id="j112">1</td><td class="ctr2" id="k112">1</td></tr><tr><td id="a20"><a href="TimeSeriesRequest.java.html#L1423" class="el_method">clearField(Descriptors.FieldDescriptor)</a></td><td class="bar" id="b113"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="5" alt="5"/></td><td class="ctr2" id="c113">0%</td><td class="bar" id="d113"/><td class="ctr2" id="e113">n/a</td><td class="ctr1" id="f113">1</td><td class="ctr2" id="g113">1</td><td class="ctr1" id="h113">1</td><td class="ctr2" id="i113">1</td><td class="ctr1" id="j113">1</td><td class="ctr2" id="k113">1</td></tr><tr><td id="a22"><a href="TimeSeriesRequest.java.html#L1428" class="el_method">clearOneof(Descriptors.OneofDescriptor)</a></td><td class="bar" id="b114"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="5" alt="5"/></td><td class="ctr2" id="c114">0%</td><td class="bar" id="d114"/><td class="ctr2" id="e114">n/a</td><td class="ctr1" id="f114">1</td><td class="ctr2" id="g114">1</td><td class="ctr1" id="h114">1</td><td class="ctr2" id="i114">1</td><td class="ctr1" id="j114">1</td><td class="ctr2" id="k114">1</td></tr><tr><td id="a59"><a href="TimeSeriesRequest.java.html#L1755" class="el_method">getInvestmentIds(int)</a></td><td class="bar" id="b115"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="5" alt="5"/></td><td class="ctr2" id="c115">0%</td><td class="bar" id="d115"/><td class="ctr2" id="e115">n/a</td><td class="ctr1" id="f115">1</td><td class="ctr2" id="g115">1</td><td class="ctr1" id="h115">1</td><td class="ctr2" id="i115">1</td><td class="ctr1" id="j115">1</td><td class="ctr2" id="k115">1</td></tr><tr><td id="a60"><a href="TimeSeriesRequest.java.html#L1768" class="el_method">getInvestmentIdsBytes(int)</a></td><td class="bar" id="b116"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="5" alt="5"/></td><td class="ctr2" id="c116">0%</td><td class="bar" id="d116"/><td class="ctr2" id="e116">n/a</td><td class="ctr1" id="f116">1</td><td class="ctr2" id="g116">1</td><td class="ctr1" id="h116">1</td><td class="ctr2" id="i116">1</td><td class="ctr1" id="j116">1</td><td class="ctr2" id="k116">1</td></tr><tr><td id="a42"><a href="TimeSeriesRequest.java.html#L1902" class="el_method">getDataPoints(int)</a></td><td class="bar" id="b117"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="5" alt="5"/></td><td class="ctr2" id="c117">0%</td><td class="bar" id="d117"/><td class="ctr2" id="e117">n/a</td><td class="ctr1" id="f117">1</td><td class="ctr2" id="g117">1</td><td class="ctr1" id="h117">1</td><td class="ctr2" id="i117">1</td><td class="ctr1" id="j117">1</td><td class="ctr2" id="k117">1</td></tr><tr><td id="a43"><a href="TimeSeriesRequest.java.html#L1915" class="el_method">getDataPointsBytes(int)</a></td><td class="bar" id="b118"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="5" alt="5"/></td><td class="ctr2" id="c118">0%</td><td class="bar" id="d118"/><td class="ctr2" id="e118">n/a</td><td class="ctr1" id="f118">1</td><td class="ctr2" id="g118">1</td><td class="ctr1" id="h118">1</td><td class="ctr2" id="i118">1</td><td class="ctr1" id="j118">1</td><td class="ctr2" id="k118">1</td></tr><tr><td id="a122"><a href="TimeSeriesRequest.java.html#L3369" class="el_method">setUnknownFields(UnknownFieldSet)</a></td><td class="bar" id="b119"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="5" alt="5"/></td><td class="ctr2" id="c119">0%</td><td class="bar" id="d119"/><td class="ctr2" id="e119">n/a</td><td class="ctr1" id="f119">1</td><td class="ctr2" id="g119">1</td><td class="ctr1" id="h119">1</td><td class="ctr2" id="i119">1</td><td class="ctr1" id="j119">1</td><td class="ctr2" id="k119">1</td></tr><tr><td id="a88"><a href="TimeSeriesRequest.java.html#L3375" class="el_method">mergeUnknownFields(UnknownFieldSet)</a></td><td class="bar" id="b120"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="5" alt="5"/></td><td class="ctr2" id="c120">0%</td><td class="bar" id="d120"/><td class="ctr2" id="e120">n/a</td><td class="ctr1" id="f120">1</td><td class="ctr2" id="g120">1</td><td class="ctr1" id="h120">1</td><td class="ctr2" id="i120">1</td><td class="ctr1" id="j120">1</td><td class="ctr2" id="k120">1</td></tr><tr><td id="a34"><a href="TimeSeriesRequest.java.html#L1412" class="el_method">clone()</a></td><td class="bar" id="b121"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c121">0%</td><td class="bar" id="d121"/><td class="ctr2" id="e121">n/a</td><td class="ctr1" id="f121">1</td><td class="ctr2" id="g121">1</td><td class="ctr1" id="h121">1</td><td class="ctr2" id="i121">1</td><td class="ctr1" id="j121">1</td><td class="ctr2" id="k121">1</td></tr><tr><td id="a61"><a href="TimeSeriesRequest.java.html#L1743" class="el_method">getInvestmentIdsCount()</a></td><td class="bar" id="b122"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c122">0%</td><td class="bar" id="d122"/><td class="ctr2" id="e122">n/a</td><td class="ctr1" id="f122">1</td><td class="ctr2" id="g122">1</td><td class="ctr1" id="h122">1</td><td class="ctr2" id="i122">1</td><td class="ctr1" id="j122">1</td><td class="ctr2" id="k122">1</td></tr><tr><td id="a44"><a href="TimeSeriesRequest.java.html#L1890" class="el_method">getDataPointsCount()</a></td><td class="bar" id="b123"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c123">0%</td><td class="bar" id="d123"/><td class="ctr2" id="e123">n/a</td><td class="ctr1" id="f123">1</td><td class="ctr2" id="g123">1</td><td class="ctr1" id="h123">1</td><td class="ctr2" id="i123">1</td><td class="ctr1" id="j123">1</td><td class="ctr2" id="k123">1</td></tr><tr><td id="a80"><a href="TimeSeriesRequest.java.html#L2746" class="el_method">getUseRequireId()</a></td><td class="bar" id="b124"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c124">0%</td><td class="bar" id="d124"/><td class="ctr2" id="e124">n/a</td><td class="ctr1" id="f124">1</td><td class="ctr2" id="g124">1</td><td class="ctr1" id="h124">1</td><td class="ctr2" id="i124">1</td><td class="ctr1" id="j124">1</td><td class="ctr2" id="k124">1</td></tr><tr><td id="a79"><a href="TimeSeriesRequest.java.html#L2858" class="el_method">getUseNewCcs()</a></td><td class="bar" id="b125"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c125">0%</td><td class="bar" id="d125"/><td class="ctr2" id="e125">n/a</td><td class="ctr1" id="f125">1</td><td class="ctr2" id="g125">1</td><td class="ctr1" id="h125">1</td><td class="ctr2" id="i125">1</td><td class="ctr1" id="j125">1</td><td class="ctr2" id="k125">1</td></tr><tr><td id="a39"><a href="TimeSeriesRequest.java.html#L3270" class="el_method">getCheckEntitlement()</a></td><td class="bar" id="b126"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c126">0%</td><td class="bar" id="d126"/><td class="ctr2" id="e126">n/a</td><td class="ctr1" id="f126">1</td><td class="ctr2" id="g126">1</td><td class="ctr1" id="h126">1</td><td class="ctr2" id="i126">1</td><td class="ctr1" id="j126">1</td><td class="ctr2" id="k126">1</td></tr><tr><td id="a51"><a href="TimeSeriesRequest.java.html#L1262" class="el_method">getDescriptor()</a></td><td class="bar" id="b127"/><td class="ctr2" id="c127">0%</td><td class="bar" id="d127"/><td class="ctr2" id="e127">n/a</td><td class="ctr1" id="f127">1</td><td class="ctr2" id="g127">1</td><td class="ctr1" id="h127">1</td><td class="ctr2" id="i127">1</td><td class="ctr1" id="j127">1</td><td class="ctr2" id="k127">1</td></tr><tr><td id="a52"><a href="TimeSeriesRequest.java.html#L1316" class="el_method">getDescriptorForType()</a></td><td class="bar" id="b128"/><td class="ctr2" id="c128">0%</td><td class="bar" id="d128"/><td class="ctr2" id="e128">n/a</td><td class="ctr1" id="f128">1</td><td class="ctr2" id="g128">1</td><td class="ctr1" id="h128">1</td><td class="ctr2" id="i128">1</td><td class="ctr1" id="j128">1</td><td class="ctr2" id="k128">1</td></tr><tr><td id="a50"><a href="TimeSeriesRequest.java.html#L1321" class="el_method">getDefaultInstanceForType()</a></td><td class="bar" id="b129"/><td class="ctr2" id="c129">0%</td><td class="bar" id="d129"/><td class="ctr2" id="e129">n/a</td><td class="ctr1" id="f129">1</td><td class="ctr2" id="g129">1</td><td class="ctr1" id="h129">1</td><td class="ctr2" id="i129">1</td><td class="ctr1" id="j129">1</td><td class="ctr2" id="k129">1</td></tr><tr><td id="a84"><a href="TimeSeriesRequest.java.html#L1570" class="el_method">isInitialized()</a></td><td class="bar" id="b130"/><td class="ctr2" id="c130">0%</td><td class="bar" id="d130"/><td class="ctr2" id="e130">n/a</td><td class="ctr1" id="f130">1</td><td class="ctr2" id="g130">1</td><td class="ctr1" id="h130">1</td><td class="ctr2" id="i130">1</td><td class="ctr1" id="j130">1</td><td class="ctr2" id="k130">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>