<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TimeSeriesController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <a href="index.source.html" class="el_package">com.morningstar.martapi.controller</a> &gt; <span class="el_source">TimeSeriesController.java</span></div><h1>TimeSeriesController.java</h1><pre class="source lang-java linenums">package com.morningstar.martapi.controller;

import static com.morningstar.martapi.config.Constant.LOG_REQUEST_PAYLOAD_LIMIT_MILLISECONDS;
import static com.morningstar.martapi.config.Constant.RESPONSE;
import static com.morningstar.martapi.config.Constant.RESPONSE_ERROR;
import static com.morningstar.martapi.config.Constant.SUCCESSFUL_RESPONSE;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.EVENT_DESCRIPTION;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.EVENT_TYPE;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.EXCEPTION_TYPE;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.EXECUTE_TIME;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.PRODUCT_ID;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.REQUEST_PARAM;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.REQUEST_PAYLOAD;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.REQUEST_TYPE;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.USER_ID;

import com.morningstar.martapi.exception.TsCacheApiValidationException;
import com.morningstar.martapi.exception.TsCacheProtobufValidationException;
import com.morningstar.martapi.exception.TsGridViewValidationException;
import com.morningstar.martapi.exception.ValidationException;
import com.morningstar.martapi.validator.RequestValidationHandler;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.martgateway.domains.timeseries.entity.TSResponse;
import com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf;
import com.morningstar.martgateway.infrastructures.log.LogAttribute;
import com.morningstar.martgateway.infrastructures.log.LogEntity;
import com.morningstar.martgateway.infrastructures.log.LogEntry;
import com.morningstar.martgateway.infrastructures.log.LogHelper;
import com.morningstar.martgateway.interfaces.MartGateway;
import com.morningstar.martcommon.entity.result.request.MartRequest;
import com.morningstar.martgateway.util.JsonUtils;
import com.morningstar.martgateway.util.JwtUtil;
import io.swagger.annotations.ApiParam;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping(value = {&quot;/v1/time-series-data/investments/{investment-ids}&quot;, &quot;/investment-api/v1/time-series-data/investments/{investment-ids}&quot;},
        produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE})
public class TimeSeriesController {

    private final MartGateway&lt;TSResponse, MartRequest&gt; tsOldRspGateway;
    private final MartGateway&lt;InvestmentResponse, MartRequest&gt; tsNewRspGateway;

    private final RequestValidationHandler&lt;HeadersAndParams, MartRequest&gt; validator;

    private static final String REQUEST_TYPE_VALUE = &quot;tscache&quot;;

    @Autowired
    public TimeSeriesController(@Qualifier(&quot;tsOldRspGateway&quot;) MartGateway&lt;TSResponse, MartRequest&gt; tsOldRspGateway,
            @Qualifier(&quot;tsNewRspGateway&quot;) MartGateway&lt;InvestmentResponse, MartRequest&gt; tsNewRspGateway,
<span class="nc" id="L73">            @Qualifier(&quot;timeSeriesApiValidator&quot;) RequestValidationHandler&lt;HeadersAndParams,MartRequest&gt; validator) {</span>
<span class="nc" id="L74">        this.tsOldRspGateway = tsOldRspGateway;</span>
<span class="nc" id="L75">        this.tsNewRspGateway = tsNewRspGateway;</span>
<span class="nc" id="L76">        this.validator = validator;</span>
<span class="nc" id="L77">    }</span>


    private void validateRequest(MartRequest martRequest, HeadersAndParams headersAndParams) {
<span class="nc" id="L81">        validator.validateHeadersAndParams(headersAndParams);</span>
<span class="nc" id="L82">        validator.validateRequestBody(martRequest);</span>
<span class="nc" id="L83">    }</span>

    @GetMapping(value = &quot;&quot;)
    public Mono&lt;InvestmentResponse&gt; getInvestmentTSData(
            @ApiParam(example = &quot;0P00000AWG,0P00000AYI,0P00000AZ0&quot;)
            @PathVariable(&quot;investment-ids&quot;) String investmentIds,
            @ApiParam(example = &quot;119114,119115&quot;)
            @RequestParam(value = &quot;dataPoints&quot;, required = false) String dataPoints, //check will be handled by the validateRequest below
            @ApiParam(example = &quot;2022-10-31&quot;)
            @RequestParam(value = &quot;startDate&quot;, required = false, defaultValue = &quot;&quot;) String startDate,
            @ApiParam(example = &quot;2022-11-30&quot;)
            @RequestParam(value = &quot;endDate&quot;, required = false, defaultValue = &quot;&quot;) String endDate,
            @ApiParam(example = &quot;EUR&quot;)
            @RequestParam(value = &quot;currency&quot;, required = false, defaultValue = &quot;&quot;) String currency,
            @ApiParam(example = &quot;USD&quot;)
            @RequestParam(value = &quot;preCurrency&quot;, required = false, defaultValue = &quot;&quot;) String preCurrency,
            @ApiParam(example = &quot;false&quot;)
            @RequestParam(value = &quot;readCache&quot;, required = false, defaultValue = &quot;&quot;) String readCache,
            @RequestParam(value= &quot;dateFormat&quot;, required = false, defaultValue = &quot;&quot;) String dateFormat,
            @RequestParam(value= &quot;decimalFormat&quot;, required = false, defaultValue = &quot;&quot;) String decimalFormat,
            @RequestParam(value= &quot;extendPerformance&quot;, required = false, defaultValue = &quot;&quot;) String extendPerformance,
            @RequestParam(value= &quot;postTax&quot;, required = false, defaultValue = &quot;&quot;) String postTax,
            @RequestParam(value= &quot;useRequireId&quot;, required = false, defaultValue = &quot;false&quot;) String useRequireId,
            @RequestParam(value= &quot;useCase&quot;, required = false, defaultValue = &quot;feed&quot;) String useCase,
            @RequestParam(value= &quot;useNewCCS&quot;, required = false, defaultValue = &quot;false&quot;) String useNewCCS,
            @RequestHeader(value = &quot;X-Api-ProductId&quot;, required = false, defaultValue = &quot;&quot;) String productId,
            @RequestHeader(value = &quot;X-API-RequestId&quot;, required = false, defaultValue = &quot;&quot;) String requestId,
            @RequestHeader(value = &quot;X-API-TraceId&quot;, required = false, defaultValue = &quot;&quot;) String traceId,
            @RequestHeader(value = &quot;X-API-UserId&quot;, required = false, defaultValue = &quot;&quot;) String headerUserId,
            @RequestHeader(value = &quot;Authorization&quot;, required = false, defaultValue = &quot;&quot;) String token,
            @RequestHeader(value= &quot;checkEntitlement&quot;, required = false, defaultValue = &quot;true&quot;) String checkEntitlement,
            @RequestHeader(value= &quot;X-API-Entitlement-Product&quot;, required = false, defaultValue = &quot;&quot;) String entitlementProductId,
            ServerHttpRequest request
    ) {
<span class="nc bnc" id="L117" title="All 2 branches missed.">        String reqId = StringUtils.isNotEmpty(requestId) ? requestId : UUID.randomUUID().toString();</span>
<span class="nc" id="L118">        long startTime = System.currentTimeMillis();</span>

<span class="nc" id="L120">        String userId = getUserIdFromToken(headerUserId, token);</span>
<span class="nc" id="L121">        String configId = getConfigIdFromToken(token);</span>

<span class="nc bnc" id="L123" title="All 2 branches missed.">        List&lt;String&gt; dps = (StringUtils.isNotEmpty(dataPoints)) ? new ArrayList&lt;&gt;(Arrays.asList(dataPoints.split(&quot;,&quot;))) : Collections.emptyList();</span>
<span class="nc" id="L124">        MartRequest martRequest = MartRequest.builder()</span>
<span class="nc" id="L125">                .currency(currency)</span>
<span class="nc" id="L126">                .dps(dps)</span>
<span class="nc" id="L127">                .ids(new ArrayList&lt;&gt;(Arrays.asList(investmentIds.split(&quot;,&quot;))))</span>
<span class="nc" id="L128">                .startDate(startDate)</span>
<span class="nc" id="L129">                .endDate(endDate)</span>
<span class="nc" id="L130">                .preCurrency(preCurrency)</span>
<span class="nc" id="L131">                .readCache(readCache)</span>
<span class="nc" id="L132">                .productId(productId)</span>
<span class="nc" id="L133">                .entitlementProductId(entitlementProductId)</span>
<span class="nc" id="L134">                .requestId(requestId)</span>
<span class="nc" id="L135">                .userId(userId)</span>
<span class="nc" id="L136">                .dateFormat(dateFormat)</span>
<span class="nc" id="L137">                .decimalFormat(decimalFormat)</span>
<span class="nc" id="L138">                .extendedPerformance(extendPerformance)</span>
<span class="nc" id="L139">                .postTax(postTax)</span>
<span class="nc" id="L140">                .useRequireId(Boolean.parseBoolean(useRequireId))</span>
<span class="nc" id="L141">                .checkEntitlement(Boolean.parseBoolean(checkEntitlement))</span>
<span class="nc" id="L142">                .useCase(useCase)</span>
<span class="nc" id="L143">                .useNewCCS(Boolean.parseBoolean(useNewCCS))</span>
<span class="nc" id="L144">                .configId(configId)</span>
<span class="nc" id="L145">                .build();</span>

<span class="nc" id="L147">        HeadersAndParams headersAndParams = HeadersAndParams.builder()</span>
<span class="nc" id="L148">                .authorizationToken(token)</span>
<span class="nc" id="L149">                .productId(productId)</span>
<span class="nc" id="L150">                .requestId(requestId)</span>
<span class="nc" id="L151">                .build();</span>
        try {
<span class="nc" id="L153">            validateRequest(martRequest, headersAndParams);</span>
<span class="nc" id="L154">        } catch (ValidationException e) {</span>
<span class="nc" id="L155">            throw new TsGridViewValidationException(e.getStatus());</span>
<span class="nc" id="L156">        }</span>

<span class="nc" id="L158">        return tsNewRspGateway.asyncRetrieveSecurities(martRequest)</span>
<span class="nc" id="L159">                .doOnEach(LogHelper.logOnNext(list -&gt; {</span>
<span class="nc" id="L160">                    MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), reqId);</span>
<span class="nc" id="L161">                    long executionTime = System.currentTimeMillis() - startTime;</span>
<span class="nc" id="L162">                    List&lt;LogEntity&gt; logEntities = Stream.of(</span>
                            new LogEntity(EVENT_TYPE, RESPONSE),
                            new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                            new LogEntity(PRODUCT_ID, productId),
                            new LogEntity(USER_ID, userId),
<span class="nc" id="L167">                            new LogEntity(REQUEST_PARAM, martRequest.getRequestParam(request.getURI().getPath(), request.getURI().getQuery())),</span>
<span class="nc" id="L168">                            new LogEntity(EXECUTE_TIME, executionTime),</span>
                            new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE)
<span class="nc" id="L170">                    ).collect(Collectors.toCollection(ArrayList::new));</span>
<span class="nc" id="L171">                    addRequestPayload(martRequest, executionTime, logEntities);</span>
<span class="nc" id="L172">                    LogEntry.info(logEntities.toArray(LogEntity[]::new));</span>
<span class="nc" id="L173">                })).doOnEach(LogHelper.logOnError(e -&gt; {</span>
<span class="nc" id="L174">                    MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), reqId);</span>
<span class="nc" id="L175">                    LogEntry.error(</span>
                            new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                            new LogEntity(EVENT_DESCRIPTION, e),
                            new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
<span class="nc" id="L179">                            new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(martRequest)),</span>
<span class="nc" id="L180">                            new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime),</span>
<span class="nc" id="L181">                            new LogEntity(EXCEPTION_TYPE, e.getClass()),</span>
                            new LogEntity(PRODUCT_ID, productId),
                            new LogEntity(USER_ID, userId),
<span class="nc" id="L184">                            new LogEntity(REQUEST_PARAM, martRequest.getRequestParam(request.getURI().getPath(), request.getURI().getQuery()))</span>
                    );
<span class="nc" id="L186">                }));</span>
    }
    @GetMapping(value = &quot;&quot;, params = &quot;format=1&quot;, produces = &quot;application/json&quot;)
    public Mono&lt;TSResponse&gt; retrieveTimeSeriesData(
            @ApiParam(example = &quot;0P00000AWG,0P00000AYI,0P00000AZ0&quot;)
            @PathVariable(&quot;investment-ids&quot;) String investmentIds,
            @ApiParam(example = &quot;119114,119115&quot;)
            @RequestParam(value = &quot;dataPoints&quot;, required = false) String dataPoints, //check will be handled by the validateRequest below
            @ApiParam(example = &quot;2022-10-31&quot;)
            @RequestParam(value = &quot;startDate&quot;, required = false, defaultValue = &quot;&quot;) String startDate,
            @ApiParam(example = &quot;2022-11-30&quot;)
            @RequestParam(value = &quot;endDate&quot;, required = false, defaultValue = &quot;&quot;) String endDate,
            @ApiParam(example = &quot;EUR&quot;)
            @RequestParam(value = &quot;currency&quot;, required = false, defaultValue = &quot;&quot;) String currency,
            @ApiParam(example = &quot;USD&quot;)
            @RequestParam(value = &quot;preCurrency&quot;, required = false, defaultValue = &quot;&quot;) String preCurrency,
            @ApiParam(example = &quot;false&quot;)
            @RequestParam(value = &quot;readCache&quot;, required = false, defaultValue = &quot;&quot;) String readCache,
            @RequestParam(value= &quot;dateFormat&quot;, required = false, defaultValue = &quot;&quot;) String dateFormat,
            @RequestParam(value= &quot;decimalFormat&quot;, required = false, defaultValue = &quot;&quot;) String decimalFormat,
            @RequestParam(value= &quot;extendPerformance&quot;, required = false, defaultValue = &quot;&quot;) String extendPerformance,
            @RequestParam(value= &quot;postTax&quot;, required = false, defaultValue = &quot;&quot;) String postTax,
            @RequestParam(value= &quot;useRequireId&quot;, required = false, defaultValue = &quot;false&quot;) String useRequireId,
            @RequestParam(value= &quot;useCase&quot;, required = false, defaultValue = &quot;feed&quot;) String useCase,
            @RequestParam(value= &quot;useNewCCS&quot;, required = false, defaultValue = &quot;false&quot;) String useNewCCS,
            @RequestHeader(value = &quot;X-Api-ProductId&quot;, required = false, defaultValue = &quot;&quot;) String productId,
            @RequestHeader(value = &quot;X-API-RequestId&quot;, required = false, defaultValue = &quot;&quot;) String requestId,
            @RequestHeader(value = &quot;X-API-TraceId&quot;, required = false, defaultValue = &quot;&quot;) String traceId,
            @RequestHeader(value = &quot;X-API-UserId&quot;, required = false, defaultValue = &quot;&quot;) String headerUserId,
            @RequestHeader(value = &quot;Authorization&quot;, required = false, defaultValue = &quot;&quot;) String token,
            @RequestHeader(value= &quot;checkEntitlement&quot;, required = false, defaultValue = &quot;true&quot;) String checkEntitlement,
            @RequestHeader(value= &quot;X-API-Entitlement-Product&quot;, required = false, defaultValue = &quot;&quot;) String entitlementProductId,
            ServerHttpRequest request
    ) {
<span class="nc bnc" id="L220" title="All 2 branches missed.">        String reqId = StringUtils.isNotEmpty(requestId) ? requestId : UUID.randomUUID().toString();</span>
<span class="nc" id="L221">        long startTime = System.currentTimeMillis();</span>

<span class="nc" id="L223">        String userId = getUserIdFromToken(headerUserId, token);</span>
<span class="nc" id="L224">        String configId = getConfigIdFromToken(token);</span>

<span class="nc bnc" id="L226" title="All 2 branches missed.">        List&lt;String&gt; dps = (StringUtils.isNotEmpty(dataPoints)) ? new ArrayList&lt;&gt;(Arrays.asList(dataPoints.split(&quot;,&quot;))) : Collections.emptyList();</span>
<span class="nc" id="L227">        MartRequest martRequest = MartRequest.builder()</span>
<span class="nc" id="L228">                .currency(currency)</span>
<span class="nc" id="L229">                .dps(dps)</span>
<span class="nc" id="L230">                .ids(new ArrayList&lt;&gt;(Arrays.asList(investmentIds.split(&quot;,&quot;))))</span>
<span class="nc" id="L231">                .startDate(startDate)</span>
<span class="nc" id="L232">                .endDate(endDate)</span>
<span class="nc" id="L233">                .preCurrency(preCurrency)</span>
<span class="nc" id="L234">                .readCache(readCache)</span>
<span class="nc" id="L235">                .productId(productId)</span>
<span class="nc" id="L236">                .entitlementProductId(entitlementProductId)</span>
<span class="nc" id="L237">                .requestId(reqId)</span>
<span class="nc" id="L238">                .userId(userId)</span>
<span class="nc" id="L239">                .dateFormat(dateFormat)</span>
<span class="nc" id="L240">                .decimalFormat(decimalFormat)</span>
<span class="nc" id="L241">                .extendedPerformance(extendPerformance)</span>
<span class="nc" id="L242">                .postTax(postTax)</span>
<span class="nc" id="L243">                .useRequireId(Boolean.parseBoolean(useRequireId))</span>
<span class="nc" id="L244">                .checkEntitlement(Boolean.parseBoolean(checkEntitlement))</span>
<span class="nc" id="L245">                .useCase(useCase)</span>
<span class="nc" id="L246">                .useNewCCS(Boolean.parseBoolean(useNewCCS))</span>
<span class="nc" id="L247">                .configId(configId)</span>
<span class="nc" id="L248">                .build();</span>

<span class="nc" id="L250">        HeadersAndParams headersAndParams = HeadersAndParams.builder()</span>
<span class="nc" id="L251">                .authorizationToken(token)</span>
<span class="nc" id="L252">                .productId(productId)</span>
<span class="nc" id="L253">                .requestId(requestId)</span>
<span class="nc" id="L254">                .build();</span>
        try {
<span class="nc" id="L256">            validateRequest(martRequest, headersAndParams);</span>
<span class="nc" id="L257">        } catch (ValidationException e) {</span>
<span class="nc" id="L258">            throw new TsCacheApiValidationException(e.getStatus());</span>
<span class="nc" id="L259">        }</span>

<span class="nc" id="L261">        return tsOldRspGateway.asyncRetrieveSecurities(martRequest)</span>
<span class="nc" id="L262">                .doOnEach(LogHelper.logOnNext(list -&gt; {</span>
<span class="nc" id="L263">                    MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), reqId);</span>
<span class="nc" id="L264">                    long executionTime = System.currentTimeMillis() - startTime;</span>
<span class="nc" id="L265">                    List&lt;LogEntity&gt; logEntities = Stream.of(</span>
                            new LogEntity(EVENT_TYPE, RESPONSE),
                            new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                            new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
                            new LogEntity(PRODUCT_ID, productId),
                            new LogEntity(USER_ID, userId),
<span class="nc" id="L271">                            new LogEntity(REQUEST_PARAM, martRequest.getRequestParam(request.getURI().getPath(), request.getURI().getQuery())),</span>
<span class="nc" id="L272">                            new LogEntity(EXECUTE_TIME, executionTime)</span>
<span class="nc" id="L273">                    ).collect(Collectors.toCollection(ArrayList::new));</span>
<span class="nc" id="L274">                    addRequestPayload(martRequest, executionTime, logEntities);</span>
<span class="nc" id="L275">                    LogEntry.info(logEntities.toArray(LogEntity[]::new));</span>
<span class="nc" id="L276">                })).doOnEach(LogHelper.logOnError(e -&gt; {</span>
<span class="nc" id="L277">                    MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), reqId);</span>
<span class="nc" id="L278">                    LogEntry.error(</span>
                            new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                            new LogEntity(EVENT_DESCRIPTION, e),
                            new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
<span class="nc" id="L282">                            new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(martRequest)),</span>
<span class="nc" id="L283">                            new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime),</span>
<span class="nc" id="L284">                            new LogEntity(EXCEPTION_TYPE, e.getClass()),</span>
                            new LogEntity(PRODUCT_ID, productId),
                            new LogEntity(USER_ID, userId),
<span class="nc" id="L287">                            new LogEntity(REQUEST_PARAM, martRequest.getRequestParam(request.getURI().getPath(), request.getURI().getQuery()))</span>
                    );
<span class="nc" id="L289">                }));</span>
    }

    private static String getUserIdFromToken(String headerUserId, String token) {
<span class="nc" id="L293">       return StringUtils.defaultIfEmpty(JwtUtil.getFieldValue(token, &quot;https://morningstar.com/mstar_id&quot;), headerUserId);</span>
    }

    private static String getConfigIdFromToken(String token) {
<span class="nc" id="L297">        return JwtUtil.getFieldValue(token, &quot;https://morningstar.com/config_id&quot;);</span>
    }

    @GetMapping(value = &quot;&quot;, params = &quot;format=0&quot;, produces = &quot;application/x-protobuf&quot;)
    public Mono&lt;TsCacheDataForProtoBuf.TimeSeriesDatas&gt; retrieveTimeSeriesDataAsProtobuf(
            @ApiParam(example = &quot;0P00000AWG,0P00000AYI,0P00000AZ0&quot;)
            @PathVariable(&quot;investment-ids&quot;) String investmentIds,
            @ApiParam(example = &quot;119114,119115&quot;)
            @RequestParam(value = &quot;dataPoints&quot;, required = false) String dataPoints, //check will be handled by the validateRequest below
            @ApiParam(example = &quot;2022-10-31&quot;)
            @RequestParam(value = &quot;startDate&quot;, required = false, defaultValue = &quot;&quot;) String startDate,
            @ApiParam(example = &quot;2022-11-30&quot;)
            @RequestParam(value = &quot;endDate&quot;, required = false, defaultValue = &quot;&quot;) String endDate,
            @ApiParam(example = &quot;EUR&quot;)
            @RequestParam(value = &quot;currency&quot;, required = false, defaultValue = &quot;&quot;) String currency,
            @ApiParam(example = &quot;USD&quot;)
            @RequestParam(value = &quot;preCurrency&quot;, required = false, defaultValue = &quot;&quot;) String preCurrency,
            @ApiParam(example = &quot;false&quot;)
            @RequestParam(value = &quot;readCache&quot;, required = false, defaultValue = &quot;&quot;) String readCache,
            @RequestParam(value= &quot;dateFormat&quot;, required = false, defaultValue = &quot;&quot;) String dateFormat,
            @RequestParam(value= &quot;decimalFormat&quot;, required = false, defaultValue = &quot;&quot;) String decimalFormat,
            @RequestParam(value= &quot;extendPerformance&quot;, required = false, defaultValue = &quot;&quot;) String extendPerformance,
            @RequestParam(value= &quot;postTax&quot;, required = false, defaultValue = &quot;&quot;) String postTax,
            @RequestParam(value= &quot;useRequireId&quot;, required = false, defaultValue = &quot;false&quot;) String useRequireId,
            @RequestParam(value= &quot;useCase&quot;, required = false, defaultValue = &quot;feed&quot;) String useCase,
            @RequestParam(value= &quot;useNewCCS&quot;, required = false, defaultValue = &quot;false&quot;) String useNewCCS,
            @RequestHeader(value = &quot;X-Api-ProductId&quot;, required = false, defaultValue = &quot;&quot;) String productId,
            @RequestHeader(value = &quot;X-API-RequestId&quot;, required = false, defaultValue = &quot;&quot;) String requestId,
            @RequestHeader(value = &quot;X-API-TraceId&quot;, required = false, defaultValue = &quot;&quot;) String traceId,
            @RequestHeader(value = &quot;X-API-UserId&quot;, required = false, defaultValue = &quot;&quot;) String headerUserId,
            @RequestHeader(value = &quot;Authorization&quot;, required = false, defaultValue = &quot;&quot;) String token,
            @RequestHeader(value= &quot;checkEntitlement&quot;, required = false, defaultValue = &quot;true&quot;) String checkEntitlement,
            @RequestHeader(value= &quot;X-API-Entitlement-Product&quot;, required = false, defaultValue = &quot;&quot;) String entitlementProductId,
            ServerHttpRequest request
    ) {
<span class="nc bnc" id="L332" title="All 2 branches missed.">        String reqId = StringUtils.isNotEmpty(requestId) ? requestId : UUID.randomUUID().toString();</span>
<span class="nc" id="L333">        long startTime = System.currentTimeMillis();</span>

<span class="nc" id="L335">        String userId = getUserIdFromToken(headerUserId, token);</span>
<span class="nc" id="L336">        String configId = getConfigIdFromToken(token);</span>

<span class="nc bnc" id="L338" title="All 2 branches missed.">        List&lt;String&gt; dps = (StringUtils.isNotEmpty(dataPoints)) ? new ArrayList&lt;&gt;(Arrays.asList(dataPoints.split(&quot;,&quot;))) : Collections.emptyList();</span>
<span class="nc" id="L339">        MartRequest martRequest = MartRequest.builder()</span>
<span class="nc" id="L340">                .currency(currency)</span>
<span class="nc" id="L341">                .dps(dps)</span>
<span class="nc" id="L342">                .ids(new ArrayList&lt;&gt;(Arrays.asList(investmentIds.split(&quot;,&quot;))))</span>
<span class="nc" id="L343">                .startDate(startDate)</span>
<span class="nc" id="L344">                .endDate(endDate)</span>
<span class="nc" id="L345">                .preCurrency(preCurrency)</span>
<span class="nc" id="L346">                .readCache(readCache)</span>
<span class="nc" id="L347">                .productId(productId)</span>
<span class="nc" id="L348">                .entitlementProductId(entitlementProductId)</span>
<span class="nc" id="L349">                .requestId(reqId)</span>
<span class="nc" id="L350">                .userId(userId)</span>
<span class="nc" id="L351">                .dateFormat(dateFormat)</span>
<span class="nc" id="L352">                .decimalFormat(decimalFormat)</span>
<span class="nc" id="L353">                .extendedPerformance(extendPerformance)</span>
<span class="nc" id="L354">                .postTax(postTax)</span>
<span class="nc" id="L355">                .useRequireId(&quot;true&quot;.equalsIgnoreCase(useRequireId))</span>
<span class="nc" id="L356">                .checkEntitlement(Boolean.parseBoolean(checkEntitlement))</span>
<span class="nc" id="L357">                .useCase(useCase)</span>
<span class="nc" id="L358">                .useNewCCS(Boolean.parseBoolean(useNewCCS))</span>
<span class="nc" id="L359">                .configId(configId)</span>
<span class="nc" id="L360">                .build();</span>

<span class="nc" id="L362">        HeadersAndParams headersAndParams = HeadersAndParams.builder()</span>
<span class="nc" id="L363">                .authorizationToken(token)</span>
<span class="nc" id="L364">                .productId(productId)</span>
<span class="nc" id="L365">                .requestId(requestId)</span>
<span class="nc" id="L366">                .build();</span>

        try {
<span class="nc" id="L369">            validateRequest(martRequest, headersAndParams);</span>
<span class="nc" id="L370">        } catch (ValidationException e) {</span>
<span class="nc" id="L371">            throw new TsCacheProtobufValidationException(e.getStatus());</span>
<span class="nc" id="L372">        }</span>

<span class="nc" id="L374">        return tsOldRspGateway.asyncRetrieveSecurities(martRequest).map(TSResponse::toProtobuf)</span>
<span class="nc" id="L375">                .doOnEach(LogHelper.logOnNext(list -&gt; {</span>
<span class="nc" id="L376">                    MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), reqId);</span>
<span class="nc" id="L377">                    long executionTime = System.currentTimeMillis() - startTime;</span>
<span class="nc" id="L378">                    List&lt;LogEntity&gt; logEntities = Stream.of(</span>
                            new LogEntity(EVENT_TYPE, RESPONSE),
                            new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                            new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
                            new LogEntity(PRODUCT_ID, productId),
                            new LogEntity(USER_ID, userId),
<span class="nc" id="L384">                            new LogEntity(REQUEST_PARAM, martRequest.getRequestParam(request.getURI().getPath(), request.getURI().getQuery())),</span>
<span class="nc" id="L385">                            new LogEntity(EXECUTE_TIME, executionTime),</span>
                            new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE)
<span class="nc" id="L387">                    ).collect(Collectors.toCollection(ArrayList::new));</span>
<span class="nc" id="L388">                    addRequestPayload(martRequest, executionTime, logEntities);</span>
<span class="nc" id="L389">                    LogEntry.info(logEntities.toArray(LogEntity[]::new));</span>
<span class="nc" id="L390">                })).doOnEach(LogHelper.logOnError(e -&gt; {</span>
<span class="nc" id="L391">                    MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), reqId);</span>
<span class="nc" id="L392">                    LogEntry.error(</span>
                            new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                            new LogEntity(EVENT_DESCRIPTION, e),
                            new LogEntity(REQUEST_TYPE, REQUEST_TYPE_VALUE),
<span class="nc" id="L396">                            new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(martRequest)),</span>
<span class="nc" id="L397">                            new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime),</span>
<span class="nc" id="L398">                            new LogEntity(EXCEPTION_TYPE, e.getClass()),</span>
                            new LogEntity(PRODUCT_ID, productId),
                            new LogEntity(USER_ID, userId),
<span class="nc" id="L401">                            new LogEntity(REQUEST_PARAM, martRequest.getRequestParam(request.getURI().getPath(), request.getURI().getQuery()))</span>
                    );
<span class="nc" id="L403">                }));</span>
    }

    private void addRequestPayload(MartRequest martRequest, long executionTime, List&lt;LogEntity&gt; logEntities) {
<span class="nc bnc" id="L407" title="All 2 branches missed.">        if (executionTime &gt; LOG_REQUEST_PAYLOAD_LIMIT_MILLISECONDS) {</span>
<span class="nc" id="L408">            logEntities.add(new LogEntity(REQUEST_PAYLOAD, JsonUtils.toJsonString(martRequest)));</span>
        }
<span class="nc" id="L410">    }</span>

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>