// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: timeseries_service.proto

// Protobuf Java Version: 3.25.1
package com.morningstar.martapi.grpc;

public final class TimeSeriesServiceProto {
  private TimeSeriesServiceProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_timeseries_TimeSeriesRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_timeseries_TimeSeriesRequest_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\030timeseries_service.proto\022\ntimeseries\032\021" +
      "TsCacheData.proto\"\331\003\n\021TimeSeriesRequest\022" +
      "\026\n\016investment_ids\030\001 \003(\t\022\023\n\013data_points\030\002" +
      " \003(\t\022\022\n\nstart_date\030\003 \001(\t\022\020\n\010end_date\030\004 \001" +
      "(\t\022\020\n\010currency\030\005 \001(\t\022\024\n\014pre_currency\030\006 \001" +
      "(\t\022\022\n\nread_cache\030\007 \001(\t\022\023\n\013date_format\030\010 " +
      "\001(\t\022\026\n\016decimal_format\030\t \001(\t\022\032\n\022extend_pe" +
      "rformance\030\n \001(\t\022\020\n\010post_tax\030\013 \001(\t\022\026\n\016use" +
      "_require_id\030\014 \001(\010\022\020\n\010use_case\030\r \001(\t\022\023\n\013u" +
      "se_new_ccs\030\016 \001(\010\022\022\n\nproduct_id\030\017 \001(\t\022\022\n\n" +
      "request_id\030\020 \001(\t\022\020\n\010trace_id\030\021 \001(\t\022\017\n\007us" +
      "er_id\030\022 \001(\t\022\025\n\rauthorization\030\023 \001(\t\022\031\n\021ch" +
      "eck_entitlement\030\024 \001(\010\022\036\n\026entitlement_pro" +
      "duct_id\030\025 \001(\t2b\n\021TimeSeriesService\022M\n\021Ge" +
      "tTimeSeriesData\022\035.timeseries.TimeSeriesR" +
      "equest\032\031.protobuf.TimeSeriesDatasB8\n\034com" +
      ".morningstar.martapi.grpcB\026TimeSeriesSer" +
      "viceProtoP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.getDescriptor(),
        });
    internal_static_timeseries_TimeSeriesRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_timeseries_TimeSeriesRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_timeseries_TimeSeriesRequest_descriptor,
        new java.lang.String[] { "InvestmentIds", "DataPoints", "StartDate", "EndDate", "Currency", "PreCurrency", "ReadCache", "DateFormat", "DecimalFormat", "ExtendPerformance", "PostTax", "UseRequireId", "UseCase", "UseNewCcs", "ProductId", "RequestId", "TraceId", "UserId", "Authorization", "CheckEntitlement", "EntitlementProductId", });
    com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
