<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>GridViewExceptionHandler.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <a href="index.source.html" class="el_package">com.morningstar.martapi.exception</a> &gt; <span class="el_source">GridViewExceptionHandler.java</span></div><h1>GridViewExceptionHandler.java</h1><pre class="source lang-java linenums">package com.morningstar.martapi.exception;

import com.morningstar.martapi.controller.AsyncApiController;
import com.morningstar.martapi.controller.DocumentController;
import com.morningstar.martapi.controller.IndexDataController;
import com.morningstar.martapi.controller.InvestmentController;
import com.morningstar.martapi.controller.SecurityController;
import com.morningstar.martapi.validator.entity.HeadersAndParams;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.martgateway.domains.entitlement.exceptions.EntitlementException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.core.codec.DecodingException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.ServerWebInputException;

import static com.morningstar.martapi.exception.utils.ExceptionHandlerUtils.extractProductId;
import static com.morningstar.martapi.exception.utils.ExceptionHandlerUtils.extractStatusCode;

@Order(Ordered.HIGHEST_PRECEDENCE)
@RestControllerAdvice(assignableTypes = {InvestmentController.class, AsyncApiController.class, DocumentController.class, IndexDataController.class, SecurityController.class})
<span class="nc" id="L30">public class GridViewExceptionHandler {</span>

<span class="nc" id="L32">    private static final Logger LOGGER = LoggerFactory.getLogger(GridViewExceptionHandler.class);</span>

    @ExceptionHandler(value = {DecodingException.class, ServerWebInputException.class})
    public ResponseEntity&lt;InvestmentResponse&gt; handleInvalidRequestInput(
            Exception e,
            ServerWebExchange ex) {
<span class="nc" id="L38">        String url = ex.getRequest().getURI().toString();</span>
<span class="nc" id="L39">        String productId = extractProductId(ex);</span>
<span class="nc" id="L40">        LOGGER.warn(&quot;event_type=\&quot;Invalid Request Input\&quot;, &quot; +</span>
                &quot;event_description=\&quot;Request Input not valid JSON\&quot;, &quot; +
<span class="nc" id="L42">                &quot;url=\&quot;{}\&quot;, product_id=\&quot;{}\&quot;, e=\&quot;{}\&quot;&quot;, url, productId, e.getMessage());</span>
<span class="nc" id="L43">        return ResponseEntity.badRequest().body(new InvestmentResponse(Status.INVALID_REQUEST_INPUT, null));</span>
    }

    @ExceptionHandler(value = InvestmentApiValidationException.class)
    public ResponseEntity&lt;InvestmentResponse&gt; handleValidationException(
            InvestmentApiValidationException e,
            ServerWebExchange ex) {
<span class="nc" id="L50">        String url = ex.getRequest().getURI().toString();</span>
<span class="nc" id="L51">        String productId = extractProductId(ex);</span>
<span class="nc" id="L52">        LOGGER.warn(&quot;event_type=\&quot;Investment API Validation\&quot;, &quot; +</span>
                &quot;event_description=\&quot;Validation failure\&quot;, &quot; +
<span class="nc" id="L54">                &quot;url=\&quot;{}\&quot;, product_id=\&quot;{}\&quot;, e=\&quot;{}\&quot;&quot;, url, productId, e.getMessage());</span>
<span class="nc" id="L55">        return ResponseEntity</span>
<span class="nc" id="L56">                .status(extractStatusCode(e.getStatus()))</span>
<span class="nc" id="L57">                .body(new InvestmentResponse(e.getStatus(), null));</span>
    }

    @ExceptionHandler(value = EntitlementException.class)
    public ResponseEntity&lt;InvestmentResponse&gt; handleEntitlementException(EntitlementException e, ServerWebExchange ex) {
<span class="nc" id="L62">        String url = ex.getRequest().getURI().toString();</span>
<span class="nc" id="L63">        String productId = extractProductId(ex);</span>
<span class="nc" id="L64">        MDC.put(&quot;request_id&quot;,e.getRequestId());</span>
<span class="nc" id="L65">        LOGGER.warn(&quot;event_type=\&quot;Investment API Authorization Failure\&quot;, &quot; +</span>
                &quot;event_description=\&quot;Entitlement retrieval failed\&quot;, &quot; +
<span class="nc" id="L67">                &quot;url=\&quot;{}\&quot;, product_id=\&quot;{}\&quot;, e=\&quot;{}\&quot;&quot;, url, productId, e.getMessage());</span>
<span class="nc" id="L68">        MDC.clear();</span>
<span class="nc" id="L69">        return ResponseEntity</span>
<span class="nc" id="L70">                .status(extractStatusCode(e.getStatus()))</span>
<span class="nc" id="L71">                .body(new InvestmentResponse(e.getStatus(), null));</span>
    }

    @ExceptionHandler(value = IllegalArgumentException.class)
    public ResponseEntity&lt;InvestmentResponse&gt; handleBadRequestException(Exception e,
                                                                        ServerWebExchange ex) {
<span class="nc" id="L77">        String url = ex.getRequest().getURI().toString();</span>
<span class="nc" id="L78">        LOGGER.warn(&quot;event_type=\&quot;BadRequest Exception\&quot;, &quot; +</span>
                &quot;event_description=\&quot;Unexpected issue encountered\&quot;, &quot; +
<span class="nc" id="L80">                &quot;url=\&quot;{}\&quot;, e=\&quot;{}\&quot;&quot;, url, e.getMessage());</span>
<span class="nc" id="L81">        return ResponseEntity.badRequest()</span>
<span class="nc" id="L82">                .body(badRequestError(e.getMessage()));</span>
    }

    @ExceptionHandler(value = {TranscriptApiException.class})
    public ResponseEntity&lt;InvestmentResponse&gt; handleNotFoundException(TranscriptApiException e,
                                                                      ServerWebExchange ex)  {
<span class="nc" id="L88">        String url = ex.getRequest().getURI().toString();</span>
<span class="nc" id="L89">        HeadersAndParams headersAndParams = e.getHeadersAndParams();</span>
<span class="nc" id="L90">        String userID = e.getUserId();</span>
<span class="nc" id="L91">        MDC.put(&quot;request_id&quot;,headersAndParams.getRequestId());</span>
<span class="nc" id="L92">        MDC.clear();</span>
<span class="nc" id="L93">        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(notFoundError(e.getMessage() + &quot; - &quot; + e.getCause()));</span>
    }

    @ExceptionHandler(value = Exception.class)
    public ResponseEntity&lt;InvestmentResponse&gt; handleOtherException(Exception e,
                                                                   ServerWebExchange ex) {
<span class="nc" id="L99">        String url = ex.getRequest().getURI().toString();</span>
<span class="nc" id="L100">        LOGGER.warn(&quot;event_type=\&quot;Unexpected Exception\&quot;, &quot; +</span>
                &quot;event_description=\&quot;Unexpected issue encountered\&quot;, &quot; +
<span class="nc" id="L102">                &quot;url=\&quot;{}\&quot;, e=\&quot;{}\&quot;&quot;, url, e.getMessage());</span>
<span class="nc" id="L103">        return ResponseEntity.internalServerError()</span>
<span class="nc" id="L104">                .body(internalServerError(e.getMessage()));</span>
    }

    @ExceptionHandler(value = {IndexAPIException.class})
    public ResponseEntity&lt;InvestmentResponse&gt; handleIndexAPIException(IndexAPIException e,
                                                                      ServerWebExchange ex) {
<span class="nc" id="L110">        String url = ex.getRequest().getURI().toString();</span>
<span class="nc" id="L111">        LOGGER.warn(&quot;event_type=\&quot;RESPONSE_ERROR\&quot;,request_type=\&quot;index\&quot;, &quot; +</span>
                &quot;event_description=\&quot;Excetpion in Index API\&quot;, &quot; +
<span class="nc" id="L113">                &quot;url=\&quot;{}\&quot;, e=\&quot;{}\&quot;&quot;, url, e.getMessage());</span>
<span class="nc" id="L114">        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(internalServerError(&quot;Internal Error&quot;));</span>
    }


    public InvestmentResponse internalServerError(String message) {
<span class="nc" id="L119">        Status status = Status.INTERNAL_ERROR.withMessage(message);</span>
<span class="nc" id="L120">        return new InvestmentResponse(status, null);</span>
    }

    public InvestmentResponse badRequestError(String message) {
<span class="nc" id="L124">        Status status = Status.BAD_REQUEST.withMessage(message);</span>
<span class="nc" id="L125">        return new InvestmentResponse(status, null);</span>
    }

    public InvestmentResponse notFoundError(String message) {
<span class="nc" id="L129">        Status status = Status.NOT_FOUND.withMessage(message);</span>
<span class="nc" id="L130">        return new InvestmentResponse(status, null);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>