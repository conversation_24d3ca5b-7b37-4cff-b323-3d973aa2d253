<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.morningstar.martapi.grpc</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <span class="el_package">com.morningstar.martapi.grpc</span></div><h1>com.morningstar.martapi.grpc</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">5,635 of 5,635</td><td class="ctr2">0%</td><td class="bar">485 of 485</td><td class="ctr2">0%</td><td class="ctr1">505</td><td class="ctr2">505</td><td class="ctr1">1,550</td><td class="ctr2">1,550</td><td class="ctr1">252</td><td class="ctr2">252</td><td class="ctr1">17</td><td class="ctr2">17</td></tr></tfoot><tbody><tr><td id="a1"><a href="TimeSeriesRequest.java.html" class="el_source">TimeSeriesRequest.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="4,946" alt="4,946"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="479" alt="479"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">458</td><td class="ctr2" id="g0">458</td><td class="ctr1" id="h0">1,395</td><td class="ctr2" id="i0">1,395</td><td class="ctr1" id="j0">208</td><td class="ctr2" id="k0">208</td><td class="ctr1" id="l1">3</td><td class="ctr2" id="m1">3</td></tr><tr><td id="a0"><a href="TimeSeriesGrpcService.java.html" class="el_source">TimeSeriesGrpcService.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="413" alt="413"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f2">15</td><td class="ctr2" id="g2">15</td><td class="ctr1" id="h1">98</td><td class="ctr2" id="i1">98</td><td class="ctr1" id="j2">13</td><td class="ctr2" id="k2">13</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a2"><a href="TimeSeriesServiceGrpc.java.html" class="el_source">TimeSeriesServiceGrpc.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="154" alt="154"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">28</td><td class="ctr2" id="g1">28</td><td class="ctr1" id="h2">45</td><td class="ctr2" id="i2">45</td><td class="ctr1" id="j1">27</td><td class="ctr2" id="k1">27</td><td class="ctr1" id="l0">12</td><td class="ctr2" id="m0">12</td></tr><tr><td id="a3"><a href="TimeSeriesServiceProto.java.html" class="el_source">TimeSeriesServiceProto.java</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="122" alt="122"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">4</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h3">12</td><td class="ctr2" id="i3">12</td><td class="ctr1" id="j3">4</td><td class="ctr2" id="k3">4</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>