<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>LicenseAuditController</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <a href="index.html" class="el_package">com.morningstar.martapi.controller</a> &gt; <span class="el_class">LicenseAuditController</span></div><h1>LicenseAuditController</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">262 of 262</td><td class="ctr2">0%</td><td class="bar">6 of 6</td><td class="ctr2">0%</td><td class="ctr1">10</td><td class="ctr2">10</td><td class="ctr1">64</td><td class="ctr2">64</td><td class="ctr1">7</td><td class="ctr2">7</td></tr></tfoot><tbody><tr><td id="a0"><a href="LicenseAuditController.java.html#L61" class="el_method">getAudit(LicenseAuditEntity, String, String, String)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="71" alt="71"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">2</td><td class="ctr2" id="g0">2</td><td class="ctr1" id="h0">15</td><td class="ctr2" id="i0">15</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a1"><a href="LicenseAuditController.java.html#L96" class="el_method">getAuditSummary(LicenseAuditEntity, String, String, String)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="71" alt="71"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">2</td><td class="ctr2" id="g1">2</td><td class="ctr1" id="h1">15</td><td class="ctr2" id="i1">15</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a2"><a href="LicenseAuditController.java.html#L123" class="el_method">getCellInspection(LicenseCellEntity, String, String, String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="118" height="10" title="70" alt="70"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">2</td><td class="ctr2" id="g2">2</td><td class="ctr1" id="h2">15</td><td class="ctr2" id="i2">15</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a6"><a href="LicenseAuditController.java.html#L151" class="el_method">validateLicenseCellRequest(LicenseCellEntity, String, String, String)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="18" alt="18"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">7</td><td class="ctr2" id="i3">7</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a5"><a href="LicenseAuditController.java.html#L144" class="el_method">validateLicenseAuditRequest(LicenseAuditEntity, String, String)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="27" height="10" title="16" alt="16"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">6</td><td class="ctr2" id="i4">6</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a3"><a href="LicenseAuditController.java.html#L41" class="el_method">LicenseAuditController(LicenseAuditService, RequestValidationHandler, RequestValidationHandler)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="12" alt="12"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">5</td><td class="ctr2" id="i5">5</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a4"><a href="LicenseAuditController.java.html#L38" class="el_method">static {...}</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="4" alt="4"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>