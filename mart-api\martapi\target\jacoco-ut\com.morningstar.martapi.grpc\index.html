<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.morningstar.martapi.grpc</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <span class="el_package">com.morningstar.martapi.grpc</span></div><h1>com.morningstar.martapi.grpc</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">5,635 of 5,635</td><td class="ctr2">0%</td><td class="bar">485 of 485</td><td class="ctr2">0%</td><td class="ctr1">505</td><td class="ctr2">505</td><td class="ctr1">1,550</td><td class="ctr2">1,550</td><td class="ctr1">252</td><td class="ctr2">252</td><td class="ctr1">17</td><td class="ctr2">17</td></tr></tfoot><tbody><tr><td id="a2"><a href="TimeSeriesRequest$Builder.html" class="el_class">TimeSeriesRequest.Builder</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="2,913" alt="2,913"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="269" alt="269"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">276</td><td class="ctr2" id="g0">276</td><td class="ctr1" id="h0">857</td><td class="ctr2" id="i0">857</td><td class="ctr1" id="j0">131</td><td class="ctr2" id="k0">131</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a1"><a href="TimeSeriesRequest.html" class="el_class">TimeSeriesRequest</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="82" height="10" title="1,997" alt="1,997"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="93" height="10" title="210" alt="210"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">180</td><td class="ctr2" id="g1">180</td><td class="ctr1" id="h1">527</td><td class="ctr2" id="i1">527</td><td class="ctr1" id="j1">75</td><td class="ctr2" id="k1">75</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a0"><a href="TimeSeriesGrpcService.html" class="el_class">TimeSeriesGrpcService</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="413" alt="413"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">15</td><td class="ctr2" id="g2">15</td><td class="ctr1" id="h2">98</td><td class="ctr2" id="i2">98</td><td class="ctr1" id="j2">13</td><td class="ctr2" id="k2">13</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a16"><a href="TimeSeriesServiceProto.html" class="el_class">TimeSeriesServiceProto</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="122" alt="122"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f3">4</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h3">12</td><td class="ctr2" id="i3">12</td><td class="ctr1" id="j3">4</td><td class="ctr2" id="k3">4</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a3"><a href="TimeSeriesRequest$1.html" class="el_class">TimeSeriesRequest.new AbstractParser() {...}</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="36" alt="36"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f9">2</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h4">12</td><td class="ctr2" id="i4">12</td><td class="ctr1" id="j9">2</td><td class="ctr2" id="k9">2</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a5"><a href="TimeSeriesServiceGrpc$MethodHandlers.html" class="el_class">TimeSeriesServiceGrpc.MethodHandlers</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="31" alt="31"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f4">4</td><td class="ctr2" id="g4">4</td><td class="ctr1" id="h5">11</td><td class="ctr2" id="i5">11</td><td class="ctr1" id="j4">3</td><td class="ctr2" id="k4">3</td><td class="ctr1" id="l5">1</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a15"><a href="TimeSeriesServiceGrpc$TimeSeriesServiceStub.html" class="el_class">TimeSeriesServiceGrpc.TimeSeriesServiceStub</a></td><td class="bar" id="b6"/><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f5">3</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h6">6</td><td class="ctr2" id="i6">6</td><td class="ctr1" id="j5">3</td><td class="ctr2" id="k5">3</td><td class="ctr1" id="l6">1</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a10"><a href="TimeSeriesServiceGrpc$TimeSeriesServiceBlockingStub.html" class="el_class">TimeSeriesServiceGrpc.TimeSeriesServiceBlockingStub</a></td><td class="bar" id="b7"/><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f6">3</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h7">5</td><td class="ctr2" id="i7">5</td><td class="ctr1" id="j6">3</td><td class="ctr2" id="k6">3</td><td class="ctr1" id="l7">1</td><td class="ctr2" id="m7">1</td></tr><tr><td id="a12"><a href="TimeSeriesServiceGrpc$TimeSeriesServiceFutureStub.html" class="el_class">TimeSeriesServiceGrpc.TimeSeriesServiceFutureStub</a></td><td class="bar" id="b8"/><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f7">3</td><td class="ctr2" id="g7">3</td><td class="ctr1" id="h8">5</td><td class="ctr2" id="i8">5</td><td class="ctr1" id="j7">3</td><td class="ctr2" id="k7">3</td><td class="ctr1" id="l8">1</td><td class="ctr2" id="m8">1</td></tr><tr><td id="a14"><a href="TimeSeriesServiceGrpc$TimeSeriesServiceMethodDescriptorSupplier.html" class="el_class">TimeSeriesServiceGrpc.TimeSeriesServiceMethodDescriptorSupplier</a></td><td class="bar" id="b9"/><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f10">2</td><td class="ctr2" id="g10">2</td><td class="ctr1" id="h9">4</td><td class="ctr2" id="i9">4</td><td class="ctr1" id="j10">2</td><td class="ctr2" id="k10">2</td><td class="ctr1" id="l9">1</td><td class="ctr2" id="m9">1</td></tr><tr><td id="a9"><a href="TimeSeriesServiceGrpc$TimeSeriesServiceBaseDescriptorSupplier.html" class="el_class">TimeSeriesServiceGrpc.TimeSeriesServiceBaseDescriptorSupplier</a></td><td class="bar" id="b10"/><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f8">3</td><td class="ctr2" id="g8">3</td><td class="ctr1" id="h10">3</td><td class="ctr2" id="i10">3</td><td class="ctr1" id="j8">3</td><td class="ctr2" id="k8">3</td><td class="ctr1" id="l10">1</td><td class="ctr2" id="m10">1</td></tr><tr><td id="a6"><a href="TimeSeriesServiceGrpc$1.html" class="el_class">TimeSeriesServiceGrpc.new AbstractStub.StubFactory() {...}</a></td><td class="bar" id="b11"/><td class="ctr2" id="c11">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">2</td><td class="ctr2" id="g11">2</td><td class="ctr1" id="h11">2</td><td class="ctr2" id="i11">2</td><td class="ctr1" id="j11">2</td><td class="ctr2" id="k11">2</td><td class="ctr1" id="l11">1</td><td class="ctr2" id="m11">1</td></tr><tr><td id="a7"><a href="TimeSeriesServiceGrpc$2.html" class="el_class">TimeSeriesServiceGrpc.new AbstractStub.StubFactory() {...}</a></td><td class="bar" id="b12"/><td class="ctr2" id="c12">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">2</td><td class="ctr2" id="g12">2</td><td class="ctr1" id="h12">2</td><td class="ctr2" id="i12">2</td><td class="ctr1" id="j12">2</td><td class="ctr2" id="k12">2</td><td class="ctr1" id="l12">1</td><td class="ctr2" id="m12">1</td></tr><tr><td id="a8"><a href="TimeSeriesServiceGrpc$3.html" class="el_class">TimeSeriesServiceGrpc.new AbstractStub.StubFactory() {...}</a></td><td class="bar" id="b13"/><td class="ctr2" id="c13">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">2</td><td class="ctr2" id="g13">2</td><td class="ctr1" id="h13">2</td><td class="ctr2" id="i13">2</td><td class="ctr1" id="j13">2</td><td class="ctr2" id="k13">2</td><td class="ctr1" id="l13">1</td><td class="ctr2" id="m13">1</td></tr><tr><td id="a13"><a href="TimeSeriesServiceGrpc$TimeSeriesServiceImplBase.html" class="el_class">TimeSeriesServiceGrpc.TimeSeriesServiceImplBase</a></td><td class="bar" id="b14"/><td class="ctr2" id="c14">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">2</td><td class="ctr2" id="g14">2</td><td class="ctr1" id="h14">2</td><td class="ctr2" id="i14">2</td><td class="ctr1" id="j14">2</td><td class="ctr2" id="k14">2</td><td class="ctr1" id="l14">1</td><td class="ctr2" id="m14">1</td></tr><tr><td id="a4"><a href="TimeSeriesServiceGrpc$AsyncService.html" class="el_class">TimeSeriesServiceGrpc.AsyncService</a></td><td class="bar" id="b15"/><td class="ctr2" id="c15">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">2</td><td class="ctr2" id="i15">2</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td><td class="ctr1" id="l15">1</td><td class="ctr2" id="m15">1</td></tr><tr><td id="a11"><a href="TimeSeriesServiceGrpc$TimeSeriesServiceFileDescriptorSupplier.html" class="el_class">TimeSeriesServiceGrpc.TimeSeriesServiceFileDescriptorSupplier</a></td><td class="bar" id="b16"/><td class="ctr2" id="c16">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">1</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td><td class="ctr1" id="l16">1</td><td class="ctr2" id="m16">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>