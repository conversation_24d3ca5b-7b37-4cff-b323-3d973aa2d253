// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: timeseries_service.proto

// Protobuf Java Version: 3.25.1
package com.morningstar.martapi.grpc;

public interface TimeSeriesRequestOrBuilder extends
    // @@protoc_insertion_point(interface_extends:timeseries.TimeSeriesRequest)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <pre>
   * Investment IDs (comma-separated in REST, repeated here)
   * </pre>
   *
   * <code>repeated string investment_ids = 1;</code>
   * @return A list containing the investmentIds.
   */
  java.util.List<java.lang.String>
      getInvestmentIdsList();
  /**
   * <pre>
   * Investment IDs (comma-separated in REST, repeated here)
   * </pre>
   *
   * <code>repeated string investment_ids = 1;</code>
   * @return The count of investmentIds.
   */
  int getInvestmentIdsCount();
  /**
   * <pre>
   * Investment IDs (comma-separated in REST, repeated here)
   * </pre>
   *
   * <code>repeated string investment_ids = 1;</code>
   * @param index The index of the element to return.
   * @return The investmentIds at the given index.
   */
  java.lang.String getInvestmentIds(int index);
  /**
   * <pre>
   * Investment IDs (comma-separated in REST, repeated here)
   * </pre>
   *
   * <code>repeated string investment_ids = 1;</code>
   * @param index The index of the value to return.
   * @return The bytes of the investmentIds at the given index.
   */
  com.google.protobuf.ByteString
      getInvestmentIdsBytes(int index);

  /**
   * <pre>
   * Data points (comma-separated in REST, repeated here)
   * </pre>
   *
   * <code>repeated string data_points = 2;</code>
   * @return A list containing the dataPoints.
   */
  java.util.List<java.lang.String>
      getDataPointsList();
  /**
   * <pre>
   * Data points (comma-separated in REST, repeated here)
   * </pre>
   *
   * <code>repeated string data_points = 2;</code>
   * @return The count of dataPoints.
   */
  int getDataPointsCount();
  /**
   * <pre>
   * Data points (comma-separated in REST, repeated here)
   * </pre>
   *
   * <code>repeated string data_points = 2;</code>
   * @param index The index of the element to return.
   * @return The dataPoints at the given index.
   */
  java.lang.String getDataPoints(int index);
  /**
   * <pre>
   * Data points (comma-separated in REST, repeated here)
   * </pre>
   *
   * <code>repeated string data_points = 2;</code>
   * @param index The index of the value to return.
   * @return The bytes of the dataPoints at the given index.
   */
  com.google.protobuf.ByteString
      getDataPointsBytes(int index);

  /**
   * <pre>
   * Date range
   * </pre>
   *
   * <code>string start_date = 3;</code>
   * @return The startDate.
   */
  java.lang.String getStartDate();
  /**
   * <pre>
   * Date range
   * </pre>
   *
   * <code>string start_date = 3;</code>
   * @return The bytes for startDate.
   */
  com.google.protobuf.ByteString
      getStartDateBytes();

  /**
   * <code>string end_date = 4;</code>
   * @return The endDate.
   */
  java.lang.String getEndDate();
  /**
   * <code>string end_date = 4;</code>
   * @return The bytes for endDate.
   */
  com.google.protobuf.ByteString
      getEndDateBytes();

  /**
   * <pre>
   * Currency settings
   * </pre>
   *
   * <code>string currency = 5;</code>
   * @return The currency.
   */
  java.lang.String getCurrency();
  /**
   * <pre>
   * Currency settings
   * </pre>
   *
   * <code>string currency = 5;</code>
   * @return The bytes for currency.
   */
  com.google.protobuf.ByteString
      getCurrencyBytes();

  /**
   * <code>string pre_currency = 6;</code>
   * @return The preCurrency.
   */
  java.lang.String getPreCurrency();
  /**
   * <code>string pre_currency = 6;</code>
   * @return The bytes for preCurrency.
   */
  com.google.protobuf.ByteString
      getPreCurrencyBytes();

  /**
   * <pre>
   * Cache and format settings
   * </pre>
   *
   * <code>string read_cache = 7;</code>
   * @return The readCache.
   */
  java.lang.String getReadCache();
  /**
   * <pre>
   * Cache and format settings
   * </pre>
   *
   * <code>string read_cache = 7;</code>
   * @return The bytes for readCache.
   */
  com.google.protobuf.ByteString
      getReadCacheBytes();

  /**
   * <code>string date_format = 8;</code>
   * @return The dateFormat.
   */
  java.lang.String getDateFormat();
  /**
   * <code>string date_format = 8;</code>
   * @return The bytes for dateFormat.
   */
  com.google.protobuf.ByteString
      getDateFormatBytes();

  /**
   * <code>string decimal_format = 9;</code>
   * @return The decimalFormat.
   */
  java.lang.String getDecimalFormat();
  /**
   * <code>string decimal_format = 9;</code>
   * @return The bytes for decimalFormat.
   */
  com.google.protobuf.ByteString
      getDecimalFormatBytes();

  /**
   * <pre>
   * Performance and tax settings
   * </pre>
   *
   * <code>string extend_performance = 10;</code>
   * @return The extendPerformance.
   */
  java.lang.String getExtendPerformance();
  /**
   * <pre>
   * Performance and tax settings
   * </pre>
   *
   * <code>string extend_performance = 10;</code>
   * @return The bytes for extendPerformance.
   */
  com.google.protobuf.ByteString
      getExtendPerformanceBytes();

  /**
   * <code>string post_tax = 11;</code>
   * @return The postTax.
   */
  java.lang.String getPostTax();
  /**
   * <code>string post_tax = 11;</code>
   * @return The bytes for postTax.
   */
  com.google.protobuf.ByteString
      getPostTaxBytes();

  /**
   * <pre>
   * Feature flags
   * </pre>
   *
   * <code>bool use_require_id = 12;</code>
   * @return The useRequireId.
   */
  boolean getUseRequireId();

  /**
   * <code>string use_case = 13;</code>
   * @return The useCase.
   */
  java.lang.String getUseCase();
  /**
   * <code>string use_case = 13;</code>
   * @return The bytes for useCase.
   */
  com.google.protobuf.ByteString
      getUseCaseBytes();

  /**
   * <code>bool use_new_ccs = 14;</code>
   * @return The useNewCcs.
   */
  boolean getUseNewCcs();

  /**
   * <pre>
   * Headers
   * </pre>
   *
   * <code>string product_id = 15;</code>
   * @return The productId.
   */
  java.lang.String getProductId();
  /**
   * <pre>
   * Headers
   * </pre>
   *
   * <code>string product_id = 15;</code>
   * @return The bytes for productId.
   */
  com.google.protobuf.ByteString
      getProductIdBytes();

  /**
   * <code>string request_id = 16;</code>
   * @return The requestId.
   */
  java.lang.String getRequestId();
  /**
   * <code>string request_id = 16;</code>
   * @return The bytes for requestId.
   */
  com.google.protobuf.ByteString
      getRequestIdBytes();

  /**
   * <code>string trace_id = 17;</code>
   * @return The traceId.
   */
  java.lang.String getTraceId();
  /**
   * <code>string trace_id = 17;</code>
   * @return The bytes for traceId.
   */
  com.google.protobuf.ByteString
      getTraceIdBytes();

  /**
   * <code>string user_id = 18;</code>
   * @return The userId.
   */
  java.lang.String getUserId();
  /**
   * <code>string user_id = 18;</code>
   * @return The bytes for userId.
   */
  com.google.protobuf.ByteString
      getUserIdBytes();

  /**
   * <code>string authorization = 19;</code>
   * @return The authorization.
   */
  java.lang.String getAuthorization();
  /**
   * <code>string authorization = 19;</code>
   * @return The bytes for authorization.
   */
  com.google.protobuf.ByteString
      getAuthorizationBytes();

  /**
   * <code>bool check_entitlement = 20;</code>
   * @return The checkEntitlement.
   */
  boolean getCheckEntitlement();

  /**
   * <code>string entitlement_product_id = 21;</code>
   * @return The entitlementProductId.
   */
  java.lang.String getEntitlementProductId();
  /**
   * <code>string entitlement_product_id = 21;</code>
   * @return The bytes for entitlementProductId.
   */
  com.google.protobuf.ByteString
      getEntitlementProductIdBytes();
}
