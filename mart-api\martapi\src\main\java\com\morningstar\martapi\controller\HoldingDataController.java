package com.morningstar.martapi.controller;

import com.morningstar.martgateway.domains.core.entity.holdingresponse.HoldingResponse;
import com.morningstar.martgateway.infrastructures.log.LogAttribute;
import com.morningstar.martgateway.infrastructures.log.LogEntity;
import com.morningstar.martgateway.infrastructures.log.LogEntry;
import com.morningstar.martgateway.infrastructures.log.LogHelper;
import com.morningstar.martgateway.interfaces.MartGateway;
import com.morningstar.martcommon.entity.result.request.IdDatePair;
import com.morningstar.martcommon.entity.result.request.MartRequest;
import io.swagger.annotations.ApiParam;
import org.slf4j.MDC;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import javax.inject.Inject;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

import static com.morningstar.martapi.config.Constant.RESPONSE;
import static com.morningstar.martapi.config.Constant.RESPONSE_ERROR;
import static com.morningstar.martapi.config.Constant.SUCCESSFUL_RESPONSE;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.EVENT_DESCRIPTION;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.EVENT_TYPE;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.EXCEPTION_TYPE;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.EXECUTE_TIME;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.PRODUCT_ID;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.REQUEST_PARAM;
import static com.morningstar.martgateway.infrastructures.log.LogAttribute.USER_ID;

@RestController
@RequestMapping(value = "/v1/holding-data",
        produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE})
public class HoldingDataController {
    private final MartGateway<HoldingResponse, MartRequest> holdingDataGateway;

    @Inject
    public HoldingDataController(MartGateway<HoldingResponse, MartRequest> holdingDataGateway) {
        this.holdingDataGateway = holdingDataGateway;
    }

    @GetMapping(value = "/investment-id/{id}")
    public Mono<?> getData(
            @ApiParam(example = "0P00000AWG")
            @PathVariable("id") String id,
            @ApiParam(example = "PMP03")
            @RequestParam(value = "dps", required = true) String dpList,
            @RequestParam(value = "top", required = false, defaultValue = "10") Integer top,
            @RequestParam(value = "readCache", required = false, defaultValue = "false") String readCache,
            @RequestHeader(value = "X-API-UserId", required = false, defaultValue = "") String userId,
            @RequestHeader(value = "X-API-RequestId", required = false, defaultValue = "") String requestId,
            @RequestHeader(value = "X-Api-ProductId", required = false, defaultValue = "") String productId,
            ServerHttpRequest request) {
        long startTime = System.currentTimeMillis();

        IdDatePair idDate = new IdDatePair();
        idDate.setId(id);
        idDate.setDates(Arrays.asList(LocalDate.now()));

        MartRequest martRequest = MartRequest.builder()
                .dps(Arrays.asList(dpList.split(",")))
                .idPairs(List.of(idDate))
                .readCache(readCache)
                .productId(productId)
                .requestId(requestId)
                .userId(userId)
                .top(top)
                .build();

        return holdingDataGateway.asyncRetrieveSecurities(martRequest).doOnEach(LogHelper.logOnNext(list -> {
            MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), requestId);
            LogEntry.info(new LogEntity(EVENT_TYPE, RESPONSE),
                    new LogEntity(EVENT_DESCRIPTION, SUCCESSFUL_RESPONSE),
                    new LogEntity(PRODUCT_ID, productId),
                    new LogEntity(USER_ID, userId),
                    new LogEntity(REQUEST_PARAM, martRequest.getRequestParam(request.getURI().getPath(), request.getURI().getQuery())),
                    new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime));
        })).doOnEach(LogHelper.logOnError(e -> {
            MDC.put(LogAttribute.REQUEST_ID.getDisplayName(), requestId);
            LogEntry.error(
                    new LogEntity(EVENT_TYPE, RESPONSE_ERROR),
                    new LogEntity(EVENT_DESCRIPTION, e),
                    new LogEntity(EXECUTE_TIME, System.currentTimeMillis() - startTime),
                    new LogEntity(EXCEPTION_TYPE, e.getClass()),
                    new LogEntity(PRODUCT_ID, productId),
                    new LogEntity(USER_ID, userId),
                    new LogEntity(REQUEST_PARAM, martRequest.getRequestParam(request.getURI().getPath(), request.getURI().getQuery()))
            );
        }));
    }
}
