package com.morningstar.martapi.controller;

import com.morningstar.martapi.service.RedisMessagePublisher;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import javax.inject.Inject;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import springfox.documentation.annotations.ApiIgnore;

@RestController
@RequestMapping(value = {"/v1/data-point","/investment-api/v1/data-point"},
        produces = {MediaType.APPLICATION_JSON_VALUE})
@ApiIgnore
public class DataPointUpdateController {

    private final RedisMessagePublisher redisMessagePublisher;

    @Inject
    public DataPointUpdateController(RedisMessagePublisher redisMessagePublisher) {
        this.redisMessagePublisher = redisMessagePublisher;
    }

    @GetMapping("/publish")
    public Mono<String> publish() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss"));
        String message = "config-update-" + timestamp;
        redisMessagePublisher.publishSync(message);
        return Mono.just("Success");
    }
}
