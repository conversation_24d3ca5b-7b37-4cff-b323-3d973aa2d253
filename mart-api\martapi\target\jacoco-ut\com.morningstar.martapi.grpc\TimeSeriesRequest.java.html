<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TimeSeriesRequest.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <a href="index.source.html" class="el_package">com.morningstar.martapi.grpc</a> &gt; <span class="el_source">TimeSeriesRequest.java</span></div><h1>TimeSeriesRequest.java</h1><pre class="source lang-java linenums">// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: timeseries_service.proto

// Protobuf Java Version: 3.25.1
package com.morningstar.martapi.grpc;

/**
 * &lt;pre&gt;
 * Request message for time series data
 * &lt;/pre&gt;
 *
 * Protobuf type {@code timeseries.TimeSeriesRequest}
 */
public final class TimeSeriesRequest extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:timeseries.TimeSeriesRequest)
    TimeSeriesRequestOrBuilder {
private static final long serialVersionUID = 0L;
  // Use TimeSeriesRequest.newBuilder() to construct.
  private TimeSeriesRequest(com.google.protobuf.GeneratedMessageV3.Builder&lt;?&gt; builder) {
<span class="nc" id="L21">    super(builder);</span>
<span class="nc" id="L22">  }</span>
<span class="nc" id="L23">  private TimeSeriesRequest() {</span>
<span class="nc" id="L24">    investmentIds_ =</span>
<span class="nc" id="L25">        com.google.protobuf.LazyStringArrayList.emptyList();</span>
<span class="nc" id="L26">    dataPoints_ =</span>
<span class="nc" id="L27">        com.google.protobuf.LazyStringArrayList.emptyList();</span>
<span class="nc" id="L28">    startDate_ = &quot;&quot;;</span>
<span class="nc" id="L29">    endDate_ = &quot;&quot;;</span>
<span class="nc" id="L30">    currency_ = &quot;&quot;;</span>
<span class="nc" id="L31">    preCurrency_ = &quot;&quot;;</span>
<span class="nc" id="L32">    readCache_ = &quot;&quot;;</span>
<span class="nc" id="L33">    dateFormat_ = &quot;&quot;;</span>
<span class="nc" id="L34">    decimalFormat_ = &quot;&quot;;</span>
<span class="nc" id="L35">    extendPerformance_ = &quot;&quot;;</span>
<span class="nc" id="L36">    postTax_ = &quot;&quot;;</span>
<span class="nc" id="L37">    useCase_ = &quot;&quot;;</span>
<span class="nc" id="L38">    productId_ = &quot;&quot;;</span>
<span class="nc" id="L39">    requestId_ = &quot;&quot;;</span>
<span class="nc" id="L40">    traceId_ = &quot;&quot;;</span>
<span class="nc" id="L41">    userId_ = &quot;&quot;;</span>
<span class="nc" id="L42">    authorization_ = &quot;&quot;;</span>
<span class="nc" id="L43">    entitlementProductId_ = &quot;&quot;;</span>
<span class="nc" id="L44">  }</span>

  @java.lang.Override
  @SuppressWarnings({&quot;unused&quot;})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
<span class="nc" id="L50">    return new TimeSeriesRequest();</span>
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
<span class="nc" id="L55">    return com.morningstar.martapi.grpc.TimeSeriesServiceProto.internal_static_timeseries_TimeSeriesRequest_descriptor;</span>
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
<span class="nc" id="L61">    return com.morningstar.martapi.grpc.TimeSeriesServiceProto.internal_static_timeseries_TimeSeriesRequest_fieldAccessorTable</span>
<span class="nc" id="L62">        .ensureFieldAccessorsInitialized(</span>
            com.morningstar.martapi.grpc.TimeSeriesRequest.class, com.morningstar.martapi.grpc.TimeSeriesRequest.Builder.class);
  }

  public static final int INVESTMENT_IDS_FIELD_NUMBER = 1;
<span class="nc" id="L67">  @SuppressWarnings(&quot;serial&quot;)</span>
  private com.google.protobuf.LazyStringArrayList investmentIds_ =
<span class="nc" id="L69">      com.google.protobuf.LazyStringArrayList.emptyList();</span>
  /**
   * &lt;pre&gt;
   * Investment IDs (comma-separated in REST, repeated here)
   * &lt;/pre&gt;
   *
   * &lt;code&gt;repeated string investment_ids = 1;&lt;/code&gt;
   * @return A list containing the investmentIds.
   */
  public com.google.protobuf.ProtocolStringList
      getInvestmentIdsList() {
<span class="nc" id="L80">    return investmentIds_;</span>
  }
  /**
   * &lt;pre&gt;
   * Investment IDs (comma-separated in REST, repeated here)
   * &lt;/pre&gt;
   *
   * &lt;code&gt;repeated string investment_ids = 1;&lt;/code&gt;
   * @return The count of investmentIds.
   */
  public int getInvestmentIdsCount() {
<span class="nc" id="L91">    return investmentIds_.size();</span>
  }
  /**
   * &lt;pre&gt;
   * Investment IDs (comma-separated in REST, repeated here)
   * &lt;/pre&gt;
   *
   * &lt;code&gt;repeated string investment_ids = 1;&lt;/code&gt;
   * @param index The index of the element to return.
   * @return The investmentIds at the given index.
   */
  public java.lang.String getInvestmentIds(int index) {
<span class="nc" id="L103">    return investmentIds_.get(index);</span>
  }
  /**
   * &lt;pre&gt;
   * Investment IDs (comma-separated in REST, repeated here)
   * &lt;/pre&gt;
   *
   * &lt;code&gt;repeated string investment_ids = 1;&lt;/code&gt;
   * @param index The index of the value to return.
   * @return The bytes of the investmentIds at the given index.
   */
  public com.google.protobuf.ByteString
      getInvestmentIdsBytes(int index) {
<span class="nc" id="L116">    return investmentIds_.getByteString(index);</span>
  }

  public static final int DATA_POINTS_FIELD_NUMBER = 2;
<span class="nc" id="L120">  @SuppressWarnings(&quot;serial&quot;)</span>
  private com.google.protobuf.LazyStringArrayList dataPoints_ =
<span class="nc" id="L122">      com.google.protobuf.LazyStringArrayList.emptyList();</span>
  /**
   * &lt;pre&gt;
   * Data points (comma-separated in REST, repeated here)
   * &lt;/pre&gt;
   *
   * &lt;code&gt;repeated string data_points = 2;&lt;/code&gt;
   * @return A list containing the dataPoints.
   */
  public com.google.protobuf.ProtocolStringList
      getDataPointsList() {
<span class="nc" id="L133">    return dataPoints_;</span>
  }
  /**
   * &lt;pre&gt;
   * Data points (comma-separated in REST, repeated here)
   * &lt;/pre&gt;
   *
   * &lt;code&gt;repeated string data_points = 2;&lt;/code&gt;
   * @return The count of dataPoints.
   */
  public int getDataPointsCount() {
<span class="nc" id="L144">    return dataPoints_.size();</span>
  }
  /**
   * &lt;pre&gt;
   * Data points (comma-separated in REST, repeated here)
   * &lt;/pre&gt;
   *
   * &lt;code&gt;repeated string data_points = 2;&lt;/code&gt;
   * @param index The index of the element to return.
   * @return The dataPoints at the given index.
   */
  public java.lang.String getDataPoints(int index) {
<span class="nc" id="L156">    return dataPoints_.get(index);</span>
  }
  /**
   * &lt;pre&gt;
   * Data points (comma-separated in REST, repeated here)
   * &lt;/pre&gt;
   *
   * &lt;code&gt;repeated string data_points = 2;&lt;/code&gt;
   * @param index The index of the value to return.
   * @return The bytes of the dataPoints at the given index.
   */
  public com.google.protobuf.ByteString
      getDataPointsBytes(int index) {
<span class="nc" id="L169">    return dataPoints_.getByteString(index);</span>
  }

  public static final int START_DATE_FIELD_NUMBER = 3;
<span class="nc" id="L173">  @SuppressWarnings(&quot;serial&quot;)</span>
  private volatile java.lang.Object startDate_ = &quot;&quot;;
  /**
   * &lt;pre&gt;
   * Date range
   * &lt;/pre&gt;
   *
   * &lt;code&gt;string start_date = 3;&lt;/code&gt;
   * @return The startDate.
   */
  @java.lang.Override
  public java.lang.String getStartDate() {
<span class="nc" id="L185">    java.lang.Object ref = startDate_;</span>
<span class="nc bnc" id="L186" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L187">      return (java.lang.String) ref;</span>
    } else {
<span class="nc" id="L189">      com.google.protobuf.ByteString bs = </span>
          (com.google.protobuf.ByteString) ref;
<span class="nc" id="L191">      java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L192">      startDate_ = s;</span>
<span class="nc" id="L193">      return s;</span>
    }
  }
  /**
   * &lt;pre&gt;
   * Date range
   * &lt;/pre&gt;
   *
   * &lt;code&gt;string start_date = 3;&lt;/code&gt;
   * @return The bytes for startDate.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getStartDateBytes() {
<span class="nc" id="L207">    java.lang.Object ref = startDate_;</span>
<span class="nc bnc" id="L208" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L209">      com.google.protobuf.ByteString b = </span>
<span class="nc" id="L210">          com.google.protobuf.ByteString.copyFromUtf8(</span>
              (java.lang.String) ref);
<span class="nc" id="L212">      startDate_ = b;</span>
<span class="nc" id="L213">      return b;</span>
    } else {
<span class="nc" id="L215">      return (com.google.protobuf.ByteString) ref;</span>
    }
  }

  public static final int END_DATE_FIELD_NUMBER = 4;
<span class="nc" id="L220">  @SuppressWarnings(&quot;serial&quot;)</span>
  private volatile java.lang.Object endDate_ = &quot;&quot;;
  /**
   * &lt;code&gt;string end_date = 4;&lt;/code&gt;
   * @return The endDate.
   */
  @java.lang.Override
  public java.lang.String getEndDate() {
<span class="nc" id="L228">    java.lang.Object ref = endDate_;</span>
<span class="nc bnc" id="L229" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L230">      return (java.lang.String) ref;</span>
    } else {
<span class="nc" id="L232">      com.google.protobuf.ByteString bs = </span>
          (com.google.protobuf.ByteString) ref;
<span class="nc" id="L234">      java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L235">      endDate_ = s;</span>
<span class="nc" id="L236">      return s;</span>
    }
  }
  /**
   * &lt;code&gt;string end_date = 4;&lt;/code&gt;
   * @return The bytes for endDate.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getEndDateBytes() {
<span class="nc" id="L246">    java.lang.Object ref = endDate_;</span>
<span class="nc bnc" id="L247" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L248">      com.google.protobuf.ByteString b = </span>
<span class="nc" id="L249">          com.google.protobuf.ByteString.copyFromUtf8(</span>
              (java.lang.String) ref);
<span class="nc" id="L251">      endDate_ = b;</span>
<span class="nc" id="L252">      return b;</span>
    } else {
<span class="nc" id="L254">      return (com.google.protobuf.ByteString) ref;</span>
    }
  }

  public static final int CURRENCY_FIELD_NUMBER = 5;
<span class="nc" id="L259">  @SuppressWarnings(&quot;serial&quot;)</span>
  private volatile java.lang.Object currency_ = &quot;&quot;;
  /**
   * &lt;pre&gt;
   * Currency settings
   * &lt;/pre&gt;
   *
   * &lt;code&gt;string currency = 5;&lt;/code&gt;
   * @return The currency.
   */
  @java.lang.Override
  public java.lang.String getCurrency() {
<span class="nc" id="L271">    java.lang.Object ref = currency_;</span>
<span class="nc bnc" id="L272" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L273">      return (java.lang.String) ref;</span>
    } else {
<span class="nc" id="L275">      com.google.protobuf.ByteString bs = </span>
          (com.google.protobuf.ByteString) ref;
<span class="nc" id="L277">      java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L278">      currency_ = s;</span>
<span class="nc" id="L279">      return s;</span>
    }
  }
  /**
   * &lt;pre&gt;
   * Currency settings
   * &lt;/pre&gt;
   *
   * &lt;code&gt;string currency = 5;&lt;/code&gt;
   * @return The bytes for currency.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getCurrencyBytes() {
<span class="nc" id="L293">    java.lang.Object ref = currency_;</span>
<span class="nc bnc" id="L294" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L295">      com.google.protobuf.ByteString b = </span>
<span class="nc" id="L296">          com.google.protobuf.ByteString.copyFromUtf8(</span>
              (java.lang.String) ref);
<span class="nc" id="L298">      currency_ = b;</span>
<span class="nc" id="L299">      return b;</span>
    } else {
<span class="nc" id="L301">      return (com.google.protobuf.ByteString) ref;</span>
    }
  }

  public static final int PRE_CURRENCY_FIELD_NUMBER = 6;
<span class="nc" id="L306">  @SuppressWarnings(&quot;serial&quot;)</span>
  private volatile java.lang.Object preCurrency_ = &quot;&quot;;
  /**
   * &lt;code&gt;string pre_currency = 6;&lt;/code&gt;
   * @return The preCurrency.
   */
  @java.lang.Override
  public java.lang.String getPreCurrency() {
<span class="nc" id="L314">    java.lang.Object ref = preCurrency_;</span>
<span class="nc bnc" id="L315" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L316">      return (java.lang.String) ref;</span>
    } else {
<span class="nc" id="L318">      com.google.protobuf.ByteString bs = </span>
          (com.google.protobuf.ByteString) ref;
<span class="nc" id="L320">      java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L321">      preCurrency_ = s;</span>
<span class="nc" id="L322">      return s;</span>
    }
  }
  /**
   * &lt;code&gt;string pre_currency = 6;&lt;/code&gt;
   * @return The bytes for preCurrency.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPreCurrencyBytes() {
<span class="nc" id="L332">    java.lang.Object ref = preCurrency_;</span>
<span class="nc bnc" id="L333" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L334">      com.google.protobuf.ByteString b = </span>
<span class="nc" id="L335">          com.google.protobuf.ByteString.copyFromUtf8(</span>
              (java.lang.String) ref);
<span class="nc" id="L337">      preCurrency_ = b;</span>
<span class="nc" id="L338">      return b;</span>
    } else {
<span class="nc" id="L340">      return (com.google.protobuf.ByteString) ref;</span>
    }
  }

  public static final int READ_CACHE_FIELD_NUMBER = 7;
<span class="nc" id="L345">  @SuppressWarnings(&quot;serial&quot;)</span>
  private volatile java.lang.Object readCache_ = &quot;&quot;;
  /**
   * &lt;pre&gt;
   * Cache and format settings
   * &lt;/pre&gt;
   *
   * &lt;code&gt;string read_cache = 7;&lt;/code&gt;
   * @return The readCache.
   */
  @java.lang.Override
  public java.lang.String getReadCache() {
<span class="nc" id="L357">    java.lang.Object ref = readCache_;</span>
<span class="nc bnc" id="L358" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L359">      return (java.lang.String) ref;</span>
    } else {
<span class="nc" id="L361">      com.google.protobuf.ByteString bs = </span>
          (com.google.protobuf.ByteString) ref;
<span class="nc" id="L363">      java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L364">      readCache_ = s;</span>
<span class="nc" id="L365">      return s;</span>
    }
  }
  /**
   * &lt;pre&gt;
   * Cache and format settings
   * &lt;/pre&gt;
   *
   * &lt;code&gt;string read_cache = 7;&lt;/code&gt;
   * @return The bytes for readCache.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getReadCacheBytes() {
<span class="nc" id="L379">    java.lang.Object ref = readCache_;</span>
<span class="nc bnc" id="L380" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L381">      com.google.protobuf.ByteString b = </span>
<span class="nc" id="L382">          com.google.protobuf.ByteString.copyFromUtf8(</span>
              (java.lang.String) ref);
<span class="nc" id="L384">      readCache_ = b;</span>
<span class="nc" id="L385">      return b;</span>
    } else {
<span class="nc" id="L387">      return (com.google.protobuf.ByteString) ref;</span>
    }
  }

  public static final int DATE_FORMAT_FIELD_NUMBER = 8;
<span class="nc" id="L392">  @SuppressWarnings(&quot;serial&quot;)</span>
  private volatile java.lang.Object dateFormat_ = &quot;&quot;;
  /**
   * &lt;code&gt;string date_format = 8;&lt;/code&gt;
   * @return The dateFormat.
   */
  @java.lang.Override
  public java.lang.String getDateFormat() {
<span class="nc" id="L400">    java.lang.Object ref = dateFormat_;</span>
<span class="nc bnc" id="L401" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L402">      return (java.lang.String) ref;</span>
    } else {
<span class="nc" id="L404">      com.google.protobuf.ByteString bs = </span>
          (com.google.protobuf.ByteString) ref;
<span class="nc" id="L406">      java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L407">      dateFormat_ = s;</span>
<span class="nc" id="L408">      return s;</span>
    }
  }
  /**
   * &lt;code&gt;string date_format = 8;&lt;/code&gt;
   * @return The bytes for dateFormat.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDateFormatBytes() {
<span class="nc" id="L418">    java.lang.Object ref = dateFormat_;</span>
<span class="nc bnc" id="L419" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L420">      com.google.protobuf.ByteString b = </span>
<span class="nc" id="L421">          com.google.protobuf.ByteString.copyFromUtf8(</span>
              (java.lang.String) ref);
<span class="nc" id="L423">      dateFormat_ = b;</span>
<span class="nc" id="L424">      return b;</span>
    } else {
<span class="nc" id="L426">      return (com.google.protobuf.ByteString) ref;</span>
    }
  }

  public static final int DECIMAL_FORMAT_FIELD_NUMBER = 9;
<span class="nc" id="L431">  @SuppressWarnings(&quot;serial&quot;)</span>
  private volatile java.lang.Object decimalFormat_ = &quot;&quot;;
  /**
   * &lt;code&gt;string decimal_format = 9;&lt;/code&gt;
   * @return The decimalFormat.
   */
  @java.lang.Override
  public java.lang.String getDecimalFormat() {
<span class="nc" id="L439">    java.lang.Object ref = decimalFormat_;</span>
<span class="nc bnc" id="L440" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L441">      return (java.lang.String) ref;</span>
    } else {
<span class="nc" id="L443">      com.google.protobuf.ByteString bs = </span>
          (com.google.protobuf.ByteString) ref;
<span class="nc" id="L445">      java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L446">      decimalFormat_ = s;</span>
<span class="nc" id="L447">      return s;</span>
    }
  }
  /**
   * &lt;code&gt;string decimal_format = 9;&lt;/code&gt;
   * @return The bytes for decimalFormat.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getDecimalFormatBytes() {
<span class="nc" id="L457">    java.lang.Object ref = decimalFormat_;</span>
<span class="nc bnc" id="L458" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L459">      com.google.protobuf.ByteString b = </span>
<span class="nc" id="L460">          com.google.protobuf.ByteString.copyFromUtf8(</span>
              (java.lang.String) ref);
<span class="nc" id="L462">      decimalFormat_ = b;</span>
<span class="nc" id="L463">      return b;</span>
    } else {
<span class="nc" id="L465">      return (com.google.protobuf.ByteString) ref;</span>
    }
  }

  public static final int EXTEND_PERFORMANCE_FIELD_NUMBER = 10;
<span class="nc" id="L470">  @SuppressWarnings(&quot;serial&quot;)</span>
  private volatile java.lang.Object extendPerformance_ = &quot;&quot;;
  /**
   * &lt;pre&gt;
   * Performance and tax settings
   * &lt;/pre&gt;
   *
   * &lt;code&gt;string extend_performance = 10;&lt;/code&gt;
   * @return The extendPerformance.
   */
  @java.lang.Override
  public java.lang.String getExtendPerformance() {
<span class="nc" id="L482">    java.lang.Object ref = extendPerformance_;</span>
<span class="nc bnc" id="L483" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L484">      return (java.lang.String) ref;</span>
    } else {
<span class="nc" id="L486">      com.google.protobuf.ByteString bs = </span>
          (com.google.protobuf.ByteString) ref;
<span class="nc" id="L488">      java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L489">      extendPerformance_ = s;</span>
<span class="nc" id="L490">      return s;</span>
    }
  }
  /**
   * &lt;pre&gt;
   * Performance and tax settings
   * &lt;/pre&gt;
   *
   * &lt;code&gt;string extend_performance = 10;&lt;/code&gt;
   * @return The bytes for extendPerformance.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getExtendPerformanceBytes() {
<span class="nc" id="L504">    java.lang.Object ref = extendPerformance_;</span>
<span class="nc bnc" id="L505" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L506">      com.google.protobuf.ByteString b = </span>
<span class="nc" id="L507">          com.google.protobuf.ByteString.copyFromUtf8(</span>
              (java.lang.String) ref);
<span class="nc" id="L509">      extendPerformance_ = b;</span>
<span class="nc" id="L510">      return b;</span>
    } else {
<span class="nc" id="L512">      return (com.google.protobuf.ByteString) ref;</span>
    }
  }

  public static final int POST_TAX_FIELD_NUMBER = 11;
<span class="nc" id="L517">  @SuppressWarnings(&quot;serial&quot;)</span>
  private volatile java.lang.Object postTax_ = &quot;&quot;;
  /**
   * &lt;code&gt;string post_tax = 11;&lt;/code&gt;
   * @return The postTax.
   */
  @java.lang.Override
  public java.lang.String getPostTax() {
<span class="nc" id="L525">    java.lang.Object ref = postTax_;</span>
<span class="nc bnc" id="L526" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L527">      return (java.lang.String) ref;</span>
    } else {
<span class="nc" id="L529">      com.google.protobuf.ByteString bs = </span>
          (com.google.protobuf.ByteString) ref;
<span class="nc" id="L531">      java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L532">      postTax_ = s;</span>
<span class="nc" id="L533">      return s;</span>
    }
  }
  /**
   * &lt;code&gt;string post_tax = 11;&lt;/code&gt;
   * @return The bytes for postTax.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getPostTaxBytes() {
<span class="nc" id="L543">    java.lang.Object ref = postTax_;</span>
<span class="nc bnc" id="L544" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L545">      com.google.protobuf.ByteString b = </span>
<span class="nc" id="L546">          com.google.protobuf.ByteString.copyFromUtf8(</span>
              (java.lang.String) ref);
<span class="nc" id="L548">      postTax_ = b;</span>
<span class="nc" id="L549">      return b;</span>
    } else {
<span class="nc" id="L551">      return (com.google.protobuf.ByteString) ref;</span>
    }
  }

  public static final int USE_REQUIRE_ID_FIELD_NUMBER = 12;
<span class="nc" id="L556">  private boolean useRequireId_ = false;</span>
  /**
   * &lt;pre&gt;
   * Feature flags
   * &lt;/pre&gt;
   *
   * &lt;code&gt;bool use_require_id = 12;&lt;/code&gt;
   * @return The useRequireId.
   */
  @java.lang.Override
  public boolean getUseRequireId() {
<span class="nc" id="L567">    return useRequireId_;</span>
  }

  public static final int USE_CASE_FIELD_NUMBER = 13;
<span class="nc" id="L571">  @SuppressWarnings(&quot;serial&quot;)</span>
  private volatile java.lang.Object useCase_ = &quot;&quot;;
  /**
   * &lt;code&gt;string use_case = 13;&lt;/code&gt;
   * @return The useCase.
   */
  @java.lang.Override
  public java.lang.String getUseCase() {
<span class="nc" id="L579">    java.lang.Object ref = useCase_;</span>
<span class="nc bnc" id="L580" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L581">      return (java.lang.String) ref;</span>
    } else {
<span class="nc" id="L583">      com.google.protobuf.ByteString bs = </span>
          (com.google.protobuf.ByteString) ref;
<span class="nc" id="L585">      java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L586">      useCase_ = s;</span>
<span class="nc" id="L587">      return s;</span>
    }
  }
  /**
   * &lt;code&gt;string use_case = 13;&lt;/code&gt;
   * @return The bytes for useCase.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getUseCaseBytes() {
<span class="nc" id="L597">    java.lang.Object ref = useCase_;</span>
<span class="nc bnc" id="L598" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L599">      com.google.protobuf.ByteString b = </span>
<span class="nc" id="L600">          com.google.protobuf.ByteString.copyFromUtf8(</span>
              (java.lang.String) ref);
<span class="nc" id="L602">      useCase_ = b;</span>
<span class="nc" id="L603">      return b;</span>
    } else {
<span class="nc" id="L605">      return (com.google.protobuf.ByteString) ref;</span>
    }
  }

  public static final int USE_NEW_CCS_FIELD_NUMBER = 14;
<span class="nc" id="L610">  private boolean useNewCcs_ = false;</span>
  /**
   * &lt;code&gt;bool use_new_ccs = 14;&lt;/code&gt;
   * @return The useNewCcs.
   */
  @java.lang.Override
  public boolean getUseNewCcs() {
<span class="nc" id="L617">    return useNewCcs_;</span>
  }

  public static final int PRODUCT_ID_FIELD_NUMBER = 15;
<span class="nc" id="L621">  @SuppressWarnings(&quot;serial&quot;)</span>
  private volatile java.lang.Object productId_ = &quot;&quot;;
  /**
   * &lt;pre&gt;
   * Headers
   * &lt;/pre&gt;
   *
   * &lt;code&gt;string product_id = 15;&lt;/code&gt;
   * @return The productId.
   */
  @java.lang.Override
  public java.lang.String getProductId() {
<span class="nc" id="L633">    java.lang.Object ref = productId_;</span>
<span class="nc bnc" id="L634" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L635">      return (java.lang.String) ref;</span>
    } else {
<span class="nc" id="L637">      com.google.protobuf.ByteString bs = </span>
          (com.google.protobuf.ByteString) ref;
<span class="nc" id="L639">      java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L640">      productId_ = s;</span>
<span class="nc" id="L641">      return s;</span>
    }
  }
  /**
   * &lt;pre&gt;
   * Headers
   * &lt;/pre&gt;
   *
   * &lt;code&gt;string product_id = 15;&lt;/code&gt;
   * @return The bytes for productId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getProductIdBytes() {
<span class="nc" id="L655">    java.lang.Object ref = productId_;</span>
<span class="nc bnc" id="L656" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L657">      com.google.protobuf.ByteString b = </span>
<span class="nc" id="L658">          com.google.protobuf.ByteString.copyFromUtf8(</span>
              (java.lang.String) ref);
<span class="nc" id="L660">      productId_ = b;</span>
<span class="nc" id="L661">      return b;</span>
    } else {
<span class="nc" id="L663">      return (com.google.protobuf.ByteString) ref;</span>
    }
  }

  public static final int REQUEST_ID_FIELD_NUMBER = 16;
<span class="nc" id="L668">  @SuppressWarnings(&quot;serial&quot;)</span>
  private volatile java.lang.Object requestId_ = &quot;&quot;;
  /**
   * &lt;code&gt;string request_id = 16;&lt;/code&gt;
   * @return The requestId.
   */
  @java.lang.Override
  public java.lang.String getRequestId() {
<span class="nc" id="L676">    java.lang.Object ref = requestId_;</span>
<span class="nc bnc" id="L677" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L678">      return (java.lang.String) ref;</span>
    } else {
<span class="nc" id="L680">      com.google.protobuf.ByteString bs = </span>
          (com.google.protobuf.ByteString) ref;
<span class="nc" id="L682">      java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L683">      requestId_ = s;</span>
<span class="nc" id="L684">      return s;</span>
    }
  }
  /**
   * &lt;code&gt;string request_id = 16;&lt;/code&gt;
   * @return The bytes for requestId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getRequestIdBytes() {
<span class="nc" id="L694">    java.lang.Object ref = requestId_;</span>
<span class="nc bnc" id="L695" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L696">      com.google.protobuf.ByteString b = </span>
<span class="nc" id="L697">          com.google.protobuf.ByteString.copyFromUtf8(</span>
              (java.lang.String) ref);
<span class="nc" id="L699">      requestId_ = b;</span>
<span class="nc" id="L700">      return b;</span>
    } else {
<span class="nc" id="L702">      return (com.google.protobuf.ByteString) ref;</span>
    }
  }

  public static final int TRACE_ID_FIELD_NUMBER = 17;
<span class="nc" id="L707">  @SuppressWarnings(&quot;serial&quot;)</span>
  private volatile java.lang.Object traceId_ = &quot;&quot;;
  /**
   * &lt;code&gt;string trace_id = 17;&lt;/code&gt;
   * @return The traceId.
   */
  @java.lang.Override
  public java.lang.String getTraceId() {
<span class="nc" id="L715">    java.lang.Object ref = traceId_;</span>
<span class="nc bnc" id="L716" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L717">      return (java.lang.String) ref;</span>
    } else {
<span class="nc" id="L719">      com.google.protobuf.ByteString bs = </span>
          (com.google.protobuf.ByteString) ref;
<span class="nc" id="L721">      java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L722">      traceId_ = s;</span>
<span class="nc" id="L723">      return s;</span>
    }
  }
  /**
   * &lt;code&gt;string trace_id = 17;&lt;/code&gt;
   * @return The bytes for traceId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getTraceIdBytes() {
<span class="nc" id="L733">    java.lang.Object ref = traceId_;</span>
<span class="nc bnc" id="L734" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L735">      com.google.protobuf.ByteString b = </span>
<span class="nc" id="L736">          com.google.protobuf.ByteString.copyFromUtf8(</span>
              (java.lang.String) ref);
<span class="nc" id="L738">      traceId_ = b;</span>
<span class="nc" id="L739">      return b;</span>
    } else {
<span class="nc" id="L741">      return (com.google.protobuf.ByteString) ref;</span>
    }
  }

  public static final int USER_ID_FIELD_NUMBER = 18;
<span class="nc" id="L746">  @SuppressWarnings(&quot;serial&quot;)</span>
  private volatile java.lang.Object userId_ = &quot;&quot;;
  /**
   * &lt;code&gt;string user_id = 18;&lt;/code&gt;
   * @return The userId.
   */
  @java.lang.Override
  public java.lang.String getUserId() {
<span class="nc" id="L754">    java.lang.Object ref = userId_;</span>
<span class="nc bnc" id="L755" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L756">      return (java.lang.String) ref;</span>
    } else {
<span class="nc" id="L758">      com.google.protobuf.ByteString bs = </span>
          (com.google.protobuf.ByteString) ref;
<span class="nc" id="L760">      java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L761">      userId_ = s;</span>
<span class="nc" id="L762">      return s;</span>
    }
  }
  /**
   * &lt;code&gt;string user_id = 18;&lt;/code&gt;
   * @return The bytes for userId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getUserIdBytes() {
<span class="nc" id="L772">    java.lang.Object ref = userId_;</span>
<span class="nc bnc" id="L773" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L774">      com.google.protobuf.ByteString b = </span>
<span class="nc" id="L775">          com.google.protobuf.ByteString.copyFromUtf8(</span>
              (java.lang.String) ref);
<span class="nc" id="L777">      userId_ = b;</span>
<span class="nc" id="L778">      return b;</span>
    } else {
<span class="nc" id="L780">      return (com.google.protobuf.ByteString) ref;</span>
    }
  }

  public static final int AUTHORIZATION_FIELD_NUMBER = 19;
<span class="nc" id="L785">  @SuppressWarnings(&quot;serial&quot;)</span>
  private volatile java.lang.Object authorization_ = &quot;&quot;;
  /**
   * &lt;code&gt;string authorization = 19;&lt;/code&gt;
   * @return The authorization.
   */
  @java.lang.Override
  public java.lang.String getAuthorization() {
<span class="nc" id="L793">    java.lang.Object ref = authorization_;</span>
<span class="nc bnc" id="L794" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L795">      return (java.lang.String) ref;</span>
    } else {
<span class="nc" id="L797">      com.google.protobuf.ByteString bs = </span>
          (com.google.protobuf.ByteString) ref;
<span class="nc" id="L799">      java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L800">      authorization_ = s;</span>
<span class="nc" id="L801">      return s;</span>
    }
  }
  /**
   * &lt;code&gt;string authorization = 19;&lt;/code&gt;
   * @return The bytes for authorization.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getAuthorizationBytes() {
<span class="nc" id="L811">    java.lang.Object ref = authorization_;</span>
<span class="nc bnc" id="L812" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L813">      com.google.protobuf.ByteString b = </span>
<span class="nc" id="L814">          com.google.protobuf.ByteString.copyFromUtf8(</span>
              (java.lang.String) ref);
<span class="nc" id="L816">      authorization_ = b;</span>
<span class="nc" id="L817">      return b;</span>
    } else {
<span class="nc" id="L819">      return (com.google.protobuf.ByteString) ref;</span>
    }
  }

  public static final int CHECK_ENTITLEMENT_FIELD_NUMBER = 20;
<span class="nc" id="L824">  private boolean checkEntitlement_ = false;</span>
  /**
   * &lt;code&gt;bool check_entitlement = 20;&lt;/code&gt;
   * @return The checkEntitlement.
   */
  @java.lang.Override
  public boolean getCheckEntitlement() {
<span class="nc" id="L831">    return checkEntitlement_;</span>
  }

  public static final int ENTITLEMENT_PRODUCT_ID_FIELD_NUMBER = 21;
<span class="nc" id="L835">  @SuppressWarnings(&quot;serial&quot;)</span>
  private volatile java.lang.Object entitlementProductId_ = &quot;&quot;;
  /**
   * &lt;code&gt;string entitlement_product_id = 21;&lt;/code&gt;
   * @return The entitlementProductId.
   */
  @java.lang.Override
  public java.lang.String getEntitlementProductId() {
<span class="nc" id="L843">    java.lang.Object ref = entitlementProductId_;</span>
<span class="nc bnc" id="L844" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L845">      return (java.lang.String) ref;</span>
    } else {
<span class="nc" id="L847">      com.google.protobuf.ByteString bs = </span>
          (com.google.protobuf.ByteString) ref;
<span class="nc" id="L849">      java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L850">      entitlementProductId_ = s;</span>
<span class="nc" id="L851">      return s;</span>
    }
  }
  /**
   * &lt;code&gt;string entitlement_product_id = 21;&lt;/code&gt;
   * @return The bytes for entitlementProductId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getEntitlementProductIdBytes() {
<span class="nc" id="L861">    java.lang.Object ref = entitlementProductId_;</span>
<span class="nc bnc" id="L862" title="All 2 branches missed.">    if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L863">      com.google.protobuf.ByteString b = </span>
<span class="nc" id="L864">          com.google.protobuf.ByteString.copyFromUtf8(</span>
              (java.lang.String) ref);
<span class="nc" id="L866">      entitlementProductId_ = b;</span>
<span class="nc" id="L867">      return b;</span>
    } else {
<span class="nc" id="L869">      return (com.google.protobuf.ByteString) ref;</span>
    }
  }

<span class="nc" id="L873">  private byte memoizedIsInitialized = -1;</span>
  @java.lang.Override
  public final boolean isInitialized() {
<span class="nc" id="L876">    byte isInitialized = memoizedIsInitialized;</span>
<span class="nc bnc" id="L877" title="All 2 branches missed.">    if (isInitialized == 1) return true;</span>
<span class="nc bnc" id="L878" title="All 2 branches missed.">    if (isInitialized == 0) return false;</span>

<span class="nc" id="L880">    memoizedIsInitialized = 1;</span>
<span class="nc" id="L881">    return true;</span>
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
<span class="nc bnc" id="L887" title="All 2 branches missed.">    for (int i = 0; i &lt; investmentIds_.size(); i++) {</span>
<span class="nc" id="L888">      com.google.protobuf.GeneratedMessageV3.writeString(output, 1, investmentIds_.getRaw(i));</span>
    }
<span class="nc bnc" id="L890" title="All 2 branches missed.">    for (int i = 0; i &lt; dataPoints_.size(); i++) {</span>
<span class="nc" id="L891">      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, dataPoints_.getRaw(i));</span>
    }
<span class="nc bnc" id="L893" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(startDate_)) {</span>
<span class="nc" id="L894">      com.google.protobuf.GeneratedMessageV3.writeString(output, 3, startDate_);</span>
    }
<span class="nc bnc" id="L896" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(endDate_)) {</span>
<span class="nc" id="L897">      com.google.protobuf.GeneratedMessageV3.writeString(output, 4, endDate_);</span>
    }
<span class="nc bnc" id="L899" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(currency_)) {</span>
<span class="nc" id="L900">      com.google.protobuf.GeneratedMessageV3.writeString(output, 5, currency_);</span>
    }
<span class="nc bnc" id="L902" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(preCurrency_)) {</span>
<span class="nc" id="L903">      com.google.protobuf.GeneratedMessageV3.writeString(output, 6, preCurrency_);</span>
    }
<span class="nc bnc" id="L905" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(readCache_)) {</span>
<span class="nc" id="L906">      com.google.protobuf.GeneratedMessageV3.writeString(output, 7, readCache_);</span>
    }
<span class="nc bnc" id="L908" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(dateFormat_)) {</span>
<span class="nc" id="L909">      com.google.protobuf.GeneratedMessageV3.writeString(output, 8, dateFormat_);</span>
    }
<span class="nc bnc" id="L911" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(decimalFormat_)) {</span>
<span class="nc" id="L912">      com.google.protobuf.GeneratedMessageV3.writeString(output, 9, decimalFormat_);</span>
    }
<span class="nc bnc" id="L914" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(extendPerformance_)) {</span>
<span class="nc" id="L915">      com.google.protobuf.GeneratedMessageV3.writeString(output, 10, extendPerformance_);</span>
    }
<span class="nc bnc" id="L917" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(postTax_)) {</span>
<span class="nc" id="L918">      com.google.protobuf.GeneratedMessageV3.writeString(output, 11, postTax_);</span>
    }
<span class="nc bnc" id="L920" title="All 2 branches missed.">    if (useRequireId_ != false) {</span>
<span class="nc" id="L921">      output.writeBool(12, useRequireId_);</span>
    }
<span class="nc bnc" id="L923" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(useCase_)) {</span>
<span class="nc" id="L924">      com.google.protobuf.GeneratedMessageV3.writeString(output, 13, useCase_);</span>
    }
<span class="nc bnc" id="L926" title="All 2 branches missed.">    if (useNewCcs_ != false) {</span>
<span class="nc" id="L927">      output.writeBool(14, useNewCcs_);</span>
    }
<span class="nc bnc" id="L929" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(productId_)) {</span>
<span class="nc" id="L930">      com.google.protobuf.GeneratedMessageV3.writeString(output, 15, productId_);</span>
    }
<span class="nc bnc" id="L932" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(requestId_)) {</span>
<span class="nc" id="L933">      com.google.protobuf.GeneratedMessageV3.writeString(output, 16, requestId_);</span>
    }
<span class="nc bnc" id="L935" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(traceId_)) {</span>
<span class="nc" id="L936">      com.google.protobuf.GeneratedMessageV3.writeString(output, 17, traceId_);</span>
    }
<span class="nc bnc" id="L938" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(userId_)) {</span>
<span class="nc" id="L939">      com.google.protobuf.GeneratedMessageV3.writeString(output, 18, userId_);</span>
    }
<span class="nc bnc" id="L941" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(authorization_)) {</span>
<span class="nc" id="L942">      com.google.protobuf.GeneratedMessageV3.writeString(output, 19, authorization_);</span>
    }
<span class="nc bnc" id="L944" title="All 2 branches missed.">    if (checkEntitlement_ != false) {</span>
<span class="nc" id="L945">      output.writeBool(20, checkEntitlement_);</span>
    }
<span class="nc bnc" id="L947" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(entitlementProductId_)) {</span>
<span class="nc" id="L948">      com.google.protobuf.GeneratedMessageV3.writeString(output, 21, entitlementProductId_);</span>
    }
<span class="nc" id="L950">    getUnknownFields().writeTo(output);</span>
<span class="nc" id="L951">  }</span>

  @java.lang.Override
  public int getSerializedSize() {
<span class="nc" id="L955">    int size = memoizedSize;</span>
<span class="nc bnc" id="L956" title="All 2 branches missed.">    if (size != -1) return size;</span>

<span class="nc" id="L958">    size = 0;</span>
    {
<span class="nc" id="L960">      int dataSize = 0;</span>
<span class="nc bnc" id="L961" title="All 2 branches missed.">      for (int i = 0; i &lt; investmentIds_.size(); i++) {</span>
<span class="nc" id="L962">        dataSize += computeStringSizeNoTag(investmentIds_.getRaw(i));</span>
      }
<span class="nc" id="L964">      size += dataSize;</span>
<span class="nc" id="L965">      size += 1 * getInvestmentIdsList().size();</span>
    }
    {
<span class="nc" id="L968">      int dataSize = 0;</span>
<span class="nc bnc" id="L969" title="All 2 branches missed.">      for (int i = 0; i &lt; dataPoints_.size(); i++) {</span>
<span class="nc" id="L970">        dataSize += computeStringSizeNoTag(dataPoints_.getRaw(i));</span>
      }
<span class="nc" id="L972">      size += dataSize;</span>
<span class="nc" id="L973">      size += 1 * getDataPointsList().size();</span>
    }
<span class="nc bnc" id="L975" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(startDate_)) {</span>
<span class="nc" id="L976">      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, startDate_);</span>
    }
<span class="nc bnc" id="L978" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(endDate_)) {</span>
<span class="nc" id="L979">      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, endDate_);</span>
    }
<span class="nc bnc" id="L981" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(currency_)) {</span>
<span class="nc" id="L982">      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, currency_);</span>
    }
<span class="nc bnc" id="L984" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(preCurrency_)) {</span>
<span class="nc" id="L985">      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, preCurrency_);</span>
    }
<span class="nc bnc" id="L987" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(readCache_)) {</span>
<span class="nc" id="L988">      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, readCache_);</span>
    }
<span class="nc bnc" id="L990" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(dateFormat_)) {</span>
<span class="nc" id="L991">      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, dateFormat_);</span>
    }
<span class="nc bnc" id="L993" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(decimalFormat_)) {</span>
<span class="nc" id="L994">      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, decimalFormat_);</span>
    }
<span class="nc bnc" id="L996" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(extendPerformance_)) {</span>
<span class="nc" id="L997">      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, extendPerformance_);</span>
    }
<span class="nc bnc" id="L999" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(postTax_)) {</span>
<span class="nc" id="L1000">      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, postTax_);</span>
    }
<span class="nc bnc" id="L1002" title="All 2 branches missed.">    if (useRequireId_ != false) {</span>
<span class="nc" id="L1003">      size += com.google.protobuf.CodedOutputStream</span>
<span class="nc" id="L1004">        .computeBoolSize(12, useRequireId_);</span>
    }
<span class="nc bnc" id="L1006" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(useCase_)) {</span>
<span class="nc" id="L1007">      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(13, useCase_);</span>
    }
<span class="nc bnc" id="L1009" title="All 2 branches missed.">    if (useNewCcs_ != false) {</span>
<span class="nc" id="L1010">      size += com.google.protobuf.CodedOutputStream</span>
<span class="nc" id="L1011">        .computeBoolSize(14, useNewCcs_);</span>
    }
<span class="nc bnc" id="L1013" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(productId_)) {</span>
<span class="nc" id="L1014">      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(15, productId_);</span>
    }
<span class="nc bnc" id="L1016" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(requestId_)) {</span>
<span class="nc" id="L1017">      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(16, requestId_);</span>
    }
<span class="nc bnc" id="L1019" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(traceId_)) {</span>
<span class="nc" id="L1020">      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(17, traceId_);</span>
    }
<span class="nc bnc" id="L1022" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(userId_)) {</span>
<span class="nc" id="L1023">      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(18, userId_);</span>
    }
<span class="nc bnc" id="L1025" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(authorization_)) {</span>
<span class="nc" id="L1026">      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(19, authorization_);</span>
    }
<span class="nc bnc" id="L1028" title="All 2 branches missed.">    if (checkEntitlement_ != false) {</span>
<span class="nc" id="L1029">      size += com.google.protobuf.CodedOutputStream</span>
<span class="nc" id="L1030">        .computeBoolSize(20, checkEntitlement_);</span>
    }
<span class="nc bnc" id="L1032" title="All 2 branches missed.">    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(entitlementProductId_)) {</span>
<span class="nc" id="L1033">      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(21, entitlementProductId_);</span>
    }
<span class="nc" id="L1035">    size += getUnknownFields().getSerializedSize();</span>
<span class="nc" id="L1036">    memoizedSize = size;</span>
<span class="nc" id="L1037">    return size;</span>
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
<span class="nc bnc" id="L1042" title="All 2 branches missed.">    if (obj == this) {</span>
<span class="nc" id="L1043">     return true;</span>
    }
<span class="nc bnc" id="L1045" title="All 2 branches missed.">    if (!(obj instanceof com.morningstar.martapi.grpc.TimeSeriesRequest)) {</span>
<span class="nc" id="L1046">      return super.equals(obj);</span>
    }
<span class="nc" id="L1048">    com.morningstar.martapi.grpc.TimeSeriesRequest other = (com.morningstar.martapi.grpc.TimeSeriesRequest) obj;</span>

<span class="nc" id="L1050">    if (!getInvestmentIdsList()</span>
<span class="nc bnc" id="L1051" title="All 2 branches missed.">        .equals(other.getInvestmentIdsList())) return false;</span>
<span class="nc" id="L1052">    if (!getDataPointsList()</span>
<span class="nc bnc" id="L1053" title="All 2 branches missed.">        .equals(other.getDataPointsList())) return false;</span>
<span class="nc" id="L1054">    if (!getStartDate()</span>
<span class="nc bnc" id="L1055" title="All 2 branches missed.">        .equals(other.getStartDate())) return false;</span>
<span class="nc" id="L1056">    if (!getEndDate()</span>
<span class="nc bnc" id="L1057" title="All 2 branches missed.">        .equals(other.getEndDate())) return false;</span>
<span class="nc" id="L1058">    if (!getCurrency()</span>
<span class="nc bnc" id="L1059" title="All 2 branches missed.">        .equals(other.getCurrency())) return false;</span>
<span class="nc" id="L1060">    if (!getPreCurrency()</span>
<span class="nc bnc" id="L1061" title="All 2 branches missed.">        .equals(other.getPreCurrency())) return false;</span>
<span class="nc" id="L1062">    if (!getReadCache()</span>
<span class="nc bnc" id="L1063" title="All 2 branches missed.">        .equals(other.getReadCache())) return false;</span>
<span class="nc" id="L1064">    if (!getDateFormat()</span>
<span class="nc bnc" id="L1065" title="All 2 branches missed.">        .equals(other.getDateFormat())) return false;</span>
<span class="nc" id="L1066">    if (!getDecimalFormat()</span>
<span class="nc bnc" id="L1067" title="All 2 branches missed.">        .equals(other.getDecimalFormat())) return false;</span>
<span class="nc" id="L1068">    if (!getExtendPerformance()</span>
<span class="nc bnc" id="L1069" title="All 2 branches missed.">        .equals(other.getExtendPerformance())) return false;</span>
<span class="nc" id="L1070">    if (!getPostTax()</span>
<span class="nc bnc" id="L1071" title="All 2 branches missed.">        .equals(other.getPostTax())) return false;</span>
<span class="nc" id="L1072">    if (getUseRequireId()</span>
<span class="nc bnc" id="L1073" title="All 2 branches missed.">        != other.getUseRequireId()) return false;</span>
<span class="nc" id="L1074">    if (!getUseCase()</span>
<span class="nc bnc" id="L1075" title="All 2 branches missed.">        .equals(other.getUseCase())) return false;</span>
<span class="nc" id="L1076">    if (getUseNewCcs()</span>
<span class="nc bnc" id="L1077" title="All 2 branches missed.">        != other.getUseNewCcs()) return false;</span>
<span class="nc" id="L1078">    if (!getProductId()</span>
<span class="nc bnc" id="L1079" title="All 2 branches missed.">        .equals(other.getProductId())) return false;</span>
<span class="nc" id="L1080">    if (!getRequestId()</span>
<span class="nc bnc" id="L1081" title="All 2 branches missed.">        .equals(other.getRequestId())) return false;</span>
<span class="nc" id="L1082">    if (!getTraceId()</span>
<span class="nc bnc" id="L1083" title="All 2 branches missed.">        .equals(other.getTraceId())) return false;</span>
<span class="nc" id="L1084">    if (!getUserId()</span>
<span class="nc bnc" id="L1085" title="All 2 branches missed.">        .equals(other.getUserId())) return false;</span>
<span class="nc" id="L1086">    if (!getAuthorization()</span>
<span class="nc bnc" id="L1087" title="All 2 branches missed.">        .equals(other.getAuthorization())) return false;</span>
<span class="nc" id="L1088">    if (getCheckEntitlement()</span>
<span class="nc bnc" id="L1089" title="All 2 branches missed.">        != other.getCheckEntitlement()) return false;</span>
<span class="nc" id="L1090">    if (!getEntitlementProductId()</span>
<span class="nc bnc" id="L1091" title="All 2 branches missed.">        .equals(other.getEntitlementProductId())) return false;</span>
<span class="nc bnc" id="L1092" title="All 2 branches missed.">    if (!getUnknownFields().equals(other.getUnknownFields())) return false;</span>
<span class="nc" id="L1093">    return true;</span>
  }

  @java.lang.Override
  public int hashCode() {
<span class="nc bnc" id="L1098" title="All 2 branches missed.">    if (memoizedHashCode != 0) {</span>
<span class="nc" id="L1099">      return memoizedHashCode;</span>
    }
<span class="nc" id="L1101">    int hash = 41;</span>
<span class="nc" id="L1102">    hash = (19 * hash) + getDescriptor().hashCode();</span>
<span class="nc bnc" id="L1103" title="All 2 branches missed.">    if (getInvestmentIdsCount() &gt; 0) {</span>
<span class="nc" id="L1104">      hash = (37 * hash) + INVESTMENT_IDS_FIELD_NUMBER;</span>
<span class="nc" id="L1105">      hash = (53 * hash) + getInvestmentIdsList().hashCode();</span>
    }
<span class="nc bnc" id="L1107" title="All 2 branches missed.">    if (getDataPointsCount() &gt; 0) {</span>
<span class="nc" id="L1108">      hash = (37 * hash) + DATA_POINTS_FIELD_NUMBER;</span>
<span class="nc" id="L1109">      hash = (53 * hash) + getDataPointsList().hashCode();</span>
    }
<span class="nc" id="L1111">    hash = (37 * hash) + START_DATE_FIELD_NUMBER;</span>
<span class="nc" id="L1112">    hash = (53 * hash) + getStartDate().hashCode();</span>
<span class="nc" id="L1113">    hash = (37 * hash) + END_DATE_FIELD_NUMBER;</span>
<span class="nc" id="L1114">    hash = (53 * hash) + getEndDate().hashCode();</span>
<span class="nc" id="L1115">    hash = (37 * hash) + CURRENCY_FIELD_NUMBER;</span>
<span class="nc" id="L1116">    hash = (53 * hash) + getCurrency().hashCode();</span>
<span class="nc" id="L1117">    hash = (37 * hash) + PRE_CURRENCY_FIELD_NUMBER;</span>
<span class="nc" id="L1118">    hash = (53 * hash) + getPreCurrency().hashCode();</span>
<span class="nc" id="L1119">    hash = (37 * hash) + READ_CACHE_FIELD_NUMBER;</span>
<span class="nc" id="L1120">    hash = (53 * hash) + getReadCache().hashCode();</span>
<span class="nc" id="L1121">    hash = (37 * hash) + DATE_FORMAT_FIELD_NUMBER;</span>
<span class="nc" id="L1122">    hash = (53 * hash) + getDateFormat().hashCode();</span>
<span class="nc" id="L1123">    hash = (37 * hash) + DECIMAL_FORMAT_FIELD_NUMBER;</span>
<span class="nc" id="L1124">    hash = (53 * hash) + getDecimalFormat().hashCode();</span>
<span class="nc" id="L1125">    hash = (37 * hash) + EXTEND_PERFORMANCE_FIELD_NUMBER;</span>
<span class="nc" id="L1126">    hash = (53 * hash) + getExtendPerformance().hashCode();</span>
<span class="nc" id="L1127">    hash = (37 * hash) + POST_TAX_FIELD_NUMBER;</span>
<span class="nc" id="L1128">    hash = (53 * hash) + getPostTax().hashCode();</span>
<span class="nc" id="L1129">    hash = (37 * hash) + USE_REQUIRE_ID_FIELD_NUMBER;</span>
<span class="nc" id="L1130">    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(</span>
<span class="nc" id="L1131">        getUseRequireId());</span>
<span class="nc" id="L1132">    hash = (37 * hash) + USE_CASE_FIELD_NUMBER;</span>
<span class="nc" id="L1133">    hash = (53 * hash) + getUseCase().hashCode();</span>
<span class="nc" id="L1134">    hash = (37 * hash) + USE_NEW_CCS_FIELD_NUMBER;</span>
<span class="nc" id="L1135">    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(</span>
<span class="nc" id="L1136">        getUseNewCcs());</span>
<span class="nc" id="L1137">    hash = (37 * hash) + PRODUCT_ID_FIELD_NUMBER;</span>
<span class="nc" id="L1138">    hash = (53 * hash) + getProductId().hashCode();</span>
<span class="nc" id="L1139">    hash = (37 * hash) + REQUEST_ID_FIELD_NUMBER;</span>
<span class="nc" id="L1140">    hash = (53 * hash) + getRequestId().hashCode();</span>
<span class="nc" id="L1141">    hash = (37 * hash) + TRACE_ID_FIELD_NUMBER;</span>
<span class="nc" id="L1142">    hash = (53 * hash) + getTraceId().hashCode();</span>
<span class="nc" id="L1143">    hash = (37 * hash) + USER_ID_FIELD_NUMBER;</span>
<span class="nc" id="L1144">    hash = (53 * hash) + getUserId().hashCode();</span>
<span class="nc" id="L1145">    hash = (37 * hash) + AUTHORIZATION_FIELD_NUMBER;</span>
<span class="nc" id="L1146">    hash = (53 * hash) + getAuthorization().hashCode();</span>
<span class="nc" id="L1147">    hash = (37 * hash) + CHECK_ENTITLEMENT_FIELD_NUMBER;</span>
<span class="nc" id="L1148">    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(</span>
<span class="nc" id="L1149">        getCheckEntitlement());</span>
<span class="nc" id="L1150">    hash = (37 * hash) + ENTITLEMENT_PRODUCT_ID_FIELD_NUMBER;</span>
<span class="nc" id="L1151">    hash = (53 * hash) + getEntitlementProductId().hashCode();</span>
<span class="nc" id="L1152">    hash = (29 * hash) + getUnknownFields().hashCode();</span>
<span class="nc" id="L1153">    memoizedHashCode = hash;</span>
<span class="nc" id="L1154">    return hash;</span>
  }

  public static com.morningstar.martapi.grpc.TimeSeriesRequest parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L1160">    return PARSER.parseFrom(data);</span>
  }
  public static com.morningstar.martapi.grpc.TimeSeriesRequest parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L1166">    return PARSER.parseFrom(data, extensionRegistry);</span>
  }
  public static com.morningstar.martapi.grpc.TimeSeriesRequest parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L1171">    return PARSER.parseFrom(data);</span>
  }
  public static com.morningstar.martapi.grpc.TimeSeriesRequest parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L1177">    return PARSER.parseFrom(data, extensionRegistry);</span>
  }
  public static com.morningstar.martapi.grpc.TimeSeriesRequest parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L1181">    return PARSER.parseFrom(data);</span>
  }
  public static com.morningstar.martapi.grpc.TimeSeriesRequest parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L1187">    return PARSER.parseFrom(data, extensionRegistry);</span>
  }
  public static com.morningstar.martapi.grpc.TimeSeriesRequest parseFrom(java.io.InputStream input)
      throws java.io.IOException {
<span class="nc" id="L1191">    return com.google.protobuf.GeneratedMessageV3</span>
<span class="nc" id="L1192">        .parseWithIOException(PARSER, input);</span>
  }
  public static com.morningstar.martapi.grpc.TimeSeriesRequest parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
<span class="nc" id="L1198">    return com.google.protobuf.GeneratedMessageV3</span>
<span class="nc" id="L1199">        .parseWithIOException(PARSER, input, extensionRegistry);</span>
  }

  public static com.morningstar.martapi.grpc.TimeSeriesRequest parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
<span class="nc" id="L1204">    return com.google.protobuf.GeneratedMessageV3</span>
<span class="nc" id="L1205">        .parseDelimitedWithIOException(PARSER, input);</span>
  }

  public static com.morningstar.martapi.grpc.TimeSeriesRequest parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
<span class="nc" id="L1212">    return com.google.protobuf.GeneratedMessageV3</span>
<span class="nc" id="L1213">        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);</span>
  }
  public static com.morningstar.martapi.grpc.TimeSeriesRequest parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
<span class="nc" id="L1218">    return com.google.protobuf.GeneratedMessageV3</span>
<span class="nc" id="L1219">        .parseWithIOException(PARSER, input);</span>
  }
  public static com.morningstar.martapi.grpc.TimeSeriesRequest parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
<span class="nc" id="L1225">    return com.google.protobuf.GeneratedMessageV3</span>
<span class="nc" id="L1226">        .parseWithIOException(PARSER, input, extensionRegistry);</span>
  }

  @java.lang.Override
<span class="nc" id="L1230">  public Builder newBuilderForType() { return newBuilder(); }</span>
  public static Builder newBuilder() {
<span class="nc" id="L1232">    return DEFAULT_INSTANCE.toBuilder();</span>
  }
  public static Builder newBuilder(com.morningstar.martapi.grpc.TimeSeriesRequest prototype) {
<span class="nc" id="L1235">    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);</span>
  }
  @java.lang.Override
  public Builder toBuilder() {
<span class="nc bnc" id="L1239" title="All 2 branches missed.">    return this == DEFAULT_INSTANCE</span>
<span class="nc" id="L1240">        ? new Builder() : new Builder().mergeFrom(this);</span>
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
<span class="nc" id="L1246">    Builder builder = new Builder(parent);</span>
<span class="nc" id="L1247">    return builder;</span>
  }
  /**
   * &lt;pre&gt;
   * Request message for time series data
   * &lt;/pre&gt;
   *
   * Protobuf type {@code timeseries.TimeSeriesRequest}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder&lt;Builder&gt; implements
      // @@protoc_insertion_point(builder_implements:timeseries.TimeSeriesRequest)
      com.morningstar.martapi.grpc.TimeSeriesRequestOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
<span class="nc" id="L1262">      return com.morningstar.martapi.grpc.TimeSeriesServiceProto.internal_static_timeseries_TimeSeriesRequest_descriptor;</span>
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
<span class="nc" id="L1268">      return com.morningstar.martapi.grpc.TimeSeriesServiceProto.internal_static_timeseries_TimeSeriesRequest_fieldAccessorTable</span>
<span class="nc" id="L1269">          .ensureFieldAccessorsInitialized(</span>
              com.morningstar.martapi.grpc.TimeSeriesRequest.class, com.morningstar.martapi.grpc.TimeSeriesRequest.Builder.class);
    }

    // Construct using com.morningstar.martapi.grpc.TimeSeriesRequest.newBuilder()
<span class="nc" id="L1274">    private Builder() {</span>

<span class="nc" id="L1276">    }</span>

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
<span class="nc" id="L1280">      super(parent);</span>

<span class="nc" id="L1282">    }</span>
    @java.lang.Override
    public Builder clear() {
<span class="nc" id="L1285">      super.clear();</span>
<span class="nc" id="L1286">      bitField0_ = 0;</span>
<span class="nc" id="L1287">      investmentIds_ =</span>
<span class="nc" id="L1288">          com.google.protobuf.LazyStringArrayList.emptyList();</span>
<span class="nc" id="L1289">      dataPoints_ =</span>
<span class="nc" id="L1290">          com.google.protobuf.LazyStringArrayList.emptyList();</span>
<span class="nc" id="L1291">      startDate_ = &quot;&quot;;</span>
<span class="nc" id="L1292">      endDate_ = &quot;&quot;;</span>
<span class="nc" id="L1293">      currency_ = &quot;&quot;;</span>
<span class="nc" id="L1294">      preCurrency_ = &quot;&quot;;</span>
<span class="nc" id="L1295">      readCache_ = &quot;&quot;;</span>
<span class="nc" id="L1296">      dateFormat_ = &quot;&quot;;</span>
<span class="nc" id="L1297">      decimalFormat_ = &quot;&quot;;</span>
<span class="nc" id="L1298">      extendPerformance_ = &quot;&quot;;</span>
<span class="nc" id="L1299">      postTax_ = &quot;&quot;;</span>
<span class="nc" id="L1300">      useRequireId_ = false;</span>
<span class="nc" id="L1301">      useCase_ = &quot;&quot;;</span>
<span class="nc" id="L1302">      useNewCcs_ = false;</span>
<span class="nc" id="L1303">      productId_ = &quot;&quot;;</span>
<span class="nc" id="L1304">      requestId_ = &quot;&quot;;</span>
<span class="nc" id="L1305">      traceId_ = &quot;&quot;;</span>
<span class="nc" id="L1306">      userId_ = &quot;&quot;;</span>
<span class="nc" id="L1307">      authorization_ = &quot;&quot;;</span>
<span class="nc" id="L1308">      checkEntitlement_ = false;</span>
<span class="nc" id="L1309">      entitlementProductId_ = &quot;&quot;;</span>
<span class="nc" id="L1310">      return this;</span>
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
<span class="nc" id="L1316">      return com.morningstar.martapi.grpc.TimeSeriesServiceProto.internal_static_timeseries_TimeSeriesRequest_descriptor;</span>
    }

    @java.lang.Override
    public com.morningstar.martapi.grpc.TimeSeriesRequest getDefaultInstanceForType() {
<span class="nc" id="L1321">      return com.morningstar.martapi.grpc.TimeSeriesRequest.getDefaultInstance();</span>
    }

    @java.lang.Override
    public com.morningstar.martapi.grpc.TimeSeriesRequest build() {
<span class="nc" id="L1326">      com.morningstar.martapi.grpc.TimeSeriesRequest result = buildPartial();</span>
<span class="nc bnc" id="L1327" title="All 2 branches missed.">      if (!result.isInitialized()) {</span>
<span class="nc" id="L1328">        throw newUninitializedMessageException(result);</span>
      }
<span class="nc" id="L1330">      return result;</span>
    }

    @java.lang.Override
    public com.morningstar.martapi.grpc.TimeSeriesRequest buildPartial() {
<span class="nc" id="L1335">      com.morningstar.martapi.grpc.TimeSeriesRequest result = new com.morningstar.martapi.grpc.TimeSeriesRequest(this);</span>
<span class="nc bnc" id="L1336" title="All 2 branches missed.">      if (bitField0_ != 0) { buildPartial0(result); }</span>
<span class="nc" id="L1337">      onBuilt();</span>
<span class="nc" id="L1338">      return result;</span>
    }

    private void buildPartial0(com.morningstar.martapi.grpc.TimeSeriesRequest result) {
<span class="nc" id="L1342">      int from_bitField0_ = bitField0_;</span>
<span class="nc bnc" id="L1343" title="All 2 branches missed.">      if (((from_bitField0_ &amp; 0x00000001) != 0)) {</span>
<span class="nc" id="L1344">        investmentIds_.makeImmutable();</span>
<span class="nc" id="L1345">        result.investmentIds_ = investmentIds_;</span>
      }
<span class="nc bnc" id="L1347" title="All 2 branches missed.">      if (((from_bitField0_ &amp; 0x00000002) != 0)) {</span>
<span class="nc" id="L1348">        dataPoints_.makeImmutable();</span>
<span class="nc" id="L1349">        result.dataPoints_ = dataPoints_;</span>
      }
<span class="nc bnc" id="L1351" title="All 2 branches missed.">      if (((from_bitField0_ &amp; 0x00000004) != 0)) {</span>
<span class="nc" id="L1352">        result.startDate_ = startDate_;</span>
      }
<span class="nc bnc" id="L1354" title="All 2 branches missed.">      if (((from_bitField0_ &amp; 0x00000008) != 0)) {</span>
<span class="nc" id="L1355">        result.endDate_ = endDate_;</span>
      }
<span class="nc bnc" id="L1357" title="All 2 branches missed.">      if (((from_bitField0_ &amp; 0x00000010) != 0)) {</span>
<span class="nc" id="L1358">        result.currency_ = currency_;</span>
      }
<span class="nc bnc" id="L1360" title="All 2 branches missed.">      if (((from_bitField0_ &amp; 0x00000020) != 0)) {</span>
<span class="nc" id="L1361">        result.preCurrency_ = preCurrency_;</span>
      }
<span class="nc bnc" id="L1363" title="All 2 branches missed.">      if (((from_bitField0_ &amp; 0x00000040) != 0)) {</span>
<span class="nc" id="L1364">        result.readCache_ = readCache_;</span>
      }
<span class="nc bnc" id="L1366" title="All 2 branches missed.">      if (((from_bitField0_ &amp; 0x00000080) != 0)) {</span>
<span class="nc" id="L1367">        result.dateFormat_ = dateFormat_;</span>
      }
<span class="nc bnc" id="L1369" title="All 2 branches missed.">      if (((from_bitField0_ &amp; 0x00000100) != 0)) {</span>
<span class="nc" id="L1370">        result.decimalFormat_ = decimalFormat_;</span>
      }
<span class="nc bnc" id="L1372" title="All 2 branches missed.">      if (((from_bitField0_ &amp; 0x00000200) != 0)) {</span>
<span class="nc" id="L1373">        result.extendPerformance_ = extendPerformance_;</span>
      }
<span class="nc bnc" id="L1375" title="All 2 branches missed.">      if (((from_bitField0_ &amp; 0x00000400) != 0)) {</span>
<span class="nc" id="L1376">        result.postTax_ = postTax_;</span>
      }
<span class="nc bnc" id="L1378" title="All 2 branches missed.">      if (((from_bitField0_ &amp; 0x00000800) != 0)) {</span>
<span class="nc" id="L1379">        result.useRequireId_ = useRequireId_;</span>
      }
<span class="nc bnc" id="L1381" title="All 2 branches missed.">      if (((from_bitField0_ &amp; 0x00001000) != 0)) {</span>
<span class="nc" id="L1382">        result.useCase_ = useCase_;</span>
      }
<span class="nc bnc" id="L1384" title="All 2 branches missed.">      if (((from_bitField0_ &amp; 0x00002000) != 0)) {</span>
<span class="nc" id="L1385">        result.useNewCcs_ = useNewCcs_;</span>
      }
<span class="nc bnc" id="L1387" title="All 2 branches missed.">      if (((from_bitField0_ &amp; 0x00004000) != 0)) {</span>
<span class="nc" id="L1388">        result.productId_ = productId_;</span>
      }
<span class="nc bnc" id="L1390" title="All 2 branches missed.">      if (((from_bitField0_ &amp; 0x00008000) != 0)) {</span>
<span class="nc" id="L1391">        result.requestId_ = requestId_;</span>
      }
<span class="nc bnc" id="L1393" title="All 2 branches missed.">      if (((from_bitField0_ &amp; 0x00010000) != 0)) {</span>
<span class="nc" id="L1394">        result.traceId_ = traceId_;</span>
      }
<span class="nc bnc" id="L1396" title="All 2 branches missed.">      if (((from_bitField0_ &amp; 0x00020000) != 0)) {</span>
<span class="nc" id="L1397">        result.userId_ = userId_;</span>
      }
<span class="nc bnc" id="L1399" title="All 2 branches missed.">      if (((from_bitField0_ &amp; 0x00040000) != 0)) {</span>
<span class="nc" id="L1400">        result.authorization_ = authorization_;</span>
      }
<span class="nc bnc" id="L1402" title="All 2 branches missed.">      if (((from_bitField0_ &amp; 0x00080000) != 0)) {</span>
<span class="nc" id="L1403">        result.checkEntitlement_ = checkEntitlement_;</span>
      }
<span class="nc bnc" id="L1405" title="All 2 branches missed.">      if (((from_bitField0_ &amp; 0x00100000) != 0)) {</span>
<span class="nc" id="L1406">        result.entitlementProductId_ = entitlementProductId_;</span>
      }
<span class="nc" id="L1408">    }</span>

    @java.lang.Override
    public Builder clone() {
<span class="nc" id="L1412">      return super.clone();</span>
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
<span class="nc" id="L1418">      return super.setField(field, value);</span>
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
<span class="nc" id="L1423">      return super.clearField(field);</span>
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
<span class="nc" id="L1428">      return super.clearOneof(oneof);</span>
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
<span class="nc" id="L1434">      return super.setRepeatedField(field, index, value);</span>
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
<span class="nc" id="L1440">      return super.addRepeatedField(field, value);</span>
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
<span class="nc bnc" id="L1444" title="All 2 branches missed.">      if (other instanceof com.morningstar.martapi.grpc.TimeSeriesRequest) {</span>
<span class="nc" id="L1445">        return mergeFrom((com.morningstar.martapi.grpc.TimeSeriesRequest)other);</span>
      } else {
<span class="nc" id="L1447">        super.mergeFrom(other);</span>
<span class="nc" id="L1448">        return this;</span>
      }
    }

    public Builder mergeFrom(com.morningstar.martapi.grpc.TimeSeriesRequest other) {
<span class="nc bnc" id="L1453" title="All 2 branches missed.">      if (other == com.morningstar.martapi.grpc.TimeSeriesRequest.getDefaultInstance()) return this;</span>
<span class="nc bnc" id="L1454" title="All 2 branches missed.">      if (!other.investmentIds_.isEmpty()) {</span>
<span class="nc bnc" id="L1455" title="All 2 branches missed.">        if (investmentIds_.isEmpty()) {</span>
<span class="nc" id="L1456">          investmentIds_ = other.investmentIds_;</span>
<span class="nc" id="L1457">          bitField0_ |= 0x00000001;</span>
        } else {
<span class="nc" id="L1459">          ensureInvestmentIdsIsMutable();</span>
<span class="nc" id="L1460">          investmentIds_.addAll(other.investmentIds_);</span>
        }
<span class="nc" id="L1462">        onChanged();</span>
      }
<span class="nc bnc" id="L1464" title="All 2 branches missed.">      if (!other.dataPoints_.isEmpty()) {</span>
<span class="nc bnc" id="L1465" title="All 2 branches missed.">        if (dataPoints_.isEmpty()) {</span>
<span class="nc" id="L1466">          dataPoints_ = other.dataPoints_;</span>
<span class="nc" id="L1467">          bitField0_ |= 0x00000002;</span>
        } else {
<span class="nc" id="L1469">          ensureDataPointsIsMutable();</span>
<span class="nc" id="L1470">          dataPoints_.addAll(other.dataPoints_);</span>
        }
<span class="nc" id="L1472">        onChanged();</span>
      }
<span class="nc bnc" id="L1474" title="All 2 branches missed.">      if (!other.getStartDate().isEmpty()) {</span>
<span class="nc" id="L1475">        startDate_ = other.startDate_;</span>
<span class="nc" id="L1476">        bitField0_ |= 0x00000004;</span>
<span class="nc" id="L1477">        onChanged();</span>
      }
<span class="nc bnc" id="L1479" title="All 2 branches missed.">      if (!other.getEndDate().isEmpty()) {</span>
<span class="nc" id="L1480">        endDate_ = other.endDate_;</span>
<span class="nc" id="L1481">        bitField0_ |= 0x00000008;</span>
<span class="nc" id="L1482">        onChanged();</span>
      }
<span class="nc bnc" id="L1484" title="All 2 branches missed.">      if (!other.getCurrency().isEmpty()) {</span>
<span class="nc" id="L1485">        currency_ = other.currency_;</span>
<span class="nc" id="L1486">        bitField0_ |= 0x00000010;</span>
<span class="nc" id="L1487">        onChanged();</span>
      }
<span class="nc bnc" id="L1489" title="All 2 branches missed.">      if (!other.getPreCurrency().isEmpty()) {</span>
<span class="nc" id="L1490">        preCurrency_ = other.preCurrency_;</span>
<span class="nc" id="L1491">        bitField0_ |= 0x00000020;</span>
<span class="nc" id="L1492">        onChanged();</span>
      }
<span class="nc bnc" id="L1494" title="All 2 branches missed.">      if (!other.getReadCache().isEmpty()) {</span>
<span class="nc" id="L1495">        readCache_ = other.readCache_;</span>
<span class="nc" id="L1496">        bitField0_ |= 0x00000040;</span>
<span class="nc" id="L1497">        onChanged();</span>
      }
<span class="nc bnc" id="L1499" title="All 2 branches missed.">      if (!other.getDateFormat().isEmpty()) {</span>
<span class="nc" id="L1500">        dateFormat_ = other.dateFormat_;</span>
<span class="nc" id="L1501">        bitField0_ |= 0x00000080;</span>
<span class="nc" id="L1502">        onChanged();</span>
      }
<span class="nc bnc" id="L1504" title="All 2 branches missed.">      if (!other.getDecimalFormat().isEmpty()) {</span>
<span class="nc" id="L1505">        decimalFormat_ = other.decimalFormat_;</span>
<span class="nc" id="L1506">        bitField0_ |= 0x00000100;</span>
<span class="nc" id="L1507">        onChanged();</span>
      }
<span class="nc bnc" id="L1509" title="All 2 branches missed.">      if (!other.getExtendPerformance().isEmpty()) {</span>
<span class="nc" id="L1510">        extendPerformance_ = other.extendPerformance_;</span>
<span class="nc" id="L1511">        bitField0_ |= 0x00000200;</span>
<span class="nc" id="L1512">        onChanged();</span>
      }
<span class="nc bnc" id="L1514" title="All 2 branches missed.">      if (!other.getPostTax().isEmpty()) {</span>
<span class="nc" id="L1515">        postTax_ = other.postTax_;</span>
<span class="nc" id="L1516">        bitField0_ |= 0x00000400;</span>
<span class="nc" id="L1517">        onChanged();</span>
      }
<span class="nc bnc" id="L1519" title="All 2 branches missed.">      if (other.getUseRequireId() != false) {</span>
<span class="nc" id="L1520">        setUseRequireId(other.getUseRequireId());</span>
      }
<span class="nc bnc" id="L1522" title="All 2 branches missed.">      if (!other.getUseCase().isEmpty()) {</span>
<span class="nc" id="L1523">        useCase_ = other.useCase_;</span>
<span class="nc" id="L1524">        bitField0_ |= 0x00001000;</span>
<span class="nc" id="L1525">        onChanged();</span>
      }
<span class="nc bnc" id="L1527" title="All 2 branches missed.">      if (other.getUseNewCcs() != false) {</span>
<span class="nc" id="L1528">        setUseNewCcs(other.getUseNewCcs());</span>
      }
<span class="nc bnc" id="L1530" title="All 2 branches missed.">      if (!other.getProductId().isEmpty()) {</span>
<span class="nc" id="L1531">        productId_ = other.productId_;</span>
<span class="nc" id="L1532">        bitField0_ |= 0x00004000;</span>
<span class="nc" id="L1533">        onChanged();</span>
      }
<span class="nc bnc" id="L1535" title="All 2 branches missed.">      if (!other.getRequestId().isEmpty()) {</span>
<span class="nc" id="L1536">        requestId_ = other.requestId_;</span>
<span class="nc" id="L1537">        bitField0_ |= 0x00008000;</span>
<span class="nc" id="L1538">        onChanged();</span>
      }
<span class="nc bnc" id="L1540" title="All 2 branches missed.">      if (!other.getTraceId().isEmpty()) {</span>
<span class="nc" id="L1541">        traceId_ = other.traceId_;</span>
<span class="nc" id="L1542">        bitField0_ |= 0x00010000;</span>
<span class="nc" id="L1543">        onChanged();</span>
      }
<span class="nc bnc" id="L1545" title="All 2 branches missed.">      if (!other.getUserId().isEmpty()) {</span>
<span class="nc" id="L1546">        userId_ = other.userId_;</span>
<span class="nc" id="L1547">        bitField0_ |= 0x00020000;</span>
<span class="nc" id="L1548">        onChanged();</span>
      }
<span class="nc bnc" id="L1550" title="All 2 branches missed.">      if (!other.getAuthorization().isEmpty()) {</span>
<span class="nc" id="L1551">        authorization_ = other.authorization_;</span>
<span class="nc" id="L1552">        bitField0_ |= 0x00040000;</span>
<span class="nc" id="L1553">        onChanged();</span>
      }
<span class="nc bnc" id="L1555" title="All 2 branches missed.">      if (other.getCheckEntitlement() != false) {</span>
<span class="nc" id="L1556">        setCheckEntitlement(other.getCheckEntitlement());</span>
      }
<span class="nc bnc" id="L1558" title="All 2 branches missed.">      if (!other.getEntitlementProductId().isEmpty()) {</span>
<span class="nc" id="L1559">        entitlementProductId_ = other.entitlementProductId_;</span>
<span class="nc" id="L1560">        bitField0_ |= 0x00100000;</span>
<span class="nc" id="L1561">        onChanged();</span>
      }
<span class="nc" id="L1563">      this.mergeUnknownFields(other.getUnknownFields());</span>
<span class="nc" id="L1564">      onChanged();</span>
<span class="nc" id="L1565">      return this;</span>
    }

    @java.lang.Override
    public final boolean isInitialized() {
<span class="nc" id="L1570">      return true;</span>
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
<span class="nc bnc" id="L1578" title="All 2 branches missed.">      if (extensionRegistry == null) {</span>
<span class="nc" id="L1579">        throw new java.lang.NullPointerException();</span>
      }
      try {
<span class="nc" id="L1582">        boolean done = false;</span>
<span class="nc bnc" id="L1583" title="All 2 branches missed.">        while (!done) {</span>
<span class="nc" id="L1584">          int tag = input.readTag();</span>
<span class="nc bnc" id="L1585" title="All 23 branches missed.">          switch (tag) {</span>
            case 0:
<span class="nc" id="L1587">              done = true;</span>
<span class="nc" id="L1588">              break;</span>
            case 10: {
<span class="nc" id="L1590">              java.lang.String s = input.readStringRequireUtf8();</span>
<span class="nc" id="L1591">              ensureInvestmentIdsIsMutable();</span>
<span class="nc" id="L1592">              investmentIds_.add(s);</span>
<span class="nc" id="L1593">              break;</span>
            } // case 10
            case 18: {
<span class="nc" id="L1596">              java.lang.String s = input.readStringRequireUtf8();</span>
<span class="nc" id="L1597">              ensureDataPointsIsMutable();</span>
<span class="nc" id="L1598">              dataPoints_.add(s);</span>
<span class="nc" id="L1599">              break;</span>
            } // case 18
            case 26: {
<span class="nc" id="L1602">              startDate_ = input.readStringRequireUtf8();</span>
<span class="nc" id="L1603">              bitField0_ |= 0x00000004;</span>
<span class="nc" id="L1604">              break;</span>
            } // case 26
            case 34: {
<span class="nc" id="L1607">              endDate_ = input.readStringRequireUtf8();</span>
<span class="nc" id="L1608">              bitField0_ |= 0x00000008;</span>
<span class="nc" id="L1609">              break;</span>
            } // case 34
            case 42: {
<span class="nc" id="L1612">              currency_ = input.readStringRequireUtf8();</span>
<span class="nc" id="L1613">              bitField0_ |= 0x00000010;</span>
<span class="nc" id="L1614">              break;</span>
            } // case 42
            case 50: {
<span class="nc" id="L1617">              preCurrency_ = input.readStringRequireUtf8();</span>
<span class="nc" id="L1618">              bitField0_ |= 0x00000020;</span>
<span class="nc" id="L1619">              break;</span>
            } // case 50
            case 58: {
<span class="nc" id="L1622">              readCache_ = input.readStringRequireUtf8();</span>
<span class="nc" id="L1623">              bitField0_ |= 0x00000040;</span>
<span class="nc" id="L1624">              break;</span>
            } // case 58
            case 66: {
<span class="nc" id="L1627">              dateFormat_ = input.readStringRequireUtf8();</span>
<span class="nc" id="L1628">              bitField0_ |= 0x00000080;</span>
<span class="nc" id="L1629">              break;</span>
            } // case 66
            case 74: {
<span class="nc" id="L1632">              decimalFormat_ = input.readStringRequireUtf8();</span>
<span class="nc" id="L1633">              bitField0_ |= 0x00000100;</span>
<span class="nc" id="L1634">              break;</span>
            } // case 74
            case 82: {
<span class="nc" id="L1637">              extendPerformance_ = input.readStringRequireUtf8();</span>
<span class="nc" id="L1638">              bitField0_ |= 0x00000200;</span>
<span class="nc" id="L1639">              break;</span>
            } // case 82
            case 90: {
<span class="nc" id="L1642">              postTax_ = input.readStringRequireUtf8();</span>
<span class="nc" id="L1643">              bitField0_ |= 0x00000400;</span>
<span class="nc" id="L1644">              break;</span>
            } // case 90
            case 96: {
<span class="nc" id="L1647">              useRequireId_ = input.readBool();</span>
<span class="nc" id="L1648">              bitField0_ |= 0x00000800;</span>
<span class="nc" id="L1649">              break;</span>
            } // case 96
            case 106: {
<span class="nc" id="L1652">              useCase_ = input.readStringRequireUtf8();</span>
<span class="nc" id="L1653">              bitField0_ |= 0x00001000;</span>
<span class="nc" id="L1654">              break;</span>
            } // case 106
            case 112: {
<span class="nc" id="L1657">              useNewCcs_ = input.readBool();</span>
<span class="nc" id="L1658">              bitField0_ |= 0x00002000;</span>
<span class="nc" id="L1659">              break;</span>
            } // case 112
            case 122: {
<span class="nc" id="L1662">              productId_ = input.readStringRequireUtf8();</span>
<span class="nc" id="L1663">              bitField0_ |= 0x00004000;</span>
<span class="nc" id="L1664">              break;</span>
            } // case 122
            case 130: {
<span class="nc" id="L1667">              requestId_ = input.readStringRequireUtf8();</span>
<span class="nc" id="L1668">              bitField0_ |= 0x00008000;</span>
<span class="nc" id="L1669">              break;</span>
            } // case 130
            case 138: {
<span class="nc" id="L1672">              traceId_ = input.readStringRequireUtf8();</span>
<span class="nc" id="L1673">              bitField0_ |= 0x00010000;</span>
<span class="nc" id="L1674">              break;</span>
            } // case 138
            case 146: {
<span class="nc" id="L1677">              userId_ = input.readStringRequireUtf8();</span>
<span class="nc" id="L1678">              bitField0_ |= 0x00020000;</span>
<span class="nc" id="L1679">              break;</span>
            } // case 146
            case 154: {
<span class="nc" id="L1682">              authorization_ = input.readStringRequireUtf8();</span>
<span class="nc" id="L1683">              bitField0_ |= 0x00040000;</span>
<span class="nc" id="L1684">              break;</span>
            } // case 154
            case 160: {
<span class="nc" id="L1687">              checkEntitlement_ = input.readBool();</span>
<span class="nc" id="L1688">              bitField0_ |= 0x00080000;</span>
<span class="nc" id="L1689">              break;</span>
            } // case 160
            case 170: {
<span class="nc" id="L1692">              entitlementProductId_ = input.readStringRequireUtf8();</span>
<span class="nc" id="L1693">              bitField0_ |= 0x00100000;</span>
<span class="nc" id="L1694">              break;</span>
            } // case 170
            default: {
<span class="nc bnc" id="L1697" title="All 2 branches missed.">              if (!super.parseUnknownField(input, extensionRegistry, tag)) {</span>
<span class="nc" id="L1698">                done = true; // was an endgroup tag</span>
              }
              break;
            } // default:
          } // switch (tag)
<span class="nc" id="L1703">        } // while (!done)</span>
<span class="nc" id="L1704">      } catch (com.google.protobuf.InvalidProtocolBufferException e) {</span>
<span class="nc" id="L1705">        throw e.unwrapIOException();</span>
      } finally {
<span class="nc" id="L1707">        onChanged();</span>
      } // finally
<span class="nc" id="L1709">      return this;</span>
    }
    private int bitField0_;

<span class="nc" id="L1713">    private com.google.protobuf.LazyStringArrayList investmentIds_ =</span>
<span class="nc" id="L1714">        com.google.protobuf.LazyStringArrayList.emptyList();</span>
    private void ensureInvestmentIdsIsMutable() {
<span class="nc bnc" id="L1716" title="All 2 branches missed.">      if (!investmentIds_.isModifiable()) {</span>
<span class="nc" id="L1717">        investmentIds_ = new com.google.protobuf.LazyStringArrayList(investmentIds_);</span>
      }
<span class="nc" id="L1719">      bitField0_ |= 0x00000001;</span>
<span class="nc" id="L1720">    }</span>
    /**
     * &lt;pre&gt;
     * Investment IDs (comma-separated in REST, repeated here)
     * &lt;/pre&gt;
     *
     * &lt;code&gt;repeated string investment_ids = 1;&lt;/code&gt;
     * @return A list containing the investmentIds.
     */
    public com.google.protobuf.ProtocolStringList
        getInvestmentIdsList() {
<span class="nc" id="L1731">      investmentIds_.makeImmutable();</span>
<span class="nc" id="L1732">      return investmentIds_;</span>
    }
    /**
     * &lt;pre&gt;
     * Investment IDs (comma-separated in REST, repeated here)
     * &lt;/pre&gt;
     *
     * &lt;code&gt;repeated string investment_ids = 1;&lt;/code&gt;
     * @return The count of investmentIds.
     */
    public int getInvestmentIdsCount() {
<span class="nc" id="L1743">      return investmentIds_.size();</span>
    }
    /**
     * &lt;pre&gt;
     * Investment IDs (comma-separated in REST, repeated here)
     * &lt;/pre&gt;
     *
     * &lt;code&gt;repeated string investment_ids = 1;&lt;/code&gt;
     * @param index The index of the element to return.
     * @return The investmentIds at the given index.
     */
    public java.lang.String getInvestmentIds(int index) {
<span class="nc" id="L1755">      return investmentIds_.get(index);</span>
    }
    /**
     * &lt;pre&gt;
     * Investment IDs (comma-separated in REST, repeated here)
     * &lt;/pre&gt;
     *
     * &lt;code&gt;repeated string investment_ids = 1;&lt;/code&gt;
     * @param index The index of the value to return.
     * @return The bytes of the investmentIds at the given index.
     */
    public com.google.protobuf.ByteString
        getInvestmentIdsBytes(int index) {
<span class="nc" id="L1768">      return investmentIds_.getByteString(index);</span>
    }
    /**
     * &lt;pre&gt;
     * Investment IDs (comma-separated in REST, repeated here)
     * &lt;/pre&gt;
     *
     * &lt;code&gt;repeated string investment_ids = 1;&lt;/code&gt;
     * @param index The index to set the value at.
     * @param value The investmentIds to set.
     * @return This builder for chaining.
     */
    public Builder setInvestmentIds(
        int index, java.lang.String value) {
<span class="nc bnc" id="L1782" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L1783">      ensureInvestmentIdsIsMutable();</span>
<span class="nc" id="L1784">      investmentIds_.set(index, value);</span>
<span class="nc" id="L1785">      bitField0_ |= 0x00000001;</span>
<span class="nc" id="L1786">      onChanged();</span>
<span class="nc" id="L1787">      return this;</span>
    }
    /**
     * &lt;pre&gt;
     * Investment IDs (comma-separated in REST, repeated here)
     * &lt;/pre&gt;
     *
     * &lt;code&gt;repeated string investment_ids = 1;&lt;/code&gt;
     * @param value The investmentIds to add.
     * @return This builder for chaining.
     */
    public Builder addInvestmentIds(
        java.lang.String value) {
<span class="nc bnc" id="L1800" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L1801">      ensureInvestmentIdsIsMutable();</span>
<span class="nc" id="L1802">      investmentIds_.add(value);</span>
<span class="nc" id="L1803">      bitField0_ |= 0x00000001;</span>
<span class="nc" id="L1804">      onChanged();</span>
<span class="nc" id="L1805">      return this;</span>
    }
    /**
     * &lt;pre&gt;
     * Investment IDs (comma-separated in REST, repeated here)
     * &lt;/pre&gt;
     *
     * &lt;code&gt;repeated string investment_ids = 1;&lt;/code&gt;
     * @param values The investmentIds to add.
     * @return This builder for chaining.
     */
    public Builder addAllInvestmentIds(
        java.lang.Iterable&lt;java.lang.String&gt; values) {
<span class="nc" id="L1818">      ensureInvestmentIdsIsMutable();</span>
<span class="nc" id="L1819">      com.google.protobuf.AbstractMessageLite.Builder.addAll(</span>
          values, investmentIds_);
<span class="nc" id="L1821">      bitField0_ |= 0x00000001;</span>
<span class="nc" id="L1822">      onChanged();</span>
<span class="nc" id="L1823">      return this;</span>
    }
    /**
     * &lt;pre&gt;
     * Investment IDs (comma-separated in REST, repeated here)
     * &lt;/pre&gt;
     *
     * &lt;code&gt;repeated string investment_ids = 1;&lt;/code&gt;
     * @return This builder for chaining.
     */
    public Builder clearInvestmentIds() {
<span class="nc" id="L1834">      investmentIds_ =</span>
<span class="nc" id="L1835">        com.google.protobuf.LazyStringArrayList.emptyList();</span>
<span class="nc" id="L1836">      bitField0_ = (bitField0_ &amp; ~0x00000001);;</span>
<span class="nc" id="L1837">      onChanged();</span>
<span class="nc" id="L1838">      return this;</span>
    }
    /**
     * &lt;pre&gt;
     * Investment IDs (comma-separated in REST, repeated here)
     * &lt;/pre&gt;
     *
     * &lt;code&gt;repeated string investment_ids = 1;&lt;/code&gt;
     * @param value The bytes of the investmentIds to add.
     * @return This builder for chaining.
     */
    public Builder addInvestmentIdsBytes(
        com.google.protobuf.ByteString value) {
<span class="nc bnc" id="L1851" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L1852">      checkByteStringIsUtf8(value);</span>
<span class="nc" id="L1853">      ensureInvestmentIdsIsMutable();</span>
<span class="nc" id="L1854">      investmentIds_.add(value);</span>
<span class="nc" id="L1855">      bitField0_ |= 0x00000001;</span>
<span class="nc" id="L1856">      onChanged();</span>
<span class="nc" id="L1857">      return this;</span>
    }

<span class="nc" id="L1860">    private com.google.protobuf.LazyStringArrayList dataPoints_ =</span>
<span class="nc" id="L1861">        com.google.protobuf.LazyStringArrayList.emptyList();</span>
    private void ensureDataPointsIsMutable() {
<span class="nc bnc" id="L1863" title="All 2 branches missed.">      if (!dataPoints_.isModifiable()) {</span>
<span class="nc" id="L1864">        dataPoints_ = new com.google.protobuf.LazyStringArrayList(dataPoints_);</span>
      }
<span class="nc" id="L1866">      bitField0_ |= 0x00000002;</span>
<span class="nc" id="L1867">    }</span>
    /**
     * &lt;pre&gt;
     * Data points (comma-separated in REST, repeated here)
     * &lt;/pre&gt;
     *
     * &lt;code&gt;repeated string data_points = 2;&lt;/code&gt;
     * @return A list containing the dataPoints.
     */
    public com.google.protobuf.ProtocolStringList
        getDataPointsList() {
<span class="nc" id="L1878">      dataPoints_.makeImmutable();</span>
<span class="nc" id="L1879">      return dataPoints_;</span>
    }
    /**
     * &lt;pre&gt;
     * Data points (comma-separated in REST, repeated here)
     * &lt;/pre&gt;
     *
     * &lt;code&gt;repeated string data_points = 2;&lt;/code&gt;
     * @return The count of dataPoints.
     */
    public int getDataPointsCount() {
<span class="nc" id="L1890">      return dataPoints_.size();</span>
    }
    /**
     * &lt;pre&gt;
     * Data points (comma-separated in REST, repeated here)
     * &lt;/pre&gt;
     *
     * &lt;code&gt;repeated string data_points = 2;&lt;/code&gt;
     * @param index The index of the element to return.
     * @return The dataPoints at the given index.
     */
    public java.lang.String getDataPoints(int index) {
<span class="nc" id="L1902">      return dataPoints_.get(index);</span>
    }
    /**
     * &lt;pre&gt;
     * Data points (comma-separated in REST, repeated here)
     * &lt;/pre&gt;
     *
     * &lt;code&gt;repeated string data_points = 2;&lt;/code&gt;
     * @param index The index of the value to return.
     * @return The bytes of the dataPoints at the given index.
     */
    public com.google.protobuf.ByteString
        getDataPointsBytes(int index) {
<span class="nc" id="L1915">      return dataPoints_.getByteString(index);</span>
    }
    /**
     * &lt;pre&gt;
     * Data points (comma-separated in REST, repeated here)
     * &lt;/pre&gt;
     *
     * &lt;code&gt;repeated string data_points = 2;&lt;/code&gt;
     * @param index The index to set the value at.
     * @param value The dataPoints to set.
     * @return This builder for chaining.
     */
    public Builder setDataPoints(
        int index, java.lang.String value) {
<span class="nc bnc" id="L1929" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L1930">      ensureDataPointsIsMutable();</span>
<span class="nc" id="L1931">      dataPoints_.set(index, value);</span>
<span class="nc" id="L1932">      bitField0_ |= 0x00000002;</span>
<span class="nc" id="L1933">      onChanged();</span>
<span class="nc" id="L1934">      return this;</span>
    }
    /**
     * &lt;pre&gt;
     * Data points (comma-separated in REST, repeated here)
     * &lt;/pre&gt;
     *
     * &lt;code&gt;repeated string data_points = 2;&lt;/code&gt;
     * @param value The dataPoints to add.
     * @return This builder for chaining.
     */
    public Builder addDataPoints(
        java.lang.String value) {
<span class="nc bnc" id="L1947" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L1948">      ensureDataPointsIsMutable();</span>
<span class="nc" id="L1949">      dataPoints_.add(value);</span>
<span class="nc" id="L1950">      bitField0_ |= 0x00000002;</span>
<span class="nc" id="L1951">      onChanged();</span>
<span class="nc" id="L1952">      return this;</span>
    }
    /**
     * &lt;pre&gt;
     * Data points (comma-separated in REST, repeated here)
     * &lt;/pre&gt;
     *
     * &lt;code&gt;repeated string data_points = 2;&lt;/code&gt;
     * @param values The dataPoints to add.
     * @return This builder for chaining.
     */
    public Builder addAllDataPoints(
        java.lang.Iterable&lt;java.lang.String&gt; values) {
<span class="nc" id="L1965">      ensureDataPointsIsMutable();</span>
<span class="nc" id="L1966">      com.google.protobuf.AbstractMessageLite.Builder.addAll(</span>
          values, dataPoints_);
<span class="nc" id="L1968">      bitField0_ |= 0x00000002;</span>
<span class="nc" id="L1969">      onChanged();</span>
<span class="nc" id="L1970">      return this;</span>
    }
    /**
     * &lt;pre&gt;
     * Data points (comma-separated in REST, repeated here)
     * &lt;/pre&gt;
     *
     * &lt;code&gt;repeated string data_points = 2;&lt;/code&gt;
     * @return This builder for chaining.
     */
    public Builder clearDataPoints() {
<span class="nc" id="L1981">      dataPoints_ =</span>
<span class="nc" id="L1982">        com.google.protobuf.LazyStringArrayList.emptyList();</span>
<span class="nc" id="L1983">      bitField0_ = (bitField0_ &amp; ~0x00000002);;</span>
<span class="nc" id="L1984">      onChanged();</span>
<span class="nc" id="L1985">      return this;</span>
    }
    /**
     * &lt;pre&gt;
     * Data points (comma-separated in REST, repeated here)
     * &lt;/pre&gt;
     *
     * &lt;code&gt;repeated string data_points = 2;&lt;/code&gt;
     * @param value The bytes of the dataPoints to add.
     * @return This builder for chaining.
     */
    public Builder addDataPointsBytes(
        com.google.protobuf.ByteString value) {
<span class="nc bnc" id="L1998" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L1999">      checkByteStringIsUtf8(value);</span>
<span class="nc" id="L2000">      ensureDataPointsIsMutable();</span>
<span class="nc" id="L2001">      dataPoints_.add(value);</span>
<span class="nc" id="L2002">      bitField0_ |= 0x00000002;</span>
<span class="nc" id="L2003">      onChanged();</span>
<span class="nc" id="L2004">      return this;</span>
    }

<span class="nc" id="L2007">    private java.lang.Object startDate_ = &quot;&quot;;</span>
    /**
     * &lt;pre&gt;
     * Date range
     * &lt;/pre&gt;
     *
     * &lt;code&gt;string start_date = 3;&lt;/code&gt;
     * @return The startDate.
     */
    public java.lang.String getStartDate() {
<span class="nc" id="L2017">      java.lang.Object ref = startDate_;</span>
<span class="nc bnc" id="L2018" title="All 2 branches missed.">      if (!(ref instanceof java.lang.String)) {</span>
<span class="nc" id="L2019">        com.google.protobuf.ByteString bs =</span>
            (com.google.protobuf.ByteString) ref;
<span class="nc" id="L2021">        java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L2022">        startDate_ = s;</span>
<span class="nc" id="L2023">        return s;</span>
      } else {
<span class="nc" id="L2025">        return (java.lang.String) ref;</span>
      }
    }
    /**
     * &lt;pre&gt;
     * Date range
     * &lt;/pre&gt;
     *
     * &lt;code&gt;string start_date = 3;&lt;/code&gt;
     * @return The bytes for startDate.
     */
    public com.google.protobuf.ByteString
        getStartDateBytes() {
<span class="nc" id="L2038">      java.lang.Object ref = startDate_;</span>
<span class="nc bnc" id="L2039" title="All 2 branches missed.">      if (ref instanceof String) {</span>
<span class="nc" id="L2040">        com.google.protobuf.ByteString b = </span>
<span class="nc" id="L2041">            com.google.protobuf.ByteString.copyFromUtf8(</span>
                (java.lang.String) ref);
<span class="nc" id="L2043">        startDate_ = b;</span>
<span class="nc" id="L2044">        return b;</span>
      } else {
<span class="nc" id="L2046">        return (com.google.protobuf.ByteString) ref;</span>
      }
    }
    /**
     * &lt;pre&gt;
     * Date range
     * &lt;/pre&gt;
     *
     * &lt;code&gt;string start_date = 3;&lt;/code&gt;
     * @param value The startDate to set.
     * @return This builder for chaining.
     */
    public Builder setStartDate(
        java.lang.String value) {
<span class="nc bnc" id="L2060" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L2061">      startDate_ = value;</span>
<span class="nc" id="L2062">      bitField0_ |= 0x00000004;</span>
<span class="nc" id="L2063">      onChanged();</span>
<span class="nc" id="L2064">      return this;</span>
    }
    /**
     * &lt;pre&gt;
     * Date range
     * &lt;/pre&gt;
     *
     * &lt;code&gt;string start_date = 3;&lt;/code&gt;
     * @return This builder for chaining.
     */
    public Builder clearStartDate() {
<span class="nc" id="L2075">      startDate_ = getDefaultInstance().getStartDate();</span>
<span class="nc" id="L2076">      bitField0_ = (bitField0_ &amp; ~0x00000004);</span>
<span class="nc" id="L2077">      onChanged();</span>
<span class="nc" id="L2078">      return this;</span>
    }
    /**
     * &lt;pre&gt;
     * Date range
     * &lt;/pre&gt;
     *
     * &lt;code&gt;string start_date = 3;&lt;/code&gt;
     * @param value The bytes for startDate to set.
     * @return This builder for chaining.
     */
    public Builder setStartDateBytes(
        com.google.protobuf.ByteString value) {
<span class="nc bnc" id="L2091" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L2092">      checkByteStringIsUtf8(value);</span>
<span class="nc" id="L2093">      startDate_ = value;</span>
<span class="nc" id="L2094">      bitField0_ |= 0x00000004;</span>
<span class="nc" id="L2095">      onChanged();</span>
<span class="nc" id="L2096">      return this;</span>
    }

<span class="nc" id="L2099">    private java.lang.Object endDate_ = &quot;&quot;;</span>
    /**
     * &lt;code&gt;string end_date = 4;&lt;/code&gt;
     * @return The endDate.
     */
    public java.lang.String getEndDate() {
<span class="nc" id="L2105">      java.lang.Object ref = endDate_;</span>
<span class="nc bnc" id="L2106" title="All 2 branches missed.">      if (!(ref instanceof java.lang.String)) {</span>
<span class="nc" id="L2107">        com.google.protobuf.ByteString bs =</span>
            (com.google.protobuf.ByteString) ref;
<span class="nc" id="L2109">        java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L2110">        endDate_ = s;</span>
<span class="nc" id="L2111">        return s;</span>
      } else {
<span class="nc" id="L2113">        return (java.lang.String) ref;</span>
      }
    }
    /**
     * &lt;code&gt;string end_date = 4;&lt;/code&gt;
     * @return The bytes for endDate.
     */
    public com.google.protobuf.ByteString
        getEndDateBytes() {
<span class="nc" id="L2122">      java.lang.Object ref = endDate_;</span>
<span class="nc bnc" id="L2123" title="All 2 branches missed.">      if (ref instanceof String) {</span>
<span class="nc" id="L2124">        com.google.protobuf.ByteString b = </span>
<span class="nc" id="L2125">            com.google.protobuf.ByteString.copyFromUtf8(</span>
                (java.lang.String) ref);
<span class="nc" id="L2127">        endDate_ = b;</span>
<span class="nc" id="L2128">        return b;</span>
      } else {
<span class="nc" id="L2130">        return (com.google.protobuf.ByteString) ref;</span>
      }
    }
    /**
     * &lt;code&gt;string end_date = 4;&lt;/code&gt;
     * @param value The endDate to set.
     * @return This builder for chaining.
     */
    public Builder setEndDate(
        java.lang.String value) {
<span class="nc bnc" id="L2140" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L2141">      endDate_ = value;</span>
<span class="nc" id="L2142">      bitField0_ |= 0x00000008;</span>
<span class="nc" id="L2143">      onChanged();</span>
<span class="nc" id="L2144">      return this;</span>
    }
    /**
     * &lt;code&gt;string end_date = 4;&lt;/code&gt;
     * @return This builder for chaining.
     */
    public Builder clearEndDate() {
<span class="nc" id="L2151">      endDate_ = getDefaultInstance().getEndDate();</span>
<span class="nc" id="L2152">      bitField0_ = (bitField0_ &amp; ~0x00000008);</span>
<span class="nc" id="L2153">      onChanged();</span>
<span class="nc" id="L2154">      return this;</span>
    }
    /**
     * &lt;code&gt;string end_date = 4;&lt;/code&gt;
     * @param value The bytes for endDate to set.
     * @return This builder for chaining.
     */
    public Builder setEndDateBytes(
        com.google.protobuf.ByteString value) {
<span class="nc bnc" id="L2163" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L2164">      checkByteStringIsUtf8(value);</span>
<span class="nc" id="L2165">      endDate_ = value;</span>
<span class="nc" id="L2166">      bitField0_ |= 0x00000008;</span>
<span class="nc" id="L2167">      onChanged();</span>
<span class="nc" id="L2168">      return this;</span>
    }

<span class="nc" id="L2171">    private java.lang.Object currency_ = &quot;&quot;;</span>
    /**
     * &lt;pre&gt;
     * Currency settings
     * &lt;/pre&gt;
     *
     * &lt;code&gt;string currency = 5;&lt;/code&gt;
     * @return The currency.
     */
    public java.lang.String getCurrency() {
<span class="nc" id="L2181">      java.lang.Object ref = currency_;</span>
<span class="nc bnc" id="L2182" title="All 2 branches missed.">      if (!(ref instanceof java.lang.String)) {</span>
<span class="nc" id="L2183">        com.google.protobuf.ByteString bs =</span>
            (com.google.protobuf.ByteString) ref;
<span class="nc" id="L2185">        java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L2186">        currency_ = s;</span>
<span class="nc" id="L2187">        return s;</span>
      } else {
<span class="nc" id="L2189">        return (java.lang.String) ref;</span>
      }
    }
    /**
     * &lt;pre&gt;
     * Currency settings
     * &lt;/pre&gt;
     *
     * &lt;code&gt;string currency = 5;&lt;/code&gt;
     * @return The bytes for currency.
     */
    public com.google.protobuf.ByteString
        getCurrencyBytes() {
<span class="nc" id="L2202">      java.lang.Object ref = currency_;</span>
<span class="nc bnc" id="L2203" title="All 2 branches missed.">      if (ref instanceof String) {</span>
<span class="nc" id="L2204">        com.google.protobuf.ByteString b = </span>
<span class="nc" id="L2205">            com.google.protobuf.ByteString.copyFromUtf8(</span>
                (java.lang.String) ref);
<span class="nc" id="L2207">        currency_ = b;</span>
<span class="nc" id="L2208">        return b;</span>
      } else {
<span class="nc" id="L2210">        return (com.google.protobuf.ByteString) ref;</span>
      }
    }
    /**
     * &lt;pre&gt;
     * Currency settings
     * &lt;/pre&gt;
     *
     * &lt;code&gt;string currency = 5;&lt;/code&gt;
     * @param value The currency to set.
     * @return This builder for chaining.
     */
    public Builder setCurrency(
        java.lang.String value) {
<span class="nc bnc" id="L2224" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L2225">      currency_ = value;</span>
<span class="nc" id="L2226">      bitField0_ |= 0x00000010;</span>
<span class="nc" id="L2227">      onChanged();</span>
<span class="nc" id="L2228">      return this;</span>
    }
    /**
     * &lt;pre&gt;
     * Currency settings
     * &lt;/pre&gt;
     *
     * &lt;code&gt;string currency = 5;&lt;/code&gt;
     * @return This builder for chaining.
     */
    public Builder clearCurrency() {
<span class="nc" id="L2239">      currency_ = getDefaultInstance().getCurrency();</span>
<span class="nc" id="L2240">      bitField0_ = (bitField0_ &amp; ~0x00000010);</span>
<span class="nc" id="L2241">      onChanged();</span>
<span class="nc" id="L2242">      return this;</span>
    }
    /**
     * &lt;pre&gt;
     * Currency settings
     * &lt;/pre&gt;
     *
     * &lt;code&gt;string currency = 5;&lt;/code&gt;
     * @param value The bytes for currency to set.
     * @return This builder for chaining.
     */
    public Builder setCurrencyBytes(
        com.google.protobuf.ByteString value) {
<span class="nc bnc" id="L2255" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L2256">      checkByteStringIsUtf8(value);</span>
<span class="nc" id="L2257">      currency_ = value;</span>
<span class="nc" id="L2258">      bitField0_ |= 0x00000010;</span>
<span class="nc" id="L2259">      onChanged();</span>
<span class="nc" id="L2260">      return this;</span>
    }

<span class="nc" id="L2263">    private java.lang.Object preCurrency_ = &quot;&quot;;</span>
    /**
     * &lt;code&gt;string pre_currency = 6;&lt;/code&gt;
     * @return The preCurrency.
     */
    public java.lang.String getPreCurrency() {
<span class="nc" id="L2269">      java.lang.Object ref = preCurrency_;</span>
<span class="nc bnc" id="L2270" title="All 2 branches missed.">      if (!(ref instanceof java.lang.String)) {</span>
<span class="nc" id="L2271">        com.google.protobuf.ByteString bs =</span>
            (com.google.protobuf.ByteString) ref;
<span class="nc" id="L2273">        java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L2274">        preCurrency_ = s;</span>
<span class="nc" id="L2275">        return s;</span>
      } else {
<span class="nc" id="L2277">        return (java.lang.String) ref;</span>
      }
    }
    /**
     * &lt;code&gt;string pre_currency = 6;&lt;/code&gt;
     * @return The bytes for preCurrency.
     */
    public com.google.protobuf.ByteString
        getPreCurrencyBytes() {
<span class="nc" id="L2286">      java.lang.Object ref = preCurrency_;</span>
<span class="nc bnc" id="L2287" title="All 2 branches missed.">      if (ref instanceof String) {</span>
<span class="nc" id="L2288">        com.google.protobuf.ByteString b = </span>
<span class="nc" id="L2289">            com.google.protobuf.ByteString.copyFromUtf8(</span>
                (java.lang.String) ref);
<span class="nc" id="L2291">        preCurrency_ = b;</span>
<span class="nc" id="L2292">        return b;</span>
      } else {
<span class="nc" id="L2294">        return (com.google.protobuf.ByteString) ref;</span>
      }
    }
    /**
     * &lt;code&gt;string pre_currency = 6;&lt;/code&gt;
     * @param value The preCurrency to set.
     * @return This builder for chaining.
     */
    public Builder setPreCurrency(
        java.lang.String value) {
<span class="nc bnc" id="L2304" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L2305">      preCurrency_ = value;</span>
<span class="nc" id="L2306">      bitField0_ |= 0x00000020;</span>
<span class="nc" id="L2307">      onChanged();</span>
<span class="nc" id="L2308">      return this;</span>
    }
    /**
     * &lt;code&gt;string pre_currency = 6;&lt;/code&gt;
     * @return This builder for chaining.
     */
    public Builder clearPreCurrency() {
<span class="nc" id="L2315">      preCurrency_ = getDefaultInstance().getPreCurrency();</span>
<span class="nc" id="L2316">      bitField0_ = (bitField0_ &amp; ~0x00000020);</span>
<span class="nc" id="L2317">      onChanged();</span>
<span class="nc" id="L2318">      return this;</span>
    }
    /**
     * &lt;code&gt;string pre_currency = 6;&lt;/code&gt;
     * @param value The bytes for preCurrency to set.
     * @return This builder for chaining.
     */
    public Builder setPreCurrencyBytes(
        com.google.protobuf.ByteString value) {
<span class="nc bnc" id="L2327" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L2328">      checkByteStringIsUtf8(value);</span>
<span class="nc" id="L2329">      preCurrency_ = value;</span>
<span class="nc" id="L2330">      bitField0_ |= 0x00000020;</span>
<span class="nc" id="L2331">      onChanged();</span>
<span class="nc" id="L2332">      return this;</span>
    }

<span class="nc" id="L2335">    private java.lang.Object readCache_ = &quot;&quot;;</span>
    /**
     * &lt;pre&gt;
     * Cache and format settings
     * &lt;/pre&gt;
     *
     * &lt;code&gt;string read_cache = 7;&lt;/code&gt;
     * @return The readCache.
     */
    public java.lang.String getReadCache() {
<span class="nc" id="L2345">      java.lang.Object ref = readCache_;</span>
<span class="nc bnc" id="L2346" title="All 2 branches missed.">      if (!(ref instanceof java.lang.String)) {</span>
<span class="nc" id="L2347">        com.google.protobuf.ByteString bs =</span>
            (com.google.protobuf.ByteString) ref;
<span class="nc" id="L2349">        java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L2350">        readCache_ = s;</span>
<span class="nc" id="L2351">        return s;</span>
      } else {
<span class="nc" id="L2353">        return (java.lang.String) ref;</span>
      }
    }
    /**
     * &lt;pre&gt;
     * Cache and format settings
     * &lt;/pre&gt;
     *
     * &lt;code&gt;string read_cache = 7;&lt;/code&gt;
     * @return The bytes for readCache.
     */
    public com.google.protobuf.ByteString
        getReadCacheBytes() {
<span class="nc" id="L2366">      java.lang.Object ref = readCache_;</span>
<span class="nc bnc" id="L2367" title="All 2 branches missed.">      if (ref instanceof String) {</span>
<span class="nc" id="L2368">        com.google.protobuf.ByteString b = </span>
<span class="nc" id="L2369">            com.google.protobuf.ByteString.copyFromUtf8(</span>
                (java.lang.String) ref);
<span class="nc" id="L2371">        readCache_ = b;</span>
<span class="nc" id="L2372">        return b;</span>
      } else {
<span class="nc" id="L2374">        return (com.google.protobuf.ByteString) ref;</span>
      }
    }
    /**
     * &lt;pre&gt;
     * Cache and format settings
     * &lt;/pre&gt;
     *
     * &lt;code&gt;string read_cache = 7;&lt;/code&gt;
     * @param value The readCache to set.
     * @return This builder for chaining.
     */
    public Builder setReadCache(
        java.lang.String value) {
<span class="nc bnc" id="L2388" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L2389">      readCache_ = value;</span>
<span class="nc" id="L2390">      bitField0_ |= 0x00000040;</span>
<span class="nc" id="L2391">      onChanged();</span>
<span class="nc" id="L2392">      return this;</span>
    }
    /**
     * &lt;pre&gt;
     * Cache and format settings
     * &lt;/pre&gt;
     *
     * &lt;code&gt;string read_cache = 7;&lt;/code&gt;
     * @return This builder for chaining.
     */
    public Builder clearReadCache() {
<span class="nc" id="L2403">      readCache_ = getDefaultInstance().getReadCache();</span>
<span class="nc" id="L2404">      bitField0_ = (bitField0_ &amp; ~0x00000040);</span>
<span class="nc" id="L2405">      onChanged();</span>
<span class="nc" id="L2406">      return this;</span>
    }
    /**
     * &lt;pre&gt;
     * Cache and format settings
     * &lt;/pre&gt;
     *
     * &lt;code&gt;string read_cache = 7;&lt;/code&gt;
     * @param value The bytes for readCache to set.
     * @return This builder for chaining.
     */
    public Builder setReadCacheBytes(
        com.google.protobuf.ByteString value) {
<span class="nc bnc" id="L2419" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L2420">      checkByteStringIsUtf8(value);</span>
<span class="nc" id="L2421">      readCache_ = value;</span>
<span class="nc" id="L2422">      bitField0_ |= 0x00000040;</span>
<span class="nc" id="L2423">      onChanged();</span>
<span class="nc" id="L2424">      return this;</span>
    }

<span class="nc" id="L2427">    private java.lang.Object dateFormat_ = &quot;&quot;;</span>
    /**
     * &lt;code&gt;string date_format = 8;&lt;/code&gt;
     * @return The dateFormat.
     */
    public java.lang.String getDateFormat() {
<span class="nc" id="L2433">      java.lang.Object ref = dateFormat_;</span>
<span class="nc bnc" id="L2434" title="All 2 branches missed.">      if (!(ref instanceof java.lang.String)) {</span>
<span class="nc" id="L2435">        com.google.protobuf.ByteString bs =</span>
            (com.google.protobuf.ByteString) ref;
<span class="nc" id="L2437">        java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L2438">        dateFormat_ = s;</span>
<span class="nc" id="L2439">        return s;</span>
      } else {
<span class="nc" id="L2441">        return (java.lang.String) ref;</span>
      }
    }
    /**
     * &lt;code&gt;string date_format = 8;&lt;/code&gt;
     * @return The bytes for dateFormat.
     */
    public com.google.protobuf.ByteString
        getDateFormatBytes() {
<span class="nc" id="L2450">      java.lang.Object ref = dateFormat_;</span>
<span class="nc bnc" id="L2451" title="All 2 branches missed.">      if (ref instanceof String) {</span>
<span class="nc" id="L2452">        com.google.protobuf.ByteString b = </span>
<span class="nc" id="L2453">            com.google.protobuf.ByteString.copyFromUtf8(</span>
                (java.lang.String) ref);
<span class="nc" id="L2455">        dateFormat_ = b;</span>
<span class="nc" id="L2456">        return b;</span>
      } else {
<span class="nc" id="L2458">        return (com.google.protobuf.ByteString) ref;</span>
      }
    }
    /**
     * &lt;code&gt;string date_format = 8;&lt;/code&gt;
     * @param value The dateFormat to set.
     * @return This builder for chaining.
     */
    public Builder setDateFormat(
        java.lang.String value) {
<span class="nc bnc" id="L2468" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L2469">      dateFormat_ = value;</span>
<span class="nc" id="L2470">      bitField0_ |= 0x00000080;</span>
<span class="nc" id="L2471">      onChanged();</span>
<span class="nc" id="L2472">      return this;</span>
    }
    /**
     * &lt;code&gt;string date_format = 8;&lt;/code&gt;
     * @return This builder for chaining.
     */
    public Builder clearDateFormat() {
<span class="nc" id="L2479">      dateFormat_ = getDefaultInstance().getDateFormat();</span>
<span class="nc" id="L2480">      bitField0_ = (bitField0_ &amp; ~0x00000080);</span>
<span class="nc" id="L2481">      onChanged();</span>
<span class="nc" id="L2482">      return this;</span>
    }
    /**
     * &lt;code&gt;string date_format = 8;&lt;/code&gt;
     * @param value The bytes for dateFormat to set.
     * @return This builder for chaining.
     */
    public Builder setDateFormatBytes(
        com.google.protobuf.ByteString value) {
<span class="nc bnc" id="L2491" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L2492">      checkByteStringIsUtf8(value);</span>
<span class="nc" id="L2493">      dateFormat_ = value;</span>
<span class="nc" id="L2494">      bitField0_ |= 0x00000080;</span>
<span class="nc" id="L2495">      onChanged();</span>
<span class="nc" id="L2496">      return this;</span>
    }

<span class="nc" id="L2499">    private java.lang.Object decimalFormat_ = &quot;&quot;;</span>
    /**
     * &lt;code&gt;string decimal_format = 9;&lt;/code&gt;
     * @return The decimalFormat.
     */
    public java.lang.String getDecimalFormat() {
<span class="nc" id="L2505">      java.lang.Object ref = decimalFormat_;</span>
<span class="nc bnc" id="L2506" title="All 2 branches missed.">      if (!(ref instanceof java.lang.String)) {</span>
<span class="nc" id="L2507">        com.google.protobuf.ByteString bs =</span>
            (com.google.protobuf.ByteString) ref;
<span class="nc" id="L2509">        java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L2510">        decimalFormat_ = s;</span>
<span class="nc" id="L2511">        return s;</span>
      } else {
<span class="nc" id="L2513">        return (java.lang.String) ref;</span>
      }
    }
    /**
     * &lt;code&gt;string decimal_format = 9;&lt;/code&gt;
     * @return The bytes for decimalFormat.
     */
    public com.google.protobuf.ByteString
        getDecimalFormatBytes() {
<span class="nc" id="L2522">      java.lang.Object ref = decimalFormat_;</span>
<span class="nc bnc" id="L2523" title="All 2 branches missed.">      if (ref instanceof String) {</span>
<span class="nc" id="L2524">        com.google.protobuf.ByteString b = </span>
<span class="nc" id="L2525">            com.google.protobuf.ByteString.copyFromUtf8(</span>
                (java.lang.String) ref);
<span class="nc" id="L2527">        decimalFormat_ = b;</span>
<span class="nc" id="L2528">        return b;</span>
      } else {
<span class="nc" id="L2530">        return (com.google.protobuf.ByteString) ref;</span>
      }
    }
    /**
     * &lt;code&gt;string decimal_format = 9;&lt;/code&gt;
     * @param value The decimalFormat to set.
     * @return This builder for chaining.
     */
    public Builder setDecimalFormat(
        java.lang.String value) {
<span class="nc bnc" id="L2540" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L2541">      decimalFormat_ = value;</span>
<span class="nc" id="L2542">      bitField0_ |= 0x00000100;</span>
<span class="nc" id="L2543">      onChanged();</span>
<span class="nc" id="L2544">      return this;</span>
    }
    /**
     * &lt;code&gt;string decimal_format = 9;&lt;/code&gt;
     * @return This builder for chaining.
     */
    public Builder clearDecimalFormat() {
<span class="nc" id="L2551">      decimalFormat_ = getDefaultInstance().getDecimalFormat();</span>
<span class="nc" id="L2552">      bitField0_ = (bitField0_ &amp; ~0x00000100);</span>
<span class="nc" id="L2553">      onChanged();</span>
<span class="nc" id="L2554">      return this;</span>
    }
    /**
     * &lt;code&gt;string decimal_format = 9;&lt;/code&gt;
     * @param value The bytes for decimalFormat to set.
     * @return This builder for chaining.
     */
    public Builder setDecimalFormatBytes(
        com.google.protobuf.ByteString value) {
<span class="nc bnc" id="L2563" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L2564">      checkByteStringIsUtf8(value);</span>
<span class="nc" id="L2565">      decimalFormat_ = value;</span>
<span class="nc" id="L2566">      bitField0_ |= 0x00000100;</span>
<span class="nc" id="L2567">      onChanged();</span>
<span class="nc" id="L2568">      return this;</span>
    }

<span class="nc" id="L2571">    private java.lang.Object extendPerformance_ = &quot;&quot;;</span>
    /**
     * &lt;pre&gt;
     * Performance and tax settings
     * &lt;/pre&gt;
     *
     * &lt;code&gt;string extend_performance = 10;&lt;/code&gt;
     * @return The extendPerformance.
     */
    public java.lang.String getExtendPerformance() {
<span class="nc" id="L2581">      java.lang.Object ref = extendPerformance_;</span>
<span class="nc bnc" id="L2582" title="All 2 branches missed.">      if (!(ref instanceof java.lang.String)) {</span>
<span class="nc" id="L2583">        com.google.protobuf.ByteString bs =</span>
            (com.google.protobuf.ByteString) ref;
<span class="nc" id="L2585">        java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L2586">        extendPerformance_ = s;</span>
<span class="nc" id="L2587">        return s;</span>
      } else {
<span class="nc" id="L2589">        return (java.lang.String) ref;</span>
      }
    }
    /**
     * &lt;pre&gt;
     * Performance and tax settings
     * &lt;/pre&gt;
     *
     * &lt;code&gt;string extend_performance = 10;&lt;/code&gt;
     * @return The bytes for extendPerformance.
     */
    public com.google.protobuf.ByteString
        getExtendPerformanceBytes() {
<span class="nc" id="L2602">      java.lang.Object ref = extendPerformance_;</span>
<span class="nc bnc" id="L2603" title="All 2 branches missed.">      if (ref instanceof String) {</span>
<span class="nc" id="L2604">        com.google.protobuf.ByteString b = </span>
<span class="nc" id="L2605">            com.google.protobuf.ByteString.copyFromUtf8(</span>
                (java.lang.String) ref);
<span class="nc" id="L2607">        extendPerformance_ = b;</span>
<span class="nc" id="L2608">        return b;</span>
      } else {
<span class="nc" id="L2610">        return (com.google.protobuf.ByteString) ref;</span>
      }
    }
    /**
     * &lt;pre&gt;
     * Performance and tax settings
     * &lt;/pre&gt;
     *
     * &lt;code&gt;string extend_performance = 10;&lt;/code&gt;
     * @param value The extendPerformance to set.
     * @return This builder for chaining.
     */
    public Builder setExtendPerformance(
        java.lang.String value) {
<span class="nc bnc" id="L2624" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L2625">      extendPerformance_ = value;</span>
<span class="nc" id="L2626">      bitField0_ |= 0x00000200;</span>
<span class="nc" id="L2627">      onChanged();</span>
<span class="nc" id="L2628">      return this;</span>
    }
    /**
     * &lt;pre&gt;
     * Performance and tax settings
     * &lt;/pre&gt;
     *
     * &lt;code&gt;string extend_performance = 10;&lt;/code&gt;
     * @return This builder for chaining.
     */
    public Builder clearExtendPerformance() {
<span class="nc" id="L2639">      extendPerformance_ = getDefaultInstance().getExtendPerformance();</span>
<span class="nc" id="L2640">      bitField0_ = (bitField0_ &amp; ~0x00000200);</span>
<span class="nc" id="L2641">      onChanged();</span>
<span class="nc" id="L2642">      return this;</span>
    }
    /**
     * &lt;pre&gt;
     * Performance and tax settings
     * &lt;/pre&gt;
     *
     * &lt;code&gt;string extend_performance = 10;&lt;/code&gt;
     * @param value The bytes for extendPerformance to set.
     * @return This builder for chaining.
     */
    public Builder setExtendPerformanceBytes(
        com.google.protobuf.ByteString value) {
<span class="nc bnc" id="L2655" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L2656">      checkByteStringIsUtf8(value);</span>
<span class="nc" id="L2657">      extendPerformance_ = value;</span>
<span class="nc" id="L2658">      bitField0_ |= 0x00000200;</span>
<span class="nc" id="L2659">      onChanged();</span>
<span class="nc" id="L2660">      return this;</span>
    }

<span class="nc" id="L2663">    private java.lang.Object postTax_ = &quot;&quot;;</span>
    /**
     * &lt;code&gt;string post_tax = 11;&lt;/code&gt;
     * @return The postTax.
     */
    public java.lang.String getPostTax() {
<span class="nc" id="L2669">      java.lang.Object ref = postTax_;</span>
<span class="nc bnc" id="L2670" title="All 2 branches missed.">      if (!(ref instanceof java.lang.String)) {</span>
<span class="nc" id="L2671">        com.google.protobuf.ByteString bs =</span>
            (com.google.protobuf.ByteString) ref;
<span class="nc" id="L2673">        java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L2674">        postTax_ = s;</span>
<span class="nc" id="L2675">        return s;</span>
      } else {
<span class="nc" id="L2677">        return (java.lang.String) ref;</span>
      }
    }
    /**
     * &lt;code&gt;string post_tax = 11;&lt;/code&gt;
     * @return The bytes for postTax.
     */
    public com.google.protobuf.ByteString
        getPostTaxBytes() {
<span class="nc" id="L2686">      java.lang.Object ref = postTax_;</span>
<span class="nc bnc" id="L2687" title="All 2 branches missed.">      if (ref instanceof String) {</span>
<span class="nc" id="L2688">        com.google.protobuf.ByteString b = </span>
<span class="nc" id="L2689">            com.google.protobuf.ByteString.copyFromUtf8(</span>
                (java.lang.String) ref);
<span class="nc" id="L2691">        postTax_ = b;</span>
<span class="nc" id="L2692">        return b;</span>
      } else {
<span class="nc" id="L2694">        return (com.google.protobuf.ByteString) ref;</span>
      }
    }
    /**
     * &lt;code&gt;string post_tax = 11;&lt;/code&gt;
     * @param value The postTax to set.
     * @return This builder for chaining.
     */
    public Builder setPostTax(
        java.lang.String value) {
<span class="nc bnc" id="L2704" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L2705">      postTax_ = value;</span>
<span class="nc" id="L2706">      bitField0_ |= 0x00000400;</span>
<span class="nc" id="L2707">      onChanged();</span>
<span class="nc" id="L2708">      return this;</span>
    }
    /**
     * &lt;code&gt;string post_tax = 11;&lt;/code&gt;
     * @return This builder for chaining.
     */
    public Builder clearPostTax() {
<span class="nc" id="L2715">      postTax_ = getDefaultInstance().getPostTax();</span>
<span class="nc" id="L2716">      bitField0_ = (bitField0_ &amp; ~0x00000400);</span>
<span class="nc" id="L2717">      onChanged();</span>
<span class="nc" id="L2718">      return this;</span>
    }
    /**
     * &lt;code&gt;string post_tax = 11;&lt;/code&gt;
     * @param value The bytes for postTax to set.
     * @return This builder for chaining.
     */
    public Builder setPostTaxBytes(
        com.google.protobuf.ByteString value) {
<span class="nc bnc" id="L2727" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L2728">      checkByteStringIsUtf8(value);</span>
<span class="nc" id="L2729">      postTax_ = value;</span>
<span class="nc" id="L2730">      bitField0_ |= 0x00000400;</span>
<span class="nc" id="L2731">      onChanged();</span>
<span class="nc" id="L2732">      return this;</span>
    }

    private boolean useRequireId_ ;
    /**
     * &lt;pre&gt;
     * Feature flags
     * &lt;/pre&gt;
     *
     * &lt;code&gt;bool use_require_id = 12;&lt;/code&gt;
     * @return The useRequireId.
     */
    @java.lang.Override
    public boolean getUseRequireId() {
<span class="nc" id="L2746">      return useRequireId_;</span>
    }
    /**
     * &lt;pre&gt;
     * Feature flags
     * &lt;/pre&gt;
     *
     * &lt;code&gt;bool use_require_id = 12;&lt;/code&gt;
     * @param value The useRequireId to set.
     * @return This builder for chaining.
     */
    public Builder setUseRequireId(boolean value) {

<span class="nc" id="L2759">      useRequireId_ = value;</span>
<span class="nc" id="L2760">      bitField0_ |= 0x00000800;</span>
<span class="nc" id="L2761">      onChanged();</span>
<span class="nc" id="L2762">      return this;</span>
    }
    /**
     * &lt;pre&gt;
     * Feature flags
     * &lt;/pre&gt;
     *
     * &lt;code&gt;bool use_require_id = 12;&lt;/code&gt;
     * @return This builder for chaining.
     */
    public Builder clearUseRequireId() {
<span class="nc" id="L2773">      bitField0_ = (bitField0_ &amp; ~0x00000800);</span>
<span class="nc" id="L2774">      useRequireId_ = false;</span>
<span class="nc" id="L2775">      onChanged();</span>
<span class="nc" id="L2776">      return this;</span>
    }

<span class="nc" id="L2779">    private java.lang.Object useCase_ = &quot;&quot;;</span>
    /**
     * &lt;code&gt;string use_case = 13;&lt;/code&gt;
     * @return The useCase.
     */
    public java.lang.String getUseCase() {
<span class="nc" id="L2785">      java.lang.Object ref = useCase_;</span>
<span class="nc bnc" id="L2786" title="All 2 branches missed.">      if (!(ref instanceof java.lang.String)) {</span>
<span class="nc" id="L2787">        com.google.protobuf.ByteString bs =</span>
            (com.google.protobuf.ByteString) ref;
<span class="nc" id="L2789">        java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L2790">        useCase_ = s;</span>
<span class="nc" id="L2791">        return s;</span>
      } else {
<span class="nc" id="L2793">        return (java.lang.String) ref;</span>
      }
    }
    /**
     * &lt;code&gt;string use_case = 13;&lt;/code&gt;
     * @return The bytes for useCase.
     */
    public com.google.protobuf.ByteString
        getUseCaseBytes() {
<span class="nc" id="L2802">      java.lang.Object ref = useCase_;</span>
<span class="nc bnc" id="L2803" title="All 2 branches missed.">      if (ref instanceof String) {</span>
<span class="nc" id="L2804">        com.google.protobuf.ByteString b = </span>
<span class="nc" id="L2805">            com.google.protobuf.ByteString.copyFromUtf8(</span>
                (java.lang.String) ref);
<span class="nc" id="L2807">        useCase_ = b;</span>
<span class="nc" id="L2808">        return b;</span>
      } else {
<span class="nc" id="L2810">        return (com.google.protobuf.ByteString) ref;</span>
      }
    }
    /**
     * &lt;code&gt;string use_case = 13;&lt;/code&gt;
     * @param value The useCase to set.
     * @return This builder for chaining.
     */
    public Builder setUseCase(
        java.lang.String value) {
<span class="nc bnc" id="L2820" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L2821">      useCase_ = value;</span>
<span class="nc" id="L2822">      bitField0_ |= 0x00001000;</span>
<span class="nc" id="L2823">      onChanged();</span>
<span class="nc" id="L2824">      return this;</span>
    }
    /**
     * &lt;code&gt;string use_case = 13;&lt;/code&gt;
     * @return This builder for chaining.
     */
    public Builder clearUseCase() {
<span class="nc" id="L2831">      useCase_ = getDefaultInstance().getUseCase();</span>
<span class="nc" id="L2832">      bitField0_ = (bitField0_ &amp; ~0x00001000);</span>
<span class="nc" id="L2833">      onChanged();</span>
<span class="nc" id="L2834">      return this;</span>
    }
    /**
     * &lt;code&gt;string use_case = 13;&lt;/code&gt;
     * @param value The bytes for useCase to set.
     * @return This builder for chaining.
     */
    public Builder setUseCaseBytes(
        com.google.protobuf.ByteString value) {
<span class="nc bnc" id="L2843" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L2844">      checkByteStringIsUtf8(value);</span>
<span class="nc" id="L2845">      useCase_ = value;</span>
<span class="nc" id="L2846">      bitField0_ |= 0x00001000;</span>
<span class="nc" id="L2847">      onChanged();</span>
<span class="nc" id="L2848">      return this;</span>
    }

    private boolean useNewCcs_ ;
    /**
     * &lt;code&gt;bool use_new_ccs = 14;&lt;/code&gt;
     * @return The useNewCcs.
     */
    @java.lang.Override
    public boolean getUseNewCcs() {
<span class="nc" id="L2858">      return useNewCcs_;</span>
    }
    /**
     * &lt;code&gt;bool use_new_ccs = 14;&lt;/code&gt;
     * @param value The useNewCcs to set.
     * @return This builder for chaining.
     */
    public Builder setUseNewCcs(boolean value) {

<span class="nc" id="L2867">      useNewCcs_ = value;</span>
<span class="nc" id="L2868">      bitField0_ |= 0x00002000;</span>
<span class="nc" id="L2869">      onChanged();</span>
<span class="nc" id="L2870">      return this;</span>
    }
    /**
     * &lt;code&gt;bool use_new_ccs = 14;&lt;/code&gt;
     * @return This builder for chaining.
     */
    public Builder clearUseNewCcs() {
<span class="nc" id="L2877">      bitField0_ = (bitField0_ &amp; ~0x00002000);</span>
<span class="nc" id="L2878">      useNewCcs_ = false;</span>
<span class="nc" id="L2879">      onChanged();</span>
<span class="nc" id="L2880">      return this;</span>
    }

<span class="nc" id="L2883">    private java.lang.Object productId_ = &quot;&quot;;</span>
    /**
     * &lt;pre&gt;
     * Headers
     * &lt;/pre&gt;
     *
     * &lt;code&gt;string product_id = 15;&lt;/code&gt;
     * @return The productId.
     */
    public java.lang.String getProductId() {
<span class="nc" id="L2893">      java.lang.Object ref = productId_;</span>
<span class="nc bnc" id="L2894" title="All 2 branches missed.">      if (!(ref instanceof java.lang.String)) {</span>
<span class="nc" id="L2895">        com.google.protobuf.ByteString bs =</span>
            (com.google.protobuf.ByteString) ref;
<span class="nc" id="L2897">        java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L2898">        productId_ = s;</span>
<span class="nc" id="L2899">        return s;</span>
      } else {
<span class="nc" id="L2901">        return (java.lang.String) ref;</span>
      }
    }
    /**
     * &lt;pre&gt;
     * Headers
     * &lt;/pre&gt;
     *
     * &lt;code&gt;string product_id = 15;&lt;/code&gt;
     * @return The bytes for productId.
     */
    public com.google.protobuf.ByteString
        getProductIdBytes() {
<span class="nc" id="L2914">      java.lang.Object ref = productId_;</span>
<span class="nc bnc" id="L2915" title="All 2 branches missed.">      if (ref instanceof String) {</span>
<span class="nc" id="L2916">        com.google.protobuf.ByteString b = </span>
<span class="nc" id="L2917">            com.google.protobuf.ByteString.copyFromUtf8(</span>
                (java.lang.String) ref);
<span class="nc" id="L2919">        productId_ = b;</span>
<span class="nc" id="L2920">        return b;</span>
      } else {
<span class="nc" id="L2922">        return (com.google.protobuf.ByteString) ref;</span>
      }
    }
    /**
     * &lt;pre&gt;
     * Headers
     * &lt;/pre&gt;
     *
     * &lt;code&gt;string product_id = 15;&lt;/code&gt;
     * @param value The productId to set.
     * @return This builder for chaining.
     */
    public Builder setProductId(
        java.lang.String value) {
<span class="nc bnc" id="L2936" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L2937">      productId_ = value;</span>
<span class="nc" id="L2938">      bitField0_ |= 0x00004000;</span>
<span class="nc" id="L2939">      onChanged();</span>
<span class="nc" id="L2940">      return this;</span>
    }
    /**
     * &lt;pre&gt;
     * Headers
     * &lt;/pre&gt;
     *
     * &lt;code&gt;string product_id = 15;&lt;/code&gt;
     * @return This builder for chaining.
     */
    public Builder clearProductId() {
<span class="nc" id="L2951">      productId_ = getDefaultInstance().getProductId();</span>
<span class="nc" id="L2952">      bitField0_ = (bitField0_ &amp; ~0x00004000);</span>
<span class="nc" id="L2953">      onChanged();</span>
<span class="nc" id="L2954">      return this;</span>
    }
    /**
     * &lt;pre&gt;
     * Headers
     * &lt;/pre&gt;
     *
     * &lt;code&gt;string product_id = 15;&lt;/code&gt;
     * @param value The bytes for productId to set.
     * @return This builder for chaining.
     */
    public Builder setProductIdBytes(
        com.google.protobuf.ByteString value) {
<span class="nc bnc" id="L2967" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L2968">      checkByteStringIsUtf8(value);</span>
<span class="nc" id="L2969">      productId_ = value;</span>
<span class="nc" id="L2970">      bitField0_ |= 0x00004000;</span>
<span class="nc" id="L2971">      onChanged();</span>
<span class="nc" id="L2972">      return this;</span>
    }

<span class="nc" id="L2975">    private java.lang.Object requestId_ = &quot;&quot;;</span>
    /**
     * &lt;code&gt;string request_id = 16;&lt;/code&gt;
     * @return The requestId.
     */
    public java.lang.String getRequestId() {
<span class="nc" id="L2981">      java.lang.Object ref = requestId_;</span>
<span class="nc bnc" id="L2982" title="All 2 branches missed.">      if (!(ref instanceof java.lang.String)) {</span>
<span class="nc" id="L2983">        com.google.protobuf.ByteString bs =</span>
            (com.google.protobuf.ByteString) ref;
<span class="nc" id="L2985">        java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L2986">        requestId_ = s;</span>
<span class="nc" id="L2987">        return s;</span>
      } else {
<span class="nc" id="L2989">        return (java.lang.String) ref;</span>
      }
    }
    /**
     * &lt;code&gt;string request_id = 16;&lt;/code&gt;
     * @return The bytes for requestId.
     */
    public com.google.protobuf.ByteString
        getRequestIdBytes() {
<span class="nc" id="L2998">      java.lang.Object ref = requestId_;</span>
<span class="nc bnc" id="L2999" title="All 2 branches missed.">      if (ref instanceof String) {</span>
<span class="nc" id="L3000">        com.google.protobuf.ByteString b = </span>
<span class="nc" id="L3001">            com.google.protobuf.ByteString.copyFromUtf8(</span>
                (java.lang.String) ref);
<span class="nc" id="L3003">        requestId_ = b;</span>
<span class="nc" id="L3004">        return b;</span>
      } else {
<span class="nc" id="L3006">        return (com.google.protobuf.ByteString) ref;</span>
      }
    }
    /**
     * &lt;code&gt;string request_id = 16;&lt;/code&gt;
     * @param value The requestId to set.
     * @return This builder for chaining.
     */
    public Builder setRequestId(
        java.lang.String value) {
<span class="nc bnc" id="L3016" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L3017">      requestId_ = value;</span>
<span class="nc" id="L3018">      bitField0_ |= 0x00008000;</span>
<span class="nc" id="L3019">      onChanged();</span>
<span class="nc" id="L3020">      return this;</span>
    }
    /**
     * &lt;code&gt;string request_id = 16;&lt;/code&gt;
     * @return This builder for chaining.
     */
    public Builder clearRequestId() {
<span class="nc" id="L3027">      requestId_ = getDefaultInstance().getRequestId();</span>
<span class="nc" id="L3028">      bitField0_ = (bitField0_ &amp; ~0x00008000);</span>
<span class="nc" id="L3029">      onChanged();</span>
<span class="nc" id="L3030">      return this;</span>
    }
    /**
     * &lt;code&gt;string request_id = 16;&lt;/code&gt;
     * @param value The bytes for requestId to set.
     * @return This builder for chaining.
     */
    public Builder setRequestIdBytes(
        com.google.protobuf.ByteString value) {
<span class="nc bnc" id="L3039" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L3040">      checkByteStringIsUtf8(value);</span>
<span class="nc" id="L3041">      requestId_ = value;</span>
<span class="nc" id="L3042">      bitField0_ |= 0x00008000;</span>
<span class="nc" id="L3043">      onChanged();</span>
<span class="nc" id="L3044">      return this;</span>
    }

<span class="nc" id="L3047">    private java.lang.Object traceId_ = &quot;&quot;;</span>
    /**
     * &lt;code&gt;string trace_id = 17;&lt;/code&gt;
     * @return The traceId.
     */
    public java.lang.String getTraceId() {
<span class="nc" id="L3053">      java.lang.Object ref = traceId_;</span>
<span class="nc bnc" id="L3054" title="All 2 branches missed.">      if (!(ref instanceof java.lang.String)) {</span>
<span class="nc" id="L3055">        com.google.protobuf.ByteString bs =</span>
            (com.google.protobuf.ByteString) ref;
<span class="nc" id="L3057">        java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L3058">        traceId_ = s;</span>
<span class="nc" id="L3059">        return s;</span>
      } else {
<span class="nc" id="L3061">        return (java.lang.String) ref;</span>
      }
    }
    /**
     * &lt;code&gt;string trace_id = 17;&lt;/code&gt;
     * @return The bytes for traceId.
     */
    public com.google.protobuf.ByteString
        getTraceIdBytes() {
<span class="nc" id="L3070">      java.lang.Object ref = traceId_;</span>
<span class="nc bnc" id="L3071" title="All 2 branches missed.">      if (ref instanceof String) {</span>
<span class="nc" id="L3072">        com.google.protobuf.ByteString b = </span>
<span class="nc" id="L3073">            com.google.protobuf.ByteString.copyFromUtf8(</span>
                (java.lang.String) ref);
<span class="nc" id="L3075">        traceId_ = b;</span>
<span class="nc" id="L3076">        return b;</span>
      } else {
<span class="nc" id="L3078">        return (com.google.protobuf.ByteString) ref;</span>
      }
    }
    /**
     * &lt;code&gt;string trace_id = 17;&lt;/code&gt;
     * @param value The traceId to set.
     * @return This builder for chaining.
     */
    public Builder setTraceId(
        java.lang.String value) {
<span class="nc bnc" id="L3088" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L3089">      traceId_ = value;</span>
<span class="nc" id="L3090">      bitField0_ |= 0x00010000;</span>
<span class="nc" id="L3091">      onChanged();</span>
<span class="nc" id="L3092">      return this;</span>
    }
    /**
     * &lt;code&gt;string trace_id = 17;&lt;/code&gt;
     * @return This builder for chaining.
     */
    public Builder clearTraceId() {
<span class="nc" id="L3099">      traceId_ = getDefaultInstance().getTraceId();</span>
<span class="nc" id="L3100">      bitField0_ = (bitField0_ &amp; ~0x00010000);</span>
<span class="nc" id="L3101">      onChanged();</span>
<span class="nc" id="L3102">      return this;</span>
    }
    /**
     * &lt;code&gt;string trace_id = 17;&lt;/code&gt;
     * @param value The bytes for traceId to set.
     * @return This builder for chaining.
     */
    public Builder setTraceIdBytes(
        com.google.protobuf.ByteString value) {
<span class="nc bnc" id="L3111" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L3112">      checkByteStringIsUtf8(value);</span>
<span class="nc" id="L3113">      traceId_ = value;</span>
<span class="nc" id="L3114">      bitField0_ |= 0x00010000;</span>
<span class="nc" id="L3115">      onChanged();</span>
<span class="nc" id="L3116">      return this;</span>
    }

<span class="nc" id="L3119">    private java.lang.Object userId_ = &quot;&quot;;</span>
    /**
     * &lt;code&gt;string user_id = 18;&lt;/code&gt;
     * @return The userId.
     */
    public java.lang.String getUserId() {
<span class="nc" id="L3125">      java.lang.Object ref = userId_;</span>
<span class="nc bnc" id="L3126" title="All 2 branches missed.">      if (!(ref instanceof java.lang.String)) {</span>
<span class="nc" id="L3127">        com.google.protobuf.ByteString bs =</span>
            (com.google.protobuf.ByteString) ref;
<span class="nc" id="L3129">        java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L3130">        userId_ = s;</span>
<span class="nc" id="L3131">        return s;</span>
      } else {
<span class="nc" id="L3133">        return (java.lang.String) ref;</span>
      }
    }
    /**
     * &lt;code&gt;string user_id = 18;&lt;/code&gt;
     * @return The bytes for userId.
     */
    public com.google.protobuf.ByteString
        getUserIdBytes() {
<span class="nc" id="L3142">      java.lang.Object ref = userId_;</span>
<span class="nc bnc" id="L3143" title="All 2 branches missed.">      if (ref instanceof String) {</span>
<span class="nc" id="L3144">        com.google.protobuf.ByteString b = </span>
<span class="nc" id="L3145">            com.google.protobuf.ByteString.copyFromUtf8(</span>
                (java.lang.String) ref);
<span class="nc" id="L3147">        userId_ = b;</span>
<span class="nc" id="L3148">        return b;</span>
      } else {
<span class="nc" id="L3150">        return (com.google.protobuf.ByteString) ref;</span>
      }
    }
    /**
     * &lt;code&gt;string user_id = 18;&lt;/code&gt;
     * @param value The userId to set.
     * @return This builder for chaining.
     */
    public Builder setUserId(
        java.lang.String value) {
<span class="nc bnc" id="L3160" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L3161">      userId_ = value;</span>
<span class="nc" id="L3162">      bitField0_ |= 0x00020000;</span>
<span class="nc" id="L3163">      onChanged();</span>
<span class="nc" id="L3164">      return this;</span>
    }
    /**
     * &lt;code&gt;string user_id = 18;&lt;/code&gt;
     * @return This builder for chaining.
     */
    public Builder clearUserId() {
<span class="nc" id="L3171">      userId_ = getDefaultInstance().getUserId();</span>
<span class="nc" id="L3172">      bitField0_ = (bitField0_ &amp; ~0x00020000);</span>
<span class="nc" id="L3173">      onChanged();</span>
<span class="nc" id="L3174">      return this;</span>
    }
    /**
     * &lt;code&gt;string user_id = 18;&lt;/code&gt;
     * @param value The bytes for userId to set.
     * @return This builder for chaining.
     */
    public Builder setUserIdBytes(
        com.google.protobuf.ByteString value) {
<span class="nc bnc" id="L3183" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L3184">      checkByteStringIsUtf8(value);</span>
<span class="nc" id="L3185">      userId_ = value;</span>
<span class="nc" id="L3186">      bitField0_ |= 0x00020000;</span>
<span class="nc" id="L3187">      onChanged();</span>
<span class="nc" id="L3188">      return this;</span>
    }

<span class="nc" id="L3191">    private java.lang.Object authorization_ = &quot;&quot;;</span>
    /**
     * &lt;code&gt;string authorization = 19;&lt;/code&gt;
     * @return The authorization.
     */
    public java.lang.String getAuthorization() {
<span class="nc" id="L3197">      java.lang.Object ref = authorization_;</span>
<span class="nc bnc" id="L3198" title="All 2 branches missed.">      if (!(ref instanceof java.lang.String)) {</span>
<span class="nc" id="L3199">        com.google.protobuf.ByteString bs =</span>
            (com.google.protobuf.ByteString) ref;
<span class="nc" id="L3201">        java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L3202">        authorization_ = s;</span>
<span class="nc" id="L3203">        return s;</span>
      } else {
<span class="nc" id="L3205">        return (java.lang.String) ref;</span>
      }
    }
    /**
     * &lt;code&gt;string authorization = 19;&lt;/code&gt;
     * @return The bytes for authorization.
     */
    public com.google.protobuf.ByteString
        getAuthorizationBytes() {
<span class="nc" id="L3214">      java.lang.Object ref = authorization_;</span>
<span class="nc bnc" id="L3215" title="All 2 branches missed.">      if (ref instanceof String) {</span>
<span class="nc" id="L3216">        com.google.protobuf.ByteString b = </span>
<span class="nc" id="L3217">            com.google.protobuf.ByteString.copyFromUtf8(</span>
                (java.lang.String) ref);
<span class="nc" id="L3219">        authorization_ = b;</span>
<span class="nc" id="L3220">        return b;</span>
      } else {
<span class="nc" id="L3222">        return (com.google.protobuf.ByteString) ref;</span>
      }
    }
    /**
     * &lt;code&gt;string authorization = 19;&lt;/code&gt;
     * @param value The authorization to set.
     * @return This builder for chaining.
     */
    public Builder setAuthorization(
        java.lang.String value) {
<span class="nc bnc" id="L3232" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L3233">      authorization_ = value;</span>
<span class="nc" id="L3234">      bitField0_ |= 0x00040000;</span>
<span class="nc" id="L3235">      onChanged();</span>
<span class="nc" id="L3236">      return this;</span>
    }
    /**
     * &lt;code&gt;string authorization = 19;&lt;/code&gt;
     * @return This builder for chaining.
     */
    public Builder clearAuthorization() {
<span class="nc" id="L3243">      authorization_ = getDefaultInstance().getAuthorization();</span>
<span class="nc" id="L3244">      bitField0_ = (bitField0_ &amp; ~0x00040000);</span>
<span class="nc" id="L3245">      onChanged();</span>
<span class="nc" id="L3246">      return this;</span>
    }
    /**
     * &lt;code&gt;string authorization = 19;&lt;/code&gt;
     * @param value The bytes for authorization to set.
     * @return This builder for chaining.
     */
    public Builder setAuthorizationBytes(
        com.google.protobuf.ByteString value) {
<span class="nc bnc" id="L3255" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L3256">      checkByteStringIsUtf8(value);</span>
<span class="nc" id="L3257">      authorization_ = value;</span>
<span class="nc" id="L3258">      bitField0_ |= 0x00040000;</span>
<span class="nc" id="L3259">      onChanged();</span>
<span class="nc" id="L3260">      return this;</span>
    }

    private boolean checkEntitlement_ ;
    /**
     * &lt;code&gt;bool check_entitlement = 20;&lt;/code&gt;
     * @return The checkEntitlement.
     */
    @java.lang.Override
    public boolean getCheckEntitlement() {
<span class="nc" id="L3270">      return checkEntitlement_;</span>
    }
    /**
     * &lt;code&gt;bool check_entitlement = 20;&lt;/code&gt;
     * @param value The checkEntitlement to set.
     * @return This builder for chaining.
     */
    public Builder setCheckEntitlement(boolean value) {

<span class="nc" id="L3279">      checkEntitlement_ = value;</span>
<span class="nc" id="L3280">      bitField0_ |= 0x00080000;</span>
<span class="nc" id="L3281">      onChanged();</span>
<span class="nc" id="L3282">      return this;</span>
    }
    /**
     * &lt;code&gt;bool check_entitlement = 20;&lt;/code&gt;
     * @return This builder for chaining.
     */
    public Builder clearCheckEntitlement() {
<span class="nc" id="L3289">      bitField0_ = (bitField0_ &amp; ~0x00080000);</span>
<span class="nc" id="L3290">      checkEntitlement_ = false;</span>
<span class="nc" id="L3291">      onChanged();</span>
<span class="nc" id="L3292">      return this;</span>
    }

<span class="nc" id="L3295">    private java.lang.Object entitlementProductId_ = &quot;&quot;;</span>
    /**
     * &lt;code&gt;string entitlement_product_id = 21;&lt;/code&gt;
     * @return The entitlementProductId.
     */
    public java.lang.String getEntitlementProductId() {
<span class="nc" id="L3301">      java.lang.Object ref = entitlementProductId_;</span>
<span class="nc bnc" id="L3302" title="All 2 branches missed.">      if (!(ref instanceof java.lang.String)) {</span>
<span class="nc" id="L3303">        com.google.protobuf.ByteString bs =</span>
            (com.google.protobuf.ByteString) ref;
<span class="nc" id="L3305">        java.lang.String s = bs.toStringUtf8();</span>
<span class="nc" id="L3306">        entitlementProductId_ = s;</span>
<span class="nc" id="L3307">        return s;</span>
      } else {
<span class="nc" id="L3309">        return (java.lang.String) ref;</span>
      }
    }
    /**
     * &lt;code&gt;string entitlement_product_id = 21;&lt;/code&gt;
     * @return The bytes for entitlementProductId.
     */
    public com.google.protobuf.ByteString
        getEntitlementProductIdBytes() {
<span class="nc" id="L3318">      java.lang.Object ref = entitlementProductId_;</span>
<span class="nc bnc" id="L3319" title="All 2 branches missed.">      if (ref instanceof String) {</span>
<span class="nc" id="L3320">        com.google.protobuf.ByteString b = </span>
<span class="nc" id="L3321">            com.google.protobuf.ByteString.copyFromUtf8(</span>
                (java.lang.String) ref);
<span class="nc" id="L3323">        entitlementProductId_ = b;</span>
<span class="nc" id="L3324">        return b;</span>
      } else {
<span class="nc" id="L3326">        return (com.google.protobuf.ByteString) ref;</span>
      }
    }
    /**
     * &lt;code&gt;string entitlement_product_id = 21;&lt;/code&gt;
     * @param value The entitlementProductId to set.
     * @return This builder for chaining.
     */
    public Builder setEntitlementProductId(
        java.lang.String value) {
<span class="nc bnc" id="L3336" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L3337">      entitlementProductId_ = value;</span>
<span class="nc" id="L3338">      bitField0_ |= 0x00100000;</span>
<span class="nc" id="L3339">      onChanged();</span>
<span class="nc" id="L3340">      return this;</span>
    }
    /**
     * &lt;code&gt;string entitlement_product_id = 21;&lt;/code&gt;
     * @return This builder for chaining.
     */
    public Builder clearEntitlementProductId() {
<span class="nc" id="L3347">      entitlementProductId_ = getDefaultInstance().getEntitlementProductId();</span>
<span class="nc" id="L3348">      bitField0_ = (bitField0_ &amp; ~0x00100000);</span>
<span class="nc" id="L3349">      onChanged();</span>
<span class="nc" id="L3350">      return this;</span>
    }
    /**
     * &lt;code&gt;string entitlement_product_id = 21;&lt;/code&gt;
     * @param value The bytes for entitlementProductId to set.
     * @return This builder for chaining.
     */
    public Builder setEntitlementProductIdBytes(
        com.google.protobuf.ByteString value) {
<span class="nc bnc" id="L3359" title="All 2 branches missed.">      if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L3360">      checkByteStringIsUtf8(value);</span>
<span class="nc" id="L3361">      entitlementProductId_ = value;</span>
<span class="nc" id="L3362">      bitField0_ |= 0x00100000;</span>
<span class="nc" id="L3363">      onChanged();</span>
<span class="nc" id="L3364">      return this;</span>
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
<span class="nc" id="L3369">      return super.setUnknownFields(unknownFields);</span>
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
<span class="nc" id="L3375">      return super.mergeUnknownFields(unknownFields);</span>
    }


    // @@protoc_insertion_point(builder_scope:timeseries.TimeSeriesRequest)
  }

  // @@protoc_insertion_point(class_scope:timeseries.TimeSeriesRequest)
  private static final com.morningstar.martapi.grpc.TimeSeriesRequest DEFAULT_INSTANCE;
  static {
<span class="nc" id="L3385">    DEFAULT_INSTANCE = new com.morningstar.martapi.grpc.TimeSeriesRequest();</span>
  }

  public static com.morningstar.martapi.grpc.TimeSeriesRequest getDefaultInstance() {
<span class="nc" id="L3389">    return DEFAULT_INSTANCE;</span>
  }

  private static final com.google.protobuf.Parser&lt;TimeSeriesRequest&gt;
<span class="nc" id="L3393">      PARSER = new com.google.protobuf.AbstractParser&lt;TimeSeriesRequest&gt;() {</span>
    @java.lang.Override
    public TimeSeriesRequest parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L3399">      Builder builder = newBuilder();</span>
      try {
<span class="nc" id="L3401">        builder.mergeFrom(input, extensionRegistry);</span>
<span class="nc" id="L3402">      } catch (com.google.protobuf.InvalidProtocolBufferException e) {</span>
<span class="nc" id="L3403">        throw e.setUnfinishedMessage(builder.buildPartial());</span>
<span class="nc" id="L3404">      } catch (com.google.protobuf.UninitializedMessageException e) {</span>
<span class="nc" id="L3405">        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());</span>
<span class="nc" id="L3406">      } catch (java.io.IOException e) {</span>
<span class="nc" id="L3407">        throw new com.google.protobuf.InvalidProtocolBufferException(e)</span>
<span class="nc" id="L3408">            .setUnfinishedMessage(builder.buildPartial());</span>
<span class="nc" id="L3409">      }</span>
<span class="nc" id="L3410">      return builder.buildPartial();</span>
    }
  };

  public static com.google.protobuf.Parser&lt;TimeSeriesRequest&gt; parser() {
<span class="nc" id="L3415">    return PARSER;</span>
  }

  @java.lang.Override
  public com.google.protobuf.Parser&lt;TimeSeriesRequest&gt; getParserForType() {
<span class="nc" id="L3420">    return PARSER;</span>
  }

  @java.lang.Override
  public com.morningstar.martapi.grpc.TimeSeriesRequest getDefaultInstanceForType() {
<span class="nc" id="L3425">    return DEFAULT_INSTANCE;</span>
  }

}

</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>