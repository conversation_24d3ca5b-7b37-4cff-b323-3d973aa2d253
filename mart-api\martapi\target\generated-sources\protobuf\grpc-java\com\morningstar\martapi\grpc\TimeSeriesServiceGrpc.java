package com.morningstar.martapi.grpc;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 * <pre>
 * Time Series Service Definition
 * </pre>
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.58.0)",
    comments = "Source: timeseries_service.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class TimeSeriesServiceGrpc {

  private TimeSeriesServiceGrpc() {}

  public static final java.lang.String SERVICE_NAME = "timeseries.TimeSeriesService";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.morningstar.martapi.grpc.TimeSeriesRequest,
      com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas> getGetTimeSeriesDataMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "GetTimeSeriesData",
      requestType = com.morningstar.martapi.grpc.TimeSeriesRequest.class,
      responseType = com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.morningstar.martapi.grpc.TimeSeriesRequest,
      com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas> getGetTimeSeriesDataMethod() {
    io.grpc.MethodDescriptor<com.morningstar.martapi.grpc.TimeSeriesRequest, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas> getGetTimeSeriesDataMethod;
    if ((getGetTimeSeriesDataMethod = TimeSeriesServiceGrpc.getGetTimeSeriesDataMethod) == null) {
      synchronized (TimeSeriesServiceGrpc.class) {
        if ((getGetTimeSeriesDataMethod = TimeSeriesServiceGrpc.getGetTimeSeriesDataMethod) == null) {
          TimeSeriesServiceGrpc.getGetTimeSeriesDataMethod = getGetTimeSeriesDataMethod =
              io.grpc.MethodDescriptor.<com.morningstar.martapi.grpc.TimeSeriesRequest, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "GetTimeSeriesData"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.morningstar.martapi.grpc.TimeSeriesRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas.getDefaultInstance()))
              .setSchemaDescriptor(new TimeSeriesServiceMethodDescriptorSupplier("GetTimeSeriesData"))
              .build();
        }
      }
    }
    return getGetTimeSeriesDataMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static TimeSeriesServiceStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<TimeSeriesServiceStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<TimeSeriesServiceStub>() {
        @java.lang.Override
        public TimeSeriesServiceStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new TimeSeriesServiceStub(channel, callOptions);
        }
      };
    return TimeSeriesServiceStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static TimeSeriesServiceBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<TimeSeriesServiceBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<TimeSeriesServiceBlockingStub>() {
        @java.lang.Override
        public TimeSeriesServiceBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new TimeSeriesServiceBlockingStub(channel, callOptions);
        }
      };
    return TimeSeriesServiceBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static TimeSeriesServiceFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<TimeSeriesServiceFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<TimeSeriesServiceFutureStub>() {
        @java.lang.Override
        public TimeSeriesServiceFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new TimeSeriesServiceFutureStub(channel, callOptions);
        }
      };
    return TimeSeriesServiceFutureStub.newStub(factory, channel);
  }

  /**
   * <pre>
   * Time Series Service Definition
   * </pre>
   */
  public interface AsyncService {

    /**
     * <pre>
     * Retrieve time series data as protobuf
     * </pre>
     */
    default void getTimeSeriesData(com.morningstar.martapi.grpc.TimeSeriesRequest request,
        io.grpc.stub.StreamObserver<com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetTimeSeriesDataMethod(), responseObserver);
    }
  }

  /**
   * Base class for the server implementation of the service TimeSeriesService.
   * <pre>
   * Time Series Service Definition
   * </pre>
   */
  public static abstract class TimeSeriesServiceImplBase
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return TimeSeriesServiceGrpc.bindService(this);
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service TimeSeriesService.
   * <pre>
   * Time Series Service Definition
   * </pre>
   */
  public static final class TimeSeriesServiceStub
      extends io.grpc.stub.AbstractAsyncStub<TimeSeriesServiceStub> {
    private TimeSeriesServiceStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected TimeSeriesServiceStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new TimeSeriesServiceStub(channel, callOptions);
    }

    /**
     * <pre>
     * Retrieve time series data as protobuf
     * </pre>
     */
    public void getTimeSeriesData(com.morningstar.martapi.grpc.TimeSeriesRequest request,
        io.grpc.stub.StreamObserver<com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getGetTimeSeriesDataMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service TimeSeriesService.
   * <pre>
   * Time Series Service Definition
   * </pre>
   */
  public static final class TimeSeriesServiceBlockingStub
      extends io.grpc.stub.AbstractBlockingStub<TimeSeriesServiceBlockingStub> {
    private TimeSeriesServiceBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected TimeSeriesServiceBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new TimeSeriesServiceBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     * Retrieve time series data as protobuf
     * </pre>
     */
    public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas getTimeSeriesData(com.morningstar.martapi.grpc.TimeSeriesRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getGetTimeSeriesDataMethod(), getCallOptions(), request);
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service TimeSeriesService.
   * <pre>
   * Time Series Service Definition
   * </pre>
   */
  public static final class TimeSeriesServiceFutureStub
      extends io.grpc.stub.AbstractFutureStub<TimeSeriesServiceFutureStub> {
    private TimeSeriesServiceFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected TimeSeriesServiceFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new TimeSeriesServiceFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     * Retrieve time series data as protobuf
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas> getTimeSeriesData(
        com.morningstar.martapi.grpc.TimeSeriesRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getGetTimeSeriesDataMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_GET_TIME_SERIES_DATA = 0;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AsyncService serviceImpl;
    private final int methodId;

    MethodHandlers(AsyncService serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_GET_TIME_SERIES_DATA:
          serviceImpl.getTimeSeriesData((com.morningstar.martapi.grpc.TimeSeriesRequest) request,
              (io.grpc.stub.StreamObserver<com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getGetTimeSeriesDataMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers<
              com.morningstar.martapi.grpc.TimeSeriesRequest,
              com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas>(
                service, METHODID_GET_TIME_SERIES_DATA)))
        .build();
  }

  private static abstract class TimeSeriesServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    TimeSeriesServiceBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.morningstar.martapi.grpc.TimeSeriesServiceProto.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("TimeSeriesService");
    }
  }

  private static final class TimeSeriesServiceFileDescriptorSupplier
      extends TimeSeriesServiceBaseDescriptorSupplier {
    TimeSeriesServiceFileDescriptorSupplier() {}
  }

  private static final class TimeSeriesServiceMethodDescriptorSupplier
      extends TimeSeriesServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

    TimeSeriesServiceMethodDescriptorSupplier(java.lang.String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (TimeSeriesServiceGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new TimeSeriesServiceFileDescriptorSupplier())
              .addMethod(getGetTimeSeriesDataMethod())
              .build();
        }
      }
    }
    return result;
  }
}
