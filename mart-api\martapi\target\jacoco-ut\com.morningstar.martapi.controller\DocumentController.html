<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DocumentController</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <a href="index.html" class="el_package">com.morningstar.martapi.controller</a> &gt; <span class="el_class">DocumentController</span></div><h1>DocumentController</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">378 of 378</td><td class="ctr2">0%</td><td class="bar">14 of 14</td><td class="ctr2">0%</td><td class="ctr1">18</td><td class="ctr2">18</td><td class="ctr1">53</td><td class="ctr2">53</td><td class="ctr1">11</td><td class="ctr2">11</td></tr></tfoot><tbody><tr><td id="a3"><a href="DocumentController.java.html#L74" class="el_method">getData(String, String, String, String, String, String, String, String, String, boolean, ServerHttpRequest)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="78" alt="78"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="6" alt="6"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">4</td><td class="ctr2" id="g0">4</td><td class="ctr1" id="h0">12</td><td class="ctr2" id="i0">12</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a9"><a href="DocumentController.java.html#L103" class="el_method">lambda$getData$2(String, long, String, InvestmentApiRequest, ServerHttpRequest, Throwable)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="107" height="10" title="70" alt="70"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">7</td><td class="ctr2" id="i3">7</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a0"><a href="DocumentController.java.html#L120" class="el_method">buildInvestmentApiRequest(String, String, String, String, String, String, String, String, boolean)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="95" height="10" title="62" alt="62"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h1">10</td><td class="ctr2" id="i1">10</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a8"><a href="DocumentController.java.html#L95" class="el_method">lambda$getData$1(String, String, InvestmentApiRequest, ServerHttpRequest, long, ResponseEntity)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="93" height="10" title="61" alt="61"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h4">6</td><td class="ctr2" id="i4">6</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a1"><a href="DocumentController.java.html#L143" class="el_method">buildResponse(InputStream, String, String, String)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="83" height="10" title="54" alt="54"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h2">9</td><td class="ctr2" id="i2">9</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a6"><a href="DocumentController.java.html#L167" class="el_method">isValidPerformanceId(String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="10" alt="10"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="4" alt="4"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">3</td><td class="ctr2" id="g1">3</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a2"><a href="DocumentController.java.html#L56" class="el_method">DocumentController(RequestValidationHandler, DocumentService)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="9" alt="9"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h5">4</td><td class="ctr2" id="i5">4</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a10"><a href="DocumentController.java.html#L158" class="el_method">validateRequest(InvestmentApiRequest, HeadersAndParams)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="9" alt="9"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h6">3</td><td class="ctr2" id="i6">3</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a5"><a href="DocumentController.java.html#L163" class="el_method">isValidDocumentId(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="9" alt="9"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="4" alt="4"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">3</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a4"><a href="DocumentController.java.html#L139" class="el_method">getHeadersAndParams(String, String, String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="8" alt="8"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a7"><a href="DocumentController.java.html#L93" class="el_method">lambda$getData$0(String, String, String, ByteArrayInputStream)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="8" alt="8"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>