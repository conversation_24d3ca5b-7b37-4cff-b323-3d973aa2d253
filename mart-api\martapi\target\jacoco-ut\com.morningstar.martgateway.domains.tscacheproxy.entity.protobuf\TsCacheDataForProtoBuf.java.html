<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TsCacheDataForProtoBuf.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <a href="index.source.html" class="el_package">com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf</a> &gt; <span class="el_source">TsCacheDataForProtoBuf.java</span></div><h1>TsCacheDataForProtoBuf.java</h1><pre class="source lang-java linenums">// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: TsCacheData.proto

// Protobuf Java Version: 3.25.1
package com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf;

public final class TsCacheDataForProtoBuf {
  private TsCacheDataForProtoBuf() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
<span class="nc" id="L11">  }</span>

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
<span class="nc" id="L15">    registerAllExtensions(</span>
        (com.google.protobuf.ExtensionRegistryLite) registry);
<span class="nc" id="L17">  }</span>
  public interface TSValuePairOrBuilder extends
      // @@protoc_insertion_point(interface_extends:protobuf.TSValuePair)
      com.google.protobuf.MessageOrBuilder {

    /**
     * &lt;code&gt;repeated int64 dates = 1;&lt;/code&gt;
     * @return A list containing the dates.
     */
    java.util.List&lt;java.lang.Long&gt; getDatesList();
    /**
     * &lt;code&gt;repeated int64 dates = 1;&lt;/code&gt;
     * @return The count of dates.
     */
    int getDatesCount();
    /**
     * &lt;code&gt;repeated int64 dates = 1;&lt;/code&gt;
     * @param index The index of the element to return.
     * @return The dates at the given index.
     */
    long getDates(int index);

    /**
     * &lt;code&gt;repeated double values = 2;&lt;/code&gt;
     * @return A list containing the values.
     */
    java.util.List&lt;java.lang.Double&gt; getValuesList();
    /**
     * &lt;code&gt;repeated double values = 2;&lt;/code&gt;
     * @return The count of values.
     */
    int getValuesCount();
    /**
     * &lt;code&gt;repeated double values = 2;&lt;/code&gt;
     * @param index The index of the element to return.
     * @return The values at the given index.
     */
    double getValues(int index);

    /**
     * &lt;code&gt;repeated string strings = 3;&lt;/code&gt;
     * @return A list containing the strings.
     */
    java.util.List&lt;java.lang.String&gt;
        getStringsList();
    /**
     * &lt;code&gt;repeated string strings = 3;&lt;/code&gt;
     * @return The count of strings.
     */
    int getStringsCount();
    /**
     * &lt;code&gt;repeated string strings = 3;&lt;/code&gt;
     * @param index The index of the element to return.
     * @return The strings at the given index.
     */
    java.lang.String getStrings(int index);
    /**
     * &lt;code&gt;repeated string strings = 3;&lt;/code&gt;
     * @param index The index of the value to return.
     * @return The bytes of the strings at the given index.
     */
    com.google.protobuf.ByteString
        getStringsBytes(int index);
  }
  /**
   * Protobuf type {@code protobuf.TSValuePair}
   */
  public static final class TSValuePair extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:protobuf.TSValuePair)
      TSValuePairOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TSValuePair.newBuilder() to construct.
    private TSValuePair(com.google.protobuf.GeneratedMessageV3.Builder&lt;?&gt; builder) {
<span class="nc" id="L91">      super(builder);</span>
<span class="nc" id="L92">    }</span>
<span class="nc" id="L93">    private TSValuePair() {</span>
<span class="nc" id="L94">      dates_ = emptyLongList();</span>
<span class="nc" id="L95">      values_ = emptyDoubleList();</span>
<span class="nc" id="L96">      strings_ =</span>
<span class="nc" id="L97">          com.google.protobuf.LazyStringArrayList.emptyList();</span>
<span class="nc" id="L98">    }</span>

    @java.lang.Override
    @SuppressWarnings({&quot;unused&quot;})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
<span class="nc" id="L104">      return new TSValuePair();</span>
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
<span class="nc" id="L109">      return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TSValuePair_descriptor;</span>
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
<span class="nc" id="L115">      return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TSValuePair_fieldAccessorTable</span>
<span class="nc" id="L116">          .ensureFieldAccessorsInitialized(</span>
              com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.class, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.Builder.class);
    }

    public static final int DATES_FIELD_NUMBER = 1;
<span class="nc" id="L121">    @SuppressWarnings(&quot;serial&quot;)</span>
    private com.google.protobuf.Internal.LongList dates_ =
<span class="nc" id="L123">        emptyLongList();</span>
    /**
     * &lt;code&gt;repeated int64 dates = 1;&lt;/code&gt;
     * @return A list containing the dates.
     */
    @java.lang.Override
    public java.util.List&lt;java.lang.Long&gt;
        getDatesList() {
<span class="nc" id="L131">      return dates_;</span>
    }
    /**
     * &lt;code&gt;repeated int64 dates = 1;&lt;/code&gt;
     * @return The count of dates.
     */
    public int getDatesCount() {
<span class="nc" id="L138">      return dates_.size();</span>
    }
    /**
     * &lt;code&gt;repeated int64 dates = 1;&lt;/code&gt;
     * @param index The index of the element to return.
     * @return The dates at the given index.
     */
    public long getDates(int index) {
<span class="nc" id="L146">      return dates_.getLong(index);</span>
    }

    public static final int VALUES_FIELD_NUMBER = 2;
<span class="nc" id="L150">    @SuppressWarnings(&quot;serial&quot;)</span>
    private com.google.protobuf.Internal.DoubleList values_ =
<span class="nc" id="L152">        emptyDoubleList();</span>
    /**
     * &lt;code&gt;repeated double values = 2;&lt;/code&gt;
     * @return A list containing the values.
     */
    @java.lang.Override
    public java.util.List&lt;java.lang.Double&gt;
        getValuesList() {
<span class="nc" id="L160">      return values_;</span>
    }
    /**
     * &lt;code&gt;repeated double values = 2;&lt;/code&gt;
     * @return The count of values.
     */
    public int getValuesCount() {
<span class="nc" id="L167">      return values_.size();</span>
    }
    /**
     * &lt;code&gt;repeated double values = 2;&lt;/code&gt;
     * @param index The index of the element to return.
     * @return The values at the given index.
     */
    public double getValues(int index) {
<span class="nc" id="L175">      return values_.getDouble(index);</span>
    }

    public static final int STRINGS_FIELD_NUMBER = 3;
<span class="nc" id="L179">    @SuppressWarnings(&quot;serial&quot;)</span>
    private com.google.protobuf.LazyStringArrayList strings_ =
<span class="nc" id="L181">        com.google.protobuf.LazyStringArrayList.emptyList();</span>
    /**
     * &lt;code&gt;repeated string strings = 3;&lt;/code&gt;
     * @return A list containing the strings.
     */
    public com.google.protobuf.ProtocolStringList
        getStringsList() {
<span class="nc" id="L188">      return strings_;</span>
    }
    /**
     * &lt;code&gt;repeated string strings = 3;&lt;/code&gt;
     * @return The count of strings.
     */
    public int getStringsCount() {
<span class="nc" id="L195">      return strings_.size();</span>
    }
    /**
     * &lt;code&gt;repeated string strings = 3;&lt;/code&gt;
     * @param index The index of the element to return.
     * @return The strings at the given index.
     */
    public java.lang.String getStrings(int index) {
<span class="nc" id="L203">      return strings_.get(index);</span>
    }
    /**
     * &lt;code&gt;repeated string strings = 3;&lt;/code&gt;
     * @param index The index of the value to return.
     * @return The bytes of the strings at the given index.
     */
    public com.google.protobuf.ByteString
        getStringsBytes(int index) {
<span class="nc" id="L212">      return strings_.getByteString(index);</span>
    }

<span class="nc" id="L215">    private byte memoizedIsInitialized = -1;</span>
    @java.lang.Override
    public final boolean isInitialized() {
<span class="nc" id="L218">      byte isInitialized = memoizedIsInitialized;</span>
<span class="nc bnc" id="L219" title="All 2 branches missed.">      if (isInitialized == 1) return true;</span>
<span class="nc bnc" id="L220" title="All 2 branches missed.">      if (isInitialized == 0) return false;</span>

<span class="nc" id="L222">      memoizedIsInitialized = 1;</span>
<span class="nc" id="L223">      return true;</span>
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
<span class="nc bnc" id="L229" title="All 2 branches missed.">      for (int i = 0; i &lt; dates_.size(); i++) {</span>
<span class="nc" id="L230">        output.writeInt64(1, dates_.getLong(i));</span>
      }
<span class="nc bnc" id="L232" title="All 2 branches missed.">      for (int i = 0; i &lt; values_.size(); i++) {</span>
<span class="nc" id="L233">        output.writeDouble(2, values_.getDouble(i));</span>
      }
<span class="nc bnc" id="L235" title="All 2 branches missed.">      for (int i = 0; i &lt; strings_.size(); i++) {</span>
<span class="nc" id="L236">        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, strings_.getRaw(i));</span>
      }
<span class="nc" id="L238">      getUnknownFields().writeTo(output);</span>
<span class="nc" id="L239">    }</span>

    @java.lang.Override
    public int getSerializedSize() {
<span class="nc" id="L243">      int size = memoizedSize;</span>
<span class="nc bnc" id="L244" title="All 2 branches missed.">      if (size != -1) return size;</span>

<span class="nc" id="L246">      size = 0;</span>
      {
<span class="nc" id="L248">        int dataSize = 0;</span>
<span class="nc bnc" id="L249" title="All 2 branches missed.">        for (int i = 0; i &lt; dates_.size(); i++) {</span>
<span class="nc" id="L250">          dataSize += com.google.protobuf.CodedOutputStream</span>
<span class="nc" id="L251">            .computeInt64SizeNoTag(dates_.getLong(i));</span>
        }
<span class="nc" id="L253">        size += dataSize;</span>
<span class="nc" id="L254">        size += 1 * getDatesList().size();</span>
      }
      {
<span class="nc" id="L257">        int dataSize = 0;</span>
<span class="nc" id="L258">        dataSize = 8 * getValuesList().size();</span>
<span class="nc" id="L259">        size += dataSize;</span>
<span class="nc" id="L260">        size += 1 * getValuesList().size();</span>
      }
      {
<span class="nc" id="L263">        int dataSize = 0;</span>
<span class="nc bnc" id="L264" title="All 2 branches missed.">        for (int i = 0; i &lt; strings_.size(); i++) {</span>
<span class="nc" id="L265">          dataSize += computeStringSizeNoTag(strings_.getRaw(i));</span>
        }
<span class="nc" id="L267">        size += dataSize;</span>
<span class="nc" id="L268">        size += 1 * getStringsList().size();</span>
      }
<span class="nc" id="L270">      size += getUnknownFields().getSerializedSize();</span>
<span class="nc" id="L271">      memoizedSize = size;</span>
<span class="nc" id="L272">      return size;</span>
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
<span class="nc bnc" id="L277" title="All 2 branches missed.">      if (obj == this) {</span>
<span class="nc" id="L278">       return true;</span>
      }
<span class="nc bnc" id="L280" title="All 2 branches missed.">      if (!(obj instanceof com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair)) {</span>
<span class="nc" id="L281">        return super.equals(obj);</span>
      }
<span class="nc" id="L283">      com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair other = (com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair) obj;</span>

<span class="nc" id="L285">      if (!getDatesList()</span>
<span class="nc bnc" id="L286" title="All 2 branches missed.">          .equals(other.getDatesList())) return false;</span>
<span class="nc" id="L287">      if (!getValuesList()</span>
<span class="nc bnc" id="L288" title="All 2 branches missed.">          .equals(other.getValuesList())) return false;</span>
<span class="nc" id="L289">      if (!getStringsList()</span>
<span class="nc bnc" id="L290" title="All 2 branches missed.">          .equals(other.getStringsList())) return false;</span>
<span class="nc bnc" id="L291" title="All 2 branches missed.">      if (!getUnknownFields().equals(other.getUnknownFields())) return false;</span>
<span class="nc" id="L292">      return true;</span>
    }

    @java.lang.Override
    public int hashCode() {
<span class="nc bnc" id="L297" title="All 2 branches missed.">      if (memoizedHashCode != 0) {</span>
<span class="nc" id="L298">        return memoizedHashCode;</span>
      }
<span class="nc" id="L300">      int hash = 41;</span>
<span class="nc" id="L301">      hash = (19 * hash) + getDescriptor().hashCode();</span>
<span class="nc bnc" id="L302" title="All 2 branches missed.">      if (getDatesCount() &gt; 0) {</span>
<span class="nc" id="L303">        hash = (37 * hash) + DATES_FIELD_NUMBER;</span>
<span class="nc" id="L304">        hash = (53 * hash) + getDatesList().hashCode();</span>
      }
<span class="nc bnc" id="L306" title="All 2 branches missed.">      if (getValuesCount() &gt; 0) {</span>
<span class="nc" id="L307">        hash = (37 * hash) + VALUES_FIELD_NUMBER;</span>
<span class="nc" id="L308">        hash = (53 * hash) + getValuesList().hashCode();</span>
      }
<span class="nc bnc" id="L310" title="All 2 branches missed.">      if (getStringsCount() &gt; 0) {</span>
<span class="nc" id="L311">        hash = (37 * hash) + STRINGS_FIELD_NUMBER;</span>
<span class="nc" id="L312">        hash = (53 * hash) + getStringsList().hashCode();</span>
      }
<span class="nc" id="L314">      hash = (29 * hash) + getUnknownFields().hashCode();</span>
<span class="nc" id="L315">      memoizedHashCode = hash;</span>
<span class="nc" id="L316">      return hash;</span>
    }

    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L322">      return PARSER.parseFrom(data);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L328">      return PARSER.parseFrom(data, extensionRegistry);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L333">      return PARSER.parseFrom(data);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L339">      return PARSER.parseFrom(data, extensionRegistry);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L343">      return PARSER.parseFrom(data);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L349">      return PARSER.parseFrom(data, extensionRegistry);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair parseFrom(java.io.InputStream input)
        throws java.io.IOException {
<span class="nc" id="L353">      return com.google.protobuf.GeneratedMessageV3</span>
<span class="nc" id="L354">          .parseWithIOException(PARSER, input);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
<span class="nc" id="L360">      return com.google.protobuf.GeneratedMessageV3</span>
<span class="nc" id="L361">          .parseWithIOException(PARSER, input, extensionRegistry);</span>
    }

    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
<span class="nc" id="L366">      return com.google.protobuf.GeneratedMessageV3</span>
<span class="nc" id="L367">          .parseDelimitedWithIOException(PARSER, input);</span>
    }

    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
<span class="nc" id="L374">      return com.google.protobuf.GeneratedMessageV3</span>
<span class="nc" id="L375">          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
<span class="nc" id="L380">      return com.google.protobuf.GeneratedMessageV3</span>
<span class="nc" id="L381">          .parseWithIOException(PARSER, input);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
<span class="nc" id="L387">      return com.google.protobuf.GeneratedMessageV3</span>
<span class="nc" id="L388">          .parseWithIOException(PARSER, input, extensionRegistry);</span>
    }

    @java.lang.Override
<span class="nc" id="L392">    public Builder newBuilderForType() { return newBuilder(); }</span>
    public static Builder newBuilder() {
<span class="nc" id="L394">      return DEFAULT_INSTANCE.toBuilder();</span>
    }
    public static Builder newBuilder(com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair prototype) {
<span class="nc" id="L397">      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);</span>
    }
    @java.lang.Override
    public Builder toBuilder() {
<span class="nc bnc" id="L401" title="All 2 branches missed.">      return this == DEFAULT_INSTANCE</span>
<span class="nc" id="L402">          ? new Builder() : new Builder().mergeFrom(this);</span>
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
<span class="nc" id="L408">      Builder builder = new Builder(parent);</span>
<span class="nc" id="L409">      return builder;</span>
    }
    /**
     * Protobuf type {@code protobuf.TSValuePair}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder&lt;Builder&gt; implements
        // @@protoc_insertion_point(builder_implements:protobuf.TSValuePair)
        com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePairOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
<span class="nc" id="L420">        return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TSValuePair_descriptor;</span>
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
<span class="nc" id="L426">        return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TSValuePair_fieldAccessorTable</span>
<span class="nc" id="L427">            .ensureFieldAccessorsInitialized(</span>
                com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.class, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.Builder.class);
      }

      // Construct using com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.newBuilder()
<span class="nc" id="L432">      private Builder() {</span>

<span class="nc" id="L434">      }</span>

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
<span class="nc" id="L438">        super(parent);</span>

<span class="nc" id="L440">      }</span>
      @java.lang.Override
      public Builder clear() {
<span class="nc" id="L443">        super.clear();</span>
<span class="nc" id="L444">        bitField0_ = 0;</span>
<span class="nc" id="L445">        dates_ = emptyLongList();</span>
<span class="nc" id="L446">        values_ = emptyDoubleList();</span>
<span class="nc" id="L447">        strings_ =</span>
<span class="nc" id="L448">            com.google.protobuf.LazyStringArrayList.emptyList();</span>
<span class="nc" id="L449">        return this;</span>
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
<span class="nc" id="L455">        return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TSValuePair_descriptor;</span>
      }

      @java.lang.Override
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair getDefaultInstanceForType() {
<span class="nc" id="L460">        return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.getDefaultInstance();</span>
      }

      @java.lang.Override
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair build() {
<span class="nc" id="L465">        com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair result = buildPartial();</span>
<span class="nc bnc" id="L466" title="All 2 branches missed.">        if (!result.isInitialized()) {</span>
<span class="nc" id="L467">          throw newUninitializedMessageException(result);</span>
        }
<span class="nc" id="L469">        return result;</span>
      }

      @java.lang.Override
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair buildPartial() {
<span class="nc" id="L474">        com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair result = new com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair(this);</span>
<span class="nc bnc" id="L475" title="All 2 branches missed.">        if (bitField0_ != 0) { buildPartial0(result); }</span>
<span class="nc" id="L476">        onBuilt();</span>
<span class="nc" id="L477">        return result;</span>
      }

      private void buildPartial0(com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair result) {
<span class="nc" id="L481">        int from_bitField0_ = bitField0_;</span>
<span class="nc bnc" id="L482" title="All 2 branches missed.">        if (((from_bitField0_ &amp; 0x00000001) != 0)) {</span>
<span class="nc" id="L483">          dates_.makeImmutable();</span>
<span class="nc" id="L484">          result.dates_ = dates_;</span>
        }
<span class="nc bnc" id="L486" title="All 2 branches missed.">        if (((from_bitField0_ &amp; 0x00000002) != 0)) {</span>
<span class="nc" id="L487">          values_.makeImmutable();</span>
<span class="nc" id="L488">          result.values_ = values_;</span>
        }
<span class="nc bnc" id="L490" title="All 2 branches missed.">        if (((from_bitField0_ &amp; 0x00000004) != 0)) {</span>
<span class="nc" id="L491">          strings_.makeImmutable();</span>
<span class="nc" id="L492">          result.strings_ = strings_;</span>
        }
<span class="nc" id="L494">      }</span>

      @java.lang.Override
      public Builder clone() {
<span class="nc" id="L498">        return super.clone();</span>
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
<span class="nc" id="L504">        return super.setField(field, value);</span>
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
<span class="nc" id="L509">        return super.clearField(field);</span>
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
<span class="nc" id="L514">        return super.clearOneof(oneof);</span>
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
<span class="nc" id="L520">        return super.setRepeatedField(field, index, value);</span>
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
<span class="nc" id="L526">        return super.addRepeatedField(field, value);</span>
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
<span class="nc bnc" id="L530" title="All 2 branches missed.">        if (other instanceof com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair) {</span>
<span class="nc" id="L531">          return mergeFrom((com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair)other);</span>
        } else {
<span class="nc" id="L533">          super.mergeFrom(other);</span>
<span class="nc" id="L534">          return this;</span>
        }
      }

      public Builder mergeFrom(com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair other) {
<span class="nc bnc" id="L539" title="All 2 branches missed.">        if (other == com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.getDefaultInstance()) return this;</span>
<span class="nc bnc" id="L540" title="All 2 branches missed.">        if (!other.dates_.isEmpty()) {</span>
<span class="nc bnc" id="L541" title="All 2 branches missed.">          if (dates_.isEmpty()) {</span>
<span class="nc" id="L542">            dates_ = other.dates_;</span>
<span class="nc" id="L543">            dates_.makeImmutable();</span>
<span class="nc" id="L544">            bitField0_ |= 0x00000001;</span>
          } else {
<span class="nc" id="L546">            ensureDatesIsMutable();</span>
<span class="nc" id="L547">            dates_.addAll(other.dates_);</span>
          }
<span class="nc" id="L549">          onChanged();</span>
        }
<span class="nc bnc" id="L551" title="All 2 branches missed.">        if (!other.values_.isEmpty()) {</span>
<span class="nc bnc" id="L552" title="All 2 branches missed.">          if (values_.isEmpty()) {</span>
<span class="nc" id="L553">            values_ = other.values_;</span>
<span class="nc" id="L554">            values_.makeImmutable();</span>
<span class="nc" id="L555">            bitField0_ |= 0x00000002;</span>
          } else {
<span class="nc" id="L557">            ensureValuesIsMutable();</span>
<span class="nc" id="L558">            values_.addAll(other.values_);</span>
          }
<span class="nc" id="L560">          onChanged();</span>
        }
<span class="nc bnc" id="L562" title="All 2 branches missed.">        if (!other.strings_.isEmpty()) {</span>
<span class="nc bnc" id="L563" title="All 2 branches missed.">          if (strings_.isEmpty()) {</span>
<span class="nc" id="L564">            strings_ = other.strings_;</span>
<span class="nc" id="L565">            bitField0_ |= 0x00000004;</span>
          } else {
<span class="nc" id="L567">            ensureStringsIsMutable();</span>
<span class="nc" id="L568">            strings_.addAll(other.strings_);</span>
          }
<span class="nc" id="L570">          onChanged();</span>
        }
<span class="nc" id="L572">        this.mergeUnknownFields(other.getUnknownFields());</span>
<span class="nc" id="L573">        onChanged();</span>
<span class="nc" id="L574">        return this;</span>
      }

      @java.lang.Override
      public final boolean isInitialized() {
<span class="nc" id="L579">        return true;</span>
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
<span class="nc bnc" id="L587" title="All 2 branches missed.">        if (extensionRegistry == null) {</span>
<span class="nc" id="L588">          throw new java.lang.NullPointerException();</span>
        }
        try {
<span class="nc" id="L591">          boolean done = false;</span>
<span class="nc bnc" id="L592" title="All 2 branches missed.">          while (!done) {</span>
<span class="nc" id="L593">            int tag = input.readTag();</span>
<span class="nc bnc" id="L594" title="All 7 branches missed.">            switch (tag) {</span>
              case 0:
<span class="nc" id="L596">                done = true;</span>
<span class="nc" id="L597">                break;</span>
              case 8: {
<span class="nc" id="L599">                long v = input.readInt64();</span>
<span class="nc" id="L600">                ensureDatesIsMutable();</span>
<span class="nc" id="L601">                dates_.addLong(v);</span>
<span class="nc" id="L602">                break;</span>
              } // case 8
              case 10: {
<span class="nc" id="L605">                int length = input.readRawVarint32();</span>
<span class="nc" id="L606">                int limit = input.pushLimit(length);</span>
<span class="nc" id="L607">                ensureDatesIsMutable();</span>
<span class="nc bnc" id="L608" title="All 2 branches missed.">                while (input.getBytesUntilLimit() &gt; 0) {</span>
<span class="nc" id="L609">                  dates_.addLong(input.readInt64());</span>
                }
<span class="nc" id="L611">                input.popLimit(limit);</span>
<span class="nc" id="L612">                break;</span>
              } // case 10
              case 17: {
<span class="nc" id="L615">                double v = input.readDouble();</span>
<span class="nc" id="L616">                ensureValuesIsMutable();</span>
<span class="nc" id="L617">                values_.addDouble(v);</span>
<span class="nc" id="L618">                break;</span>
              } // case 17
              case 18: {
<span class="nc" id="L621">                int length = input.readRawVarint32();</span>
<span class="nc" id="L622">                int limit = input.pushLimit(length);</span>
<span class="nc bnc" id="L623" title="All 2 branches missed.">                int alloc = length &gt; 4096 ? 4096 : length;</span>
<span class="nc" id="L624">                ensureValuesIsMutable(alloc / 8);</span>
<span class="nc bnc" id="L625" title="All 2 branches missed.">                while (input.getBytesUntilLimit() &gt; 0) {</span>
<span class="nc" id="L626">                  values_.addDouble(input.readDouble());</span>
                }
<span class="nc" id="L628">                input.popLimit(limit);</span>
<span class="nc" id="L629">                break;</span>
              } // case 18
              case 26: {
<span class="nc" id="L632">                com.google.protobuf.ByteString bs = input.readBytes();</span>
<span class="nc" id="L633">                ensureStringsIsMutable();</span>
<span class="nc" id="L634">                strings_.add(bs);</span>
<span class="nc" id="L635">                break;</span>
              } // case 26
              default: {
<span class="nc bnc" id="L638" title="All 2 branches missed.">                if (!super.parseUnknownField(input, extensionRegistry, tag)) {</span>
<span class="nc" id="L639">                  done = true; // was an endgroup tag</span>
                }
                break;
              } // default:
            } // switch (tag)
<span class="nc" id="L644">          } // while (!done)</span>
<span class="nc" id="L645">        } catch (com.google.protobuf.InvalidProtocolBufferException e) {</span>
<span class="nc" id="L646">          throw e.unwrapIOException();</span>
        } finally {
<span class="nc" id="L648">          onChanged();</span>
        } // finally
<span class="nc" id="L650">        return this;</span>
      }
      private int bitField0_;

<span class="nc" id="L654">      private com.google.protobuf.Internal.LongList dates_ = emptyLongList();</span>
      private void ensureDatesIsMutable() {
<span class="nc bnc" id="L656" title="All 2 branches missed.">        if (!dates_.isModifiable()) {</span>
<span class="nc" id="L657">          dates_ = makeMutableCopy(dates_);</span>
        }
<span class="nc" id="L659">        bitField0_ |= 0x00000001;</span>
<span class="nc" id="L660">      }</span>
      /**
       * &lt;code&gt;repeated int64 dates = 1;&lt;/code&gt;
       * @return A list containing the dates.
       */
      public java.util.List&lt;java.lang.Long&gt;
          getDatesList() {
<span class="nc" id="L667">        dates_.makeImmutable();</span>
<span class="nc" id="L668">        return dates_;</span>
      }
      /**
       * &lt;code&gt;repeated int64 dates = 1;&lt;/code&gt;
       * @return The count of dates.
       */
      public int getDatesCount() {
<span class="nc" id="L675">        return dates_.size();</span>
      }
      /**
       * &lt;code&gt;repeated int64 dates = 1;&lt;/code&gt;
       * @param index The index of the element to return.
       * @return The dates at the given index.
       */
      public long getDates(int index) {
<span class="nc" id="L683">        return dates_.getLong(index);</span>
      }
      /**
       * &lt;code&gt;repeated int64 dates = 1;&lt;/code&gt;
       * @param index The index to set the value at.
       * @param value The dates to set.
       * @return This builder for chaining.
       */
      public Builder setDates(
          int index, long value) {

<span class="nc" id="L694">        ensureDatesIsMutable();</span>
<span class="nc" id="L695">        dates_.setLong(index, value);</span>
<span class="nc" id="L696">        bitField0_ |= 0x00000001;</span>
<span class="nc" id="L697">        onChanged();</span>
<span class="nc" id="L698">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated int64 dates = 1;&lt;/code&gt;
       * @param value The dates to add.
       * @return This builder for chaining.
       */
      public Builder addDates(long value) {

<span class="nc" id="L707">        ensureDatesIsMutable();</span>
<span class="nc" id="L708">        dates_.addLong(value);</span>
<span class="nc" id="L709">        bitField0_ |= 0x00000001;</span>
<span class="nc" id="L710">        onChanged();</span>
<span class="nc" id="L711">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated int64 dates = 1;&lt;/code&gt;
       * @param values The dates to add.
       * @return This builder for chaining.
       */
      public Builder addAllDates(
          java.lang.Iterable&lt;? extends java.lang.Long&gt; values) {
<span class="nc" id="L720">        ensureDatesIsMutable();</span>
<span class="nc" id="L721">        com.google.protobuf.AbstractMessageLite.Builder.addAll(</span>
            values, dates_);
<span class="nc" id="L723">        bitField0_ |= 0x00000001;</span>
<span class="nc" id="L724">        onChanged();</span>
<span class="nc" id="L725">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated int64 dates = 1;&lt;/code&gt;
       * @return This builder for chaining.
       */
      public Builder clearDates() {
<span class="nc" id="L732">        dates_ = emptyLongList();</span>
<span class="nc" id="L733">        bitField0_ = (bitField0_ &amp; ~0x00000001);</span>
<span class="nc" id="L734">        onChanged();</span>
<span class="nc" id="L735">        return this;</span>
      }

<span class="nc" id="L738">      private com.google.protobuf.Internal.DoubleList values_ = emptyDoubleList();</span>
      private void ensureValuesIsMutable() {
<span class="nc bnc" id="L740" title="All 2 branches missed.">        if (!values_.isModifiable()) {</span>
<span class="nc" id="L741">          values_ = makeMutableCopy(values_);</span>
        }
<span class="nc" id="L743">        bitField0_ |= 0x00000002;</span>
<span class="nc" id="L744">      }</span>
      private void ensureValuesIsMutable(int capacity) {
<span class="nc bnc" id="L746" title="All 2 branches missed.">        if (!values_.isModifiable()) {</span>
<span class="nc" id="L747">          values_ = makeMutableCopy(values_, capacity);</span>
        }
<span class="nc" id="L749">        bitField0_ |= 0x00000002;</span>
<span class="nc" id="L750">      }</span>
      /**
       * &lt;code&gt;repeated double values = 2;&lt;/code&gt;
       * @return A list containing the values.
       */
      public java.util.List&lt;java.lang.Double&gt;
          getValuesList() {
<span class="nc" id="L757">        values_.makeImmutable();</span>
<span class="nc" id="L758">        return values_;</span>
      }
      /**
       * &lt;code&gt;repeated double values = 2;&lt;/code&gt;
       * @return The count of values.
       */
      public int getValuesCount() {
<span class="nc" id="L765">        return values_.size();</span>
      }
      /**
       * &lt;code&gt;repeated double values = 2;&lt;/code&gt;
       * @param index The index of the element to return.
       * @return The values at the given index.
       */
      public double getValues(int index) {
<span class="nc" id="L773">        return values_.getDouble(index);</span>
      }
      /**
       * &lt;code&gt;repeated double values = 2;&lt;/code&gt;
       * @param index The index to set the value at.
       * @param value The values to set.
       * @return This builder for chaining.
       */
      public Builder setValues(
          int index, double value) {

<span class="nc" id="L784">        ensureValuesIsMutable();</span>
<span class="nc" id="L785">        values_.setDouble(index, value);</span>
<span class="nc" id="L786">        bitField0_ |= 0x00000002;</span>
<span class="nc" id="L787">        onChanged();</span>
<span class="nc" id="L788">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated double values = 2;&lt;/code&gt;
       * @param value The values to add.
       * @return This builder for chaining.
       */
      public Builder addValues(double value) {

<span class="nc" id="L797">        ensureValuesIsMutable();</span>
<span class="nc" id="L798">        values_.addDouble(value);</span>
<span class="nc" id="L799">        bitField0_ |= 0x00000002;</span>
<span class="nc" id="L800">        onChanged();</span>
<span class="nc" id="L801">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated double values = 2;&lt;/code&gt;
       * @param values The values to add.
       * @return This builder for chaining.
       */
      public Builder addAllValues(
          java.lang.Iterable&lt;? extends java.lang.Double&gt; values) {
<span class="nc" id="L810">        ensureValuesIsMutable();</span>
<span class="nc" id="L811">        com.google.protobuf.AbstractMessageLite.Builder.addAll(</span>
            values, values_);
<span class="nc" id="L813">        bitField0_ |= 0x00000002;</span>
<span class="nc" id="L814">        onChanged();</span>
<span class="nc" id="L815">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated double values = 2;&lt;/code&gt;
       * @return This builder for chaining.
       */
      public Builder clearValues() {
<span class="nc" id="L822">        values_ = emptyDoubleList();</span>
<span class="nc" id="L823">        bitField0_ = (bitField0_ &amp; ~0x00000002);</span>
<span class="nc" id="L824">        onChanged();</span>
<span class="nc" id="L825">        return this;</span>
      }

<span class="nc" id="L828">      private com.google.protobuf.LazyStringArrayList strings_ =</span>
<span class="nc" id="L829">          com.google.protobuf.LazyStringArrayList.emptyList();</span>
      private void ensureStringsIsMutable() {
<span class="nc bnc" id="L831" title="All 2 branches missed.">        if (!strings_.isModifiable()) {</span>
<span class="nc" id="L832">          strings_ = new com.google.protobuf.LazyStringArrayList(strings_);</span>
        }
<span class="nc" id="L834">        bitField0_ |= 0x00000004;</span>
<span class="nc" id="L835">      }</span>
      /**
       * &lt;code&gt;repeated string strings = 3;&lt;/code&gt;
       * @return A list containing the strings.
       */
      public com.google.protobuf.ProtocolStringList
          getStringsList() {
<span class="nc" id="L842">        strings_.makeImmutable();</span>
<span class="nc" id="L843">        return strings_;</span>
      }
      /**
       * &lt;code&gt;repeated string strings = 3;&lt;/code&gt;
       * @return The count of strings.
       */
      public int getStringsCount() {
<span class="nc" id="L850">        return strings_.size();</span>
      }
      /**
       * &lt;code&gt;repeated string strings = 3;&lt;/code&gt;
       * @param index The index of the element to return.
       * @return The strings at the given index.
       */
      public java.lang.String getStrings(int index) {
<span class="nc" id="L858">        return strings_.get(index);</span>
      }
      /**
       * &lt;code&gt;repeated string strings = 3;&lt;/code&gt;
       * @param index The index of the value to return.
       * @return The bytes of the strings at the given index.
       */
      public com.google.protobuf.ByteString
          getStringsBytes(int index) {
<span class="nc" id="L867">        return strings_.getByteString(index);</span>
      }
      /**
       * &lt;code&gt;repeated string strings = 3;&lt;/code&gt;
       * @param index The index to set the value at.
       * @param value The strings to set.
       * @return This builder for chaining.
       */
      public Builder setStrings(
          int index, java.lang.String value) {
<span class="nc bnc" id="L877" title="All 2 branches missed.">        if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L878">        ensureStringsIsMutable();</span>
<span class="nc" id="L879">        strings_.set(index, value);</span>
<span class="nc" id="L880">        bitField0_ |= 0x00000004;</span>
<span class="nc" id="L881">        onChanged();</span>
<span class="nc" id="L882">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated string strings = 3;&lt;/code&gt;
       * @param value The strings to add.
       * @return This builder for chaining.
       */
      public Builder addStrings(
          java.lang.String value) {
<span class="nc bnc" id="L891" title="All 2 branches missed.">        if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L892">        ensureStringsIsMutable();</span>
<span class="nc" id="L893">        strings_.add(value);</span>
<span class="nc" id="L894">        bitField0_ |= 0x00000004;</span>
<span class="nc" id="L895">        onChanged();</span>
<span class="nc" id="L896">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated string strings = 3;&lt;/code&gt;
       * @param values The strings to add.
       * @return This builder for chaining.
       */
      public Builder addAllStrings(
          java.lang.Iterable&lt;java.lang.String&gt; values) {
<span class="nc" id="L905">        ensureStringsIsMutable();</span>
<span class="nc" id="L906">        com.google.protobuf.AbstractMessageLite.Builder.addAll(</span>
            values, strings_);
<span class="nc" id="L908">        bitField0_ |= 0x00000004;</span>
<span class="nc" id="L909">        onChanged();</span>
<span class="nc" id="L910">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated string strings = 3;&lt;/code&gt;
       * @return This builder for chaining.
       */
      public Builder clearStrings() {
<span class="nc" id="L917">        strings_ =</span>
<span class="nc" id="L918">          com.google.protobuf.LazyStringArrayList.emptyList();</span>
<span class="nc" id="L919">        bitField0_ = (bitField0_ &amp; ~0x00000004);;</span>
<span class="nc" id="L920">        onChanged();</span>
<span class="nc" id="L921">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated string strings = 3;&lt;/code&gt;
       * @param value The bytes of the strings to add.
       * @return This builder for chaining.
       */
      public Builder addStringsBytes(
          com.google.protobuf.ByteString value) {
<span class="nc bnc" id="L930" title="All 2 branches missed.">        if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L931">        ensureStringsIsMutable();</span>
<span class="nc" id="L932">        strings_.add(value);</span>
<span class="nc" id="L933">        bitField0_ |= 0x00000004;</span>
<span class="nc" id="L934">        onChanged();</span>
<span class="nc" id="L935">        return this;</span>
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
<span class="nc" id="L940">        return super.setUnknownFields(unknownFields);</span>
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
<span class="nc" id="L946">        return super.mergeUnknownFields(unknownFields);</span>
      }


      // @@protoc_insertion_point(builder_scope:protobuf.TSValuePair)
    }

    // @@protoc_insertion_point(class_scope:protobuf.TSValuePair)
    private static final com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair DEFAULT_INSTANCE;
    static {
<span class="nc" id="L956">      DEFAULT_INSTANCE = new com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair();</span>
    }

    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair getDefaultInstance() {
<span class="nc" id="L960">      return DEFAULT_INSTANCE;</span>
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser&lt;TSValuePair&gt;
<span class="nc" id="L964">        PARSER = new com.google.protobuf.AbstractParser&lt;TSValuePair&gt;() {</span>
      @java.lang.Override
      public TSValuePair parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L970">        Builder builder = newBuilder();</span>
        try {
<span class="nc" id="L972">          builder.mergeFrom(input, extensionRegistry);</span>
<span class="nc" id="L973">        } catch (com.google.protobuf.InvalidProtocolBufferException e) {</span>
<span class="nc" id="L974">          throw e.setUnfinishedMessage(builder.buildPartial());</span>
<span class="nc" id="L975">        } catch (com.google.protobuf.UninitializedMessageException e) {</span>
<span class="nc" id="L976">          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());</span>
<span class="nc" id="L977">        } catch (java.io.IOException e) {</span>
<span class="nc" id="L978">          throw new com.google.protobuf.InvalidProtocolBufferException(e)</span>
<span class="nc" id="L979">              .setUnfinishedMessage(builder.buildPartial());</span>
<span class="nc" id="L980">        }</span>
<span class="nc" id="L981">        return builder.buildPartial();</span>
      }
    };

    public static com.google.protobuf.Parser&lt;TSValuePair&gt; parser() {
<span class="nc" id="L986">      return PARSER;</span>
    }

    @java.lang.Override
    public com.google.protobuf.Parser&lt;TSValuePair&gt; getParserForType() {
<span class="nc" id="L991">      return PARSER;</span>
    }

    @java.lang.Override
    public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair getDefaultInstanceForType() {
<span class="nc" id="L996">      return DEFAULT_INSTANCE;</span>
    }

  }

  public interface TimeSeriesDatasOrBuilder extends
      // @@protoc_insertion_point(interface_extends:protobuf.TimeSeriesDatas)
      com.google.protobuf.MessageOrBuilder {

    /**
     * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
     */
    java.util.List&lt;com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData&gt; 
        getValuesList();
    /**
     * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
     */
    com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData getValues(int index);
    /**
     * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
     */
    int getValuesCount();
    /**
     * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
     */
    java.util.List&lt;? extends com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDataOrBuilder&gt; 
        getValuesOrBuilderList();
    /**
     * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
     */
    com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDataOrBuilder getValuesOrBuilder(
        int index);

    /**
     * &lt;code&gt;optional int64 retcode = 102;&lt;/code&gt;
     * @return Whether the retcode field is set.
     */
    boolean hasRetcode();
    /**
     * &lt;code&gt;optional int64 retcode = 102;&lt;/code&gt;
     * @return The retcode.
     */
    long getRetcode();

    /**
     * &lt;code&gt;optional string msg = 103;&lt;/code&gt;
     * @return Whether the msg field is set.
     */
    boolean hasMsg();
    /**
     * &lt;code&gt;optional string msg = 103;&lt;/code&gt;
     * @return The msg.
     */
    java.lang.String getMsg();
    /**
     * &lt;code&gt;optional string msg = 103;&lt;/code&gt;
     * @return The bytes for msg.
     */
    com.google.protobuf.ByteString
        getMsgBytes();
  }
  /**
   * Protobuf type {@code protobuf.TimeSeriesDatas}
   */
  public static final class TimeSeriesDatas extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:protobuf.TimeSeriesDatas)
      TimeSeriesDatasOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TimeSeriesDatas.newBuilder() to construct.
    private TimeSeriesDatas(com.google.protobuf.GeneratedMessageV3.Builder&lt;?&gt; builder) {
<span class="nc" id="L1067">      super(builder);</span>
<span class="nc" id="L1068">    }</span>
<span class="nc" id="L1069">    private TimeSeriesDatas() {</span>
<span class="nc" id="L1070">      values_ = java.util.Collections.emptyList();</span>
<span class="nc" id="L1071">      msg_ = &quot;&quot;;</span>
<span class="nc" id="L1072">    }</span>

    @java.lang.Override
    @SuppressWarnings({&quot;unused&quot;})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
<span class="nc" id="L1078">      return new TimeSeriesDatas();</span>
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
<span class="nc" id="L1083">      return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TimeSeriesDatas_descriptor;</span>
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
<span class="nc" id="L1089">      return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TimeSeriesDatas_fieldAccessorTable</span>
<span class="nc" id="L1090">          .ensureFieldAccessorsInitialized(</span>
              com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas.class, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas.Builder.class);
    }

    private int bitField0_;
    public static final int VALUES_FIELD_NUMBER = 101;
    @SuppressWarnings(&quot;serial&quot;)
    private java.util.List&lt;com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData&gt; values_;
    /**
     * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
     */
    @java.lang.Override
    public java.util.List&lt;com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData&gt; getValuesList() {
<span class="nc" id="L1103">      return values_;</span>
    }
    /**
     * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
     */
    @java.lang.Override
    public java.util.List&lt;? extends com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDataOrBuilder&gt; 
        getValuesOrBuilderList() {
<span class="nc" id="L1111">      return values_;</span>
    }
    /**
     * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
     */
    @java.lang.Override
    public int getValuesCount() {
<span class="nc" id="L1118">      return values_.size();</span>
    }
    /**
     * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
     */
    @java.lang.Override
    public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData getValues(int index) {
<span class="nc" id="L1125">      return values_.get(index);</span>
    }
    /**
     * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
     */
    @java.lang.Override
    public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDataOrBuilder getValuesOrBuilder(
        int index) {
<span class="nc" id="L1133">      return values_.get(index);</span>
    }

    public static final int RETCODE_FIELD_NUMBER = 102;
<span class="nc" id="L1137">    private long retcode_ = 0L;</span>
    /**
     * &lt;code&gt;optional int64 retcode = 102;&lt;/code&gt;
     * @return Whether the retcode field is set.
     */
    @java.lang.Override
    public boolean hasRetcode() {
<span class="nc bnc" id="L1144" title="All 2 branches missed.">      return ((bitField0_ &amp; 0x00000001) != 0);</span>
    }
    /**
     * &lt;code&gt;optional int64 retcode = 102;&lt;/code&gt;
     * @return The retcode.
     */
    @java.lang.Override
    public long getRetcode() {
<span class="nc" id="L1152">      return retcode_;</span>
    }

    public static final int MSG_FIELD_NUMBER = 103;
<span class="nc" id="L1156">    @SuppressWarnings(&quot;serial&quot;)</span>
    private volatile java.lang.Object msg_ = &quot;&quot;;
    /**
     * &lt;code&gt;optional string msg = 103;&lt;/code&gt;
     * @return Whether the msg field is set.
     */
    @java.lang.Override
    public boolean hasMsg() {
<span class="nc bnc" id="L1164" title="All 2 branches missed.">      return ((bitField0_ &amp; 0x00000002) != 0);</span>
    }
    /**
     * &lt;code&gt;optional string msg = 103;&lt;/code&gt;
     * @return The msg.
     */
    @java.lang.Override
    public java.lang.String getMsg() {
<span class="nc" id="L1172">      java.lang.Object ref = msg_;</span>
<span class="nc bnc" id="L1173" title="All 2 branches missed.">      if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L1174">        return (java.lang.String) ref;</span>
      } else {
<span class="nc" id="L1176">        com.google.protobuf.ByteString bs = </span>
            (com.google.protobuf.ByteString) ref;
<span class="nc" id="L1178">        java.lang.String s = bs.toStringUtf8();</span>
<span class="nc bnc" id="L1179" title="All 2 branches missed.">        if (bs.isValidUtf8()) {</span>
<span class="nc" id="L1180">          msg_ = s;</span>
        }
<span class="nc" id="L1182">        return s;</span>
      }
    }
    /**
     * &lt;code&gt;optional string msg = 103;&lt;/code&gt;
     * @return The bytes for msg.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getMsgBytes() {
<span class="nc" id="L1192">      java.lang.Object ref = msg_;</span>
<span class="nc bnc" id="L1193" title="All 2 branches missed.">      if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L1194">        com.google.protobuf.ByteString b = </span>
<span class="nc" id="L1195">            com.google.protobuf.ByteString.copyFromUtf8(</span>
                (java.lang.String) ref);
<span class="nc" id="L1197">        msg_ = b;</span>
<span class="nc" id="L1198">        return b;</span>
      } else {
<span class="nc" id="L1200">        return (com.google.protobuf.ByteString) ref;</span>
      }
    }

<span class="nc" id="L1204">    private byte memoizedIsInitialized = -1;</span>
    @java.lang.Override
    public final boolean isInitialized() {
<span class="nc" id="L1207">      byte isInitialized = memoizedIsInitialized;</span>
<span class="nc bnc" id="L1208" title="All 2 branches missed.">      if (isInitialized == 1) return true;</span>
<span class="nc bnc" id="L1209" title="All 2 branches missed.">      if (isInitialized == 0) return false;</span>

<span class="nc bnc" id="L1211" title="All 2 branches missed.">      for (int i = 0; i &lt; getValuesCount(); i++) {</span>
<span class="nc bnc" id="L1212" title="All 2 branches missed.">        if (!getValues(i).isInitialized()) {</span>
<span class="nc" id="L1213">          memoizedIsInitialized = 0;</span>
<span class="nc" id="L1214">          return false;</span>
        }
      }
<span class="nc" id="L1217">      memoizedIsInitialized = 1;</span>
<span class="nc" id="L1218">      return true;</span>
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
<span class="nc bnc" id="L1224" title="All 2 branches missed.">      for (int i = 0; i &lt; values_.size(); i++) {</span>
<span class="nc" id="L1225">        output.writeMessage(101, values_.get(i));</span>
      }
<span class="nc bnc" id="L1227" title="All 2 branches missed.">      if (((bitField0_ &amp; 0x00000001) != 0)) {</span>
<span class="nc" id="L1228">        output.writeInt64(102, retcode_);</span>
      }
<span class="nc bnc" id="L1230" title="All 2 branches missed.">      if (((bitField0_ &amp; 0x00000002) != 0)) {</span>
<span class="nc" id="L1231">        com.google.protobuf.GeneratedMessageV3.writeString(output, 103, msg_);</span>
      }
<span class="nc" id="L1233">      getUnknownFields().writeTo(output);</span>
<span class="nc" id="L1234">    }</span>

    @java.lang.Override
    public int getSerializedSize() {
<span class="nc" id="L1238">      int size = memoizedSize;</span>
<span class="nc bnc" id="L1239" title="All 2 branches missed.">      if (size != -1) return size;</span>

<span class="nc" id="L1241">      size = 0;</span>
<span class="nc bnc" id="L1242" title="All 2 branches missed.">      for (int i = 0; i &lt; values_.size(); i++) {</span>
<span class="nc" id="L1243">        size += com.google.protobuf.CodedOutputStream</span>
<span class="nc" id="L1244">          .computeMessageSize(101, values_.get(i));</span>
      }
<span class="nc bnc" id="L1246" title="All 2 branches missed.">      if (((bitField0_ &amp; 0x00000001) != 0)) {</span>
<span class="nc" id="L1247">        size += com.google.protobuf.CodedOutputStream</span>
<span class="nc" id="L1248">          .computeInt64Size(102, retcode_);</span>
      }
<span class="nc bnc" id="L1250" title="All 2 branches missed.">      if (((bitField0_ &amp; 0x00000002) != 0)) {</span>
<span class="nc" id="L1251">        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(103, msg_);</span>
      }
<span class="nc" id="L1253">      size += getUnknownFields().getSerializedSize();</span>
<span class="nc" id="L1254">      memoizedSize = size;</span>
<span class="nc" id="L1255">      return size;</span>
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
<span class="nc bnc" id="L1260" title="All 2 branches missed.">      if (obj == this) {</span>
<span class="nc" id="L1261">       return true;</span>
      }
<span class="nc bnc" id="L1263" title="All 2 branches missed.">      if (!(obj instanceof com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas)) {</span>
<span class="nc" id="L1264">        return super.equals(obj);</span>
      }
<span class="nc" id="L1266">      com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas other = (com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas) obj;</span>

<span class="nc" id="L1268">      if (!getValuesList()</span>
<span class="nc bnc" id="L1269" title="All 2 branches missed.">          .equals(other.getValuesList())) return false;</span>
<span class="nc bnc" id="L1270" title="All 2 branches missed.">      if (hasRetcode() != other.hasRetcode()) return false;</span>
<span class="nc bnc" id="L1271" title="All 2 branches missed.">      if (hasRetcode()) {</span>
<span class="nc" id="L1272">        if (getRetcode()</span>
<span class="nc bnc" id="L1273" title="All 2 branches missed.">            != other.getRetcode()) return false;</span>
      }
<span class="nc bnc" id="L1275" title="All 2 branches missed.">      if (hasMsg() != other.hasMsg()) return false;</span>
<span class="nc bnc" id="L1276" title="All 2 branches missed.">      if (hasMsg()) {</span>
<span class="nc" id="L1277">        if (!getMsg()</span>
<span class="nc bnc" id="L1278" title="All 2 branches missed.">            .equals(other.getMsg())) return false;</span>
      }
<span class="nc bnc" id="L1280" title="All 2 branches missed.">      if (!getUnknownFields().equals(other.getUnknownFields())) return false;</span>
<span class="nc" id="L1281">      return true;</span>
    }

    @java.lang.Override
    public int hashCode() {
<span class="nc bnc" id="L1286" title="All 2 branches missed.">      if (memoizedHashCode != 0) {</span>
<span class="nc" id="L1287">        return memoizedHashCode;</span>
      }
<span class="nc" id="L1289">      int hash = 41;</span>
<span class="nc" id="L1290">      hash = (19 * hash) + getDescriptor().hashCode();</span>
<span class="nc bnc" id="L1291" title="All 2 branches missed.">      if (getValuesCount() &gt; 0) {</span>
<span class="nc" id="L1292">        hash = (37 * hash) + VALUES_FIELD_NUMBER;</span>
<span class="nc" id="L1293">        hash = (53 * hash) + getValuesList().hashCode();</span>
      }
<span class="nc bnc" id="L1295" title="All 2 branches missed.">      if (hasRetcode()) {</span>
<span class="nc" id="L1296">        hash = (37 * hash) + RETCODE_FIELD_NUMBER;</span>
<span class="nc" id="L1297">        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(</span>
<span class="nc" id="L1298">            getRetcode());</span>
      }
<span class="nc bnc" id="L1300" title="All 2 branches missed.">      if (hasMsg()) {</span>
<span class="nc" id="L1301">        hash = (37 * hash) + MSG_FIELD_NUMBER;</span>
<span class="nc" id="L1302">        hash = (53 * hash) + getMsg().hashCode();</span>
      }
<span class="nc" id="L1304">      hash = (29 * hash) + getUnknownFields().hashCode();</span>
<span class="nc" id="L1305">      memoizedHashCode = hash;</span>
<span class="nc" id="L1306">      return hash;</span>
    }

    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L1312">      return PARSER.parseFrom(data);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L1318">      return PARSER.parseFrom(data, extensionRegistry);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L1323">      return PARSER.parseFrom(data);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L1329">      return PARSER.parseFrom(data, extensionRegistry);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L1333">      return PARSER.parseFrom(data);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L1339">      return PARSER.parseFrom(data, extensionRegistry);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas parseFrom(java.io.InputStream input)
        throws java.io.IOException {
<span class="nc" id="L1343">      return com.google.protobuf.GeneratedMessageV3</span>
<span class="nc" id="L1344">          .parseWithIOException(PARSER, input);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
<span class="nc" id="L1350">      return com.google.protobuf.GeneratedMessageV3</span>
<span class="nc" id="L1351">          .parseWithIOException(PARSER, input, extensionRegistry);</span>
    }

    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
<span class="nc" id="L1356">      return com.google.protobuf.GeneratedMessageV3</span>
<span class="nc" id="L1357">          .parseDelimitedWithIOException(PARSER, input);</span>
    }

    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
<span class="nc" id="L1364">      return com.google.protobuf.GeneratedMessageV3</span>
<span class="nc" id="L1365">          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
<span class="nc" id="L1370">      return com.google.protobuf.GeneratedMessageV3</span>
<span class="nc" id="L1371">          .parseWithIOException(PARSER, input);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
<span class="nc" id="L1377">      return com.google.protobuf.GeneratedMessageV3</span>
<span class="nc" id="L1378">          .parseWithIOException(PARSER, input, extensionRegistry);</span>
    }

    @java.lang.Override
<span class="nc" id="L1382">    public Builder newBuilderForType() { return newBuilder(); }</span>
    public static Builder newBuilder() {
<span class="nc" id="L1384">      return DEFAULT_INSTANCE.toBuilder();</span>
    }
    public static Builder newBuilder(com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas prototype) {
<span class="nc" id="L1387">      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);</span>
    }
    @java.lang.Override
    public Builder toBuilder() {
<span class="nc bnc" id="L1391" title="All 2 branches missed.">      return this == DEFAULT_INSTANCE</span>
<span class="nc" id="L1392">          ? new Builder() : new Builder().mergeFrom(this);</span>
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
<span class="nc" id="L1398">      Builder builder = new Builder(parent);</span>
<span class="nc" id="L1399">      return builder;</span>
    }
    /**
     * Protobuf type {@code protobuf.TimeSeriesDatas}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder&lt;Builder&gt; implements
        // @@protoc_insertion_point(builder_implements:protobuf.TimeSeriesDatas)
        com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatasOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
<span class="nc" id="L1410">        return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TimeSeriesDatas_descriptor;</span>
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
<span class="nc" id="L1416">        return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TimeSeriesDatas_fieldAccessorTable</span>
<span class="nc" id="L1417">            .ensureFieldAccessorsInitialized(</span>
                com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas.class, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas.Builder.class);
      }

      // Construct using com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas.newBuilder()
<span class="nc" id="L1422">      private Builder() {</span>

<span class="nc" id="L1424">      }</span>

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
<span class="nc" id="L1428">        super(parent);</span>

<span class="nc" id="L1430">      }</span>
      @java.lang.Override
      public Builder clear() {
<span class="nc" id="L1433">        super.clear();</span>
<span class="nc" id="L1434">        bitField0_ = 0;</span>
<span class="nc bnc" id="L1435" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc" id="L1436">          values_ = java.util.Collections.emptyList();</span>
        } else {
<span class="nc" id="L1438">          values_ = null;</span>
<span class="nc" id="L1439">          valuesBuilder_.clear();</span>
        }
<span class="nc" id="L1441">        bitField0_ = (bitField0_ &amp; ~0x00000001);</span>
<span class="nc" id="L1442">        retcode_ = 0L;</span>
<span class="nc" id="L1443">        msg_ = &quot;&quot;;</span>
<span class="nc" id="L1444">        return this;</span>
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
<span class="nc" id="L1450">        return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TimeSeriesDatas_descriptor;</span>
      }

      @java.lang.Override
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas getDefaultInstanceForType() {
<span class="nc" id="L1455">        return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas.getDefaultInstance();</span>
      }

      @java.lang.Override
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas build() {
<span class="nc" id="L1460">        com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas result = buildPartial();</span>
<span class="nc bnc" id="L1461" title="All 2 branches missed.">        if (!result.isInitialized()) {</span>
<span class="nc" id="L1462">          throw newUninitializedMessageException(result);</span>
        }
<span class="nc" id="L1464">        return result;</span>
      }

      @java.lang.Override
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas buildPartial() {
<span class="nc" id="L1469">        com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas result = new com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas(this);</span>
<span class="nc" id="L1470">        buildPartialRepeatedFields(result);</span>
<span class="nc bnc" id="L1471" title="All 2 branches missed.">        if (bitField0_ != 0) { buildPartial0(result); }</span>
<span class="nc" id="L1472">        onBuilt();</span>
<span class="nc" id="L1473">        return result;</span>
      }

      private void buildPartialRepeatedFields(com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas result) {
<span class="nc bnc" id="L1477" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc bnc" id="L1478" title="All 2 branches missed.">          if (((bitField0_ &amp; 0x00000001) != 0)) {</span>
<span class="nc" id="L1479">            values_ = java.util.Collections.unmodifiableList(values_);</span>
<span class="nc" id="L1480">            bitField0_ = (bitField0_ &amp; ~0x00000001);</span>
          }
<span class="nc" id="L1482">          result.values_ = values_;</span>
        } else {
<span class="nc" id="L1484">          result.values_ = valuesBuilder_.build();</span>
        }
<span class="nc" id="L1486">      }</span>

      private void buildPartial0(com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas result) {
<span class="nc" id="L1489">        int from_bitField0_ = bitField0_;</span>
<span class="nc" id="L1490">        int to_bitField0_ = 0;</span>
<span class="nc bnc" id="L1491" title="All 2 branches missed.">        if (((from_bitField0_ &amp; 0x00000002) != 0)) {</span>
<span class="nc" id="L1492">          result.retcode_ = retcode_;</span>
<span class="nc" id="L1493">          to_bitField0_ |= 0x00000001;</span>
        }
<span class="nc bnc" id="L1495" title="All 2 branches missed.">        if (((from_bitField0_ &amp; 0x00000004) != 0)) {</span>
<span class="nc" id="L1496">          result.msg_ = msg_;</span>
<span class="nc" id="L1497">          to_bitField0_ |= 0x00000002;</span>
        }
<span class="nc" id="L1499">        result.bitField0_ |= to_bitField0_;</span>
<span class="nc" id="L1500">      }</span>

      @java.lang.Override
      public Builder clone() {
<span class="nc" id="L1504">        return super.clone();</span>
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
<span class="nc" id="L1510">        return super.setField(field, value);</span>
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
<span class="nc" id="L1515">        return super.clearField(field);</span>
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
<span class="nc" id="L1520">        return super.clearOneof(oneof);</span>
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
<span class="nc" id="L1526">        return super.setRepeatedField(field, index, value);</span>
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
<span class="nc" id="L1532">        return super.addRepeatedField(field, value);</span>
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
<span class="nc bnc" id="L1536" title="All 2 branches missed.">        if (other instanceof com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas) {</span>
<span class="nc" id="L1537">          return mergeFrom((com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas)other);</span>
        } else {
<span class="nc" id="L1539">          super.mergeFrom(other);</span>
<span class="nc" id="L1540">          return this;</span>
        }
      }

      public Builder mergeFrom(com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas other) {
<span class="nc bnc" id="L1545" title="All 2 branches missed.">        if (other == com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas.getDefaultInstance()) return this;</span>
<span class="nc bnc" id="L1546" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc bnc" id="L1547" title="All 2 branches missed.">          if (!other.values_.isEmpty()) {</span>
<span class="nc bnc" id="L1548" title="All 2 branches missed.">            if (values_.isEmpty()) {</span>
<span class="nc" id="L1549">              values_ = other.values_;</span>
<span class="nc" id="L1550">              bitField0_ = (bitField0_ &amp; ~0x00000001);</span>
            } else {
<span class="nc" id="L1552">              ensureValuesIsMutable();</span>
<span class="nc" id="L1553">              values_.addAll(other.values_);</span>
            }
<span class="nc" id="L1555">            onChanged();</span>
          }
        } else {
<span class="nc bnc" id="L1558" title="All 2 branches missed.">          if (!other.values_.isEmpty()) {</span>
<span class="nc bnc" id="L1559" title="All 2 branches missed.">            if (valuesBuilder_.isEmpty()) {</span>
<span class="nc" id="L1560">              valuesBuilder_.dispose();</span>
<span class="nc" id="L1561">              valuesBuilder_ = null;</span>
<span class="nc" id="L1562">              values_ = other.values_;</span>
<span class="nc" id="L1563">              bitField0_ = (bitField0_ &amp; ~0x00000001);</span>
<span class="nc" id="L1564">              valuesBuilder_ = </span>
<span class="nc bnc" id="L1565" title="All 2 branches missed.">                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?</span>
<span class="nc" id="L1566">                   getValuesFieldBuilder() : null;</span>
            } else {
<span class="nc" id="L1568">              valuesBuilder_.addAllMessages(other.values_);</span>
            }
          }
        }
<span class="nc bnc" id="L1572" title="All 2 branches missed.">        if (other.hasRetcode()) {</span>
<span class="nc" id="L1573">          setRetcode(other.getRetcode());</span>
        }
<span class="nc bnc" id="L1575" title="All 2 branches missed.">        if (other.hasMsg()) {</span>
<span class="nc" id="L1576">          msg_ = other.msg_;</span>
<span class="nc" id="L1577">          bitField0_ |= 0x00000004;</span>
<span class="nc" id="L1578">          onChanged();</span>
        }
<span class="nc" id="L1580">        this.mergeUnknownFields(other.getUnknownFields());</span>
<span class="nc" id="L1581">        onChanged();</span>
<span class="nc" id="L1582">        return this;</span>
      }

      @java.lang.Override
      public final boolean isInitialized() {
<span class="nc bnc" id="L1587" title="All 2 branches missed.">        for (int i = 0; i &lt; getValuesCount(); i++) {</span>
<span class="nc bnc" id="L1588" title="All 2 branches missed.">          if (!getValues(i).isInitialized()) {</span>
<span class="nc" id="L1589">            return false;</span>
          }
        }
<span class="nc" id="L1592">        return true;</span>
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
<span class="nc bnc" id="L1600" title="All 2 branches missed.">        if (extensionRegistry == null) {</span>
<span class="nc" id="L1601">          throw new java.lang.NullPointerException();</span>
        }
        try {
<span class="nc" id="L1604">          boolean done = false;</span>
<span class="nc bnc" id="L1605" title="All 2 branches missed.">          while (!done) {</span>
<span class="nc" id="L1606">            int tag = input.readTag();</span>
<span class="nc bnc" id="L1607" title="All 5 branches missed.">            switch (tag) {</span>
              case 0:
<span class="nc" id="L1609">                done = true;</span>
<span class="nc" id="L1610">                break;</span>
              case 810: {
<span class="nc" id="L1612">                com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData m =</span>
<span class="nc" id="L1613">                    input.readMessage(</span>
                        com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.PARSER,
                        extensionRegistry);
<span class="nc bnc" id="L1616" title="All 2 branches missed.">                if (valuesBuilder_ == null) {</span>
<span class="nc" id="L1617">                  ensureValuesIsMutable();</span>
<span class="nc" id="L1618">                  values_.add(m);</span>
                } else {
<span class="nc" id="L1620">                  valuesBuilder_.addMessage(m);</span>
                }
<span class="nc" id="L1622">                break;</span>
              } // case 810
              case 816: {
<span class="nc" id="L1625">                retcode_ = input.readInt64();</span>
<span class="nc" id="L1626">                bitField0_ |= 0x00000002;</span>
<span class="nc" id="L1627">                break;</span>
              } // case 816
              case 826: {
<span class="nc" id="L1630">                msg_ = input.readBytes();</span>
<span class="nc" id="L1631">                bitField0_ |= 0x00000004;</span>
<span class="nc" id="L1632">                break;</span>
              } // case 826
              default: {
<span class="nc bnc" id="L1635" title="All 2 branches missed.">                if (!super.parseUnknownField(input, extensionRegistry, tag)) {</span>
<span class="nc" id="L1636">                  done = true; // was an endgroup tag</span>
                }
                break;
              } // default:
            } // switch (tag)
<span class="nc" id="L1641">          } // while (!done)</span>
<span class="nc" id="L1642">        } catch (com.google.protobuf.InvalidProtocolBufferException e) {</span>
<span class="nc" id="L1643">          throw e.unwrapIOException();</span>
        } finally {
<span class="nc" id="L1645">          onChanged();</span>
        } // finally
<span class="nc" id="L1647">        return this;</span>
      }
      private int bitField0_;

<span class="nc" id="L1651">      private java.util.List&lt;com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData&gt; values_ =</span>
<span class="nc" id="L1652">        java.util.Collections.emptyList();</span>
      private void ensureValuesIsMutable() {
<span class="nc bnc" id="L1654" title="All 2 branches missed.">        if (!((bitField0_ &amp; 0x00000001) != 0)) {</span>
<span class="nc" id="L1655">          values_ = new java.util.ArrayList&lt;com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData&gt;(values_);</span>
<span class="nc" id="L1656">          bitField0_ |= 0x00000001;</span>
         }
<span class="nc" id="L1658">      }</span>

      private com.google.protobuf.RepeatedFieldBuilderV3&lt;
          com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.Builder, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDataOrBuilder&gt; valuesBuilder_;

      /**
       * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
       */
      public java.util.List&lt;com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData&gt; getValuesList() {
<span class="nc bnc" id="L1667" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc" id="L1668">          return java.util.Collections.unmodifiableList(values_);</span>
        } else {
<span class="nc" id="L1670">          return valuesBuilder_.getMessageList();</span>
        }
      }
      /**
       * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
       */
      public int getValuesCount() {
<span class="nc bnc" id="L1677" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc" id="L1678">          return values_.size();</span>
        } else {
<span class="nc" id="L1680">          return valuesBuilder_.getCount();</span>
        }
      }
      /**
       * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
       */
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData getValues(int index) {
<span class="nc bnc" id="L1687" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc" id="L1688">          return values_.get(index);</span>
        } else {
<span class="nc" id="L1690">          return valuesBuilder_.getMessage(index);</span>
        }
      }
      /**
       * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
       */
      public Builder setValues(
          int index, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData value) {
<span class="nc bnc" id="L1698" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc bnc" id="L1699" title="All 2 branches missed.">          if (value == null) {</span>
<span class="nc" id="L1700">            throw new NullPointerException();</span>
          }
<span class="nc" id="L1702">          ensureValuesIsMutable();</span>
<span class="nc" id="L1703">          values_.set(index, value);</span>
<span class="nc" id="L1704">          onChanged();</span>
        } else {
<span class="nc" id="L1706">          valuesBuilder_.setMessage(index, value);</span>
        }
<span class="nc" id="L1708">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
       */
      public Builder setValues(
          int index, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.Builder builderForValue) {
<span class="nc bnc" id="L1715" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc" id="L1716">          ensureValuesIsMutable();</span>
<span class="nc" id="L1717">          values_.set(index, builderForValue.build());</span>
<span class="nc" id="L1718">          onChanged();</span>
        } else {
<span class="nc" id="L1720">          valuesBuilder_.setMessage(index, builderForValue.build());</span>
        }
<span class="nc" id="L1722">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
       */
      public Builder addValues(com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData value) {
<span class="nc bnc" id="L1728" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc bnc" id="L1729" title="All 2 branches missed.">          if (value == null) {</span>
<span class="nc" id="L1730">            throw new NullPointerException();</span>
          }
<span class="nc" id="L1732">          ensureValuesIsMutable();</span>
<span class="nc" id="L1733">          values_.add(value);</span>
<span class="nc" id="L1734">          onChanged();</span>
        } else {
<span class="nc" id="L1736">          valuesBuilder_.addMessage(value);</span>
        }
<span class="nc" id="L1738">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
       */
      public Builder addValues(
          int index, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData value) {
<span class="nc bnc" id="L1745" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc bnc" id="L1746" title="All 2 branches missed.">          if (value == null) {</span>
<span class="nc" id="L1747">            throw new NullPointerException();</span>
          }
<span class="nc" id="L1749">          ensureValuesIsMutable();</span>
<span class="nc" id="L1750">          values_.add(index, value);</span>
<span class="nc" id="L1751">          onChanged();</span>
        } else {
<span class="nc" id="L1753">          valuesBuilder_.addMessage(index, value);</span>
        }
<span class="nc" id="L1755">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
       */
      public Builder addValues(
          com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.Builder builderForValue) {
<span class="nc bnc" id="L1762" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc" id="L1763">          ensureValuesIsMutable();</span>
<span class="nc" id="L1764">          values_.add(builderForValue.build());</span>
<span class="nc" id="L1765">          onChanged();</span>
        } else {
<span class="nc" id="L1767">          valuesBuilder_.addMessage(builderForValue.build());</span>
        }
<span class="nc" id="L1769">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
       */
      public Builder addValues(
          int index, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.Builder builderForValue) {
<span class="nc bnc" id="L1776" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc" id="L1777">          ensureValuesIsMutable();</span>
<span class="nc" id="L1778">          values_.add(index, builderForValue.build());</span>
<span class="nc" id="L1779">          onChanged();</span>
        } else {
<span class="nc" id="L1781">          valuesBuilder_.addMessage(index, builderForValue.build());</span>
        }
<span class="nc" id="L1783">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
       */
      public Builder addAllValues(
          java.lang.Iterable&lt;? extends com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData&gt; values) {
<span class="nc bnc" id="L1790" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc" id="L1791">          ensureValuesIsMutable();</span>
<span class="nc" id="L1792">          com.google.protobuf.AbstractMessageLite.Builder.addAll(</span>
              values, values_);
<span class="nc" id="L1794">          onChanged();</span>
        } else {
<span class="nc" id="L1796">          valuesBuilder_.addAllMessages(values);</span>
        }
<span class="nc" id="L1798">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
       */
      public Builder clearValues() {
<span class="nc bnc" id="L1804" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc" id="L1805">          values_ = java.util.Collections.emptyList();</span>
<span class="nc" id="L1806">          bitField0_ = (bitField0_ &amp; ~0x00000001);</span>
<span class="nc" id="L1807">          onChanged();</span>
        } else {
<span class="nc" id="L1809">          valuesBuilder_.clear();</span>
        }
<span class="nc" id="L1811">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
       */
      public Builder removeValues(int index) {
<span class="nc bnc" id="L1817" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc" id="L1818">          ensureValuesIsMutable();</span>
<span class="nc" id="L1819">          values_.remove(index);</span>
<span class="nc" id="L1820">          onChanged();</span>
        } else {
<span class="nc" id="L1822">          valuesBuilder_.remove(index);</span>
        }
<span class="nc" id="L1824">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
       */
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.Builder getValuesBuilder(
          int index) {
<span class="nc" id="L1831">        return getValuesFieldBuilder().getBuilder(index);</span>
      }
      /**
       * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
       */
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDataOrBuilder getValuesOrBuilder(
          int index) {
<span class="nc bnc" id="L1838" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc" id="L1839">          return values_.get(index);  } else {</span>
<span class="nc" id="L1840">          return valuesBuilder_.getMessageOrBuilder(index);</span>
        }
      }
      /**
       * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
       */
      public java.util.List&lt;? extends com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDataOrBuilder&gt; 
           getValuesOrBuilderList() {
<span class="nc bnc" id="L1848" title="All 2 branches missed.">        if (valuesBuilder_ != null) {</span>
<span class="nc" id="L1849">          return valuesBuilder_.getMessageOrBuilderList();</span>
        } else {
<span class="nc" id="L1851">          return java.util.Collections.unmodifiableList(values_);</span>
        }
      }
      /**
       * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
       */
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.Builder addValuesBuilder() {
<span class="nc" id="L1858">        return getValuesFieldBuilder().addBuilder(</span>
<span class="nc" id="L1859">            com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.getDefaultInstance());</span>
      }
      /**
       * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
       */
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.Builder addValuesBuilder(
          int index) {
<span class="nc" id="L1866">        return getValuesFieldBuilder().addBuilder(</span>
<span class="nc" id="L1867">            index, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.getDefaultInstance());</span>
      }
      /**
       * &lt;code&gt;repeated .protobuf.TimeSeriesData Values = 101;&lt;/code&gt;
       */
      public java.util.List&lt;com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.Builder&gt; 
           getValuesBuilderList() {
<span class="nc" id="L1874">        return getValuesFieldBuilder().getBuilderList();</span>
      }
      private com.google.protobuf.RepeatedFieldBuilderV3&lt;
          com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.Builder, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDataOrBuilder&gt; 
          getValuesFieldBuilder() {
<span class="nc bnc" id="L1879" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc bnc" id="L1880" title="All 2 branches missed.">          valuesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3&lt;</span>
              com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.Builder, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDataOrBuilder&gt;(
                  values_,
                  ((bitField0_ &amp; 0x00000001) != 0),
<span class="nc" id="L1884">                  getParentForChildren(),</span>
<span class="nc" id="L1885">                  isClean());</span>
<span class="nc" id="L1886">          values_ = null;</span>
        }
<span class="nc" id="L1888">        return valuesBuilder_;</span>
      }

      private long retcode_ ;
      /**
       * &lt;code&gt;optional int64 retcode = 102;&lt;/code&gt;
       * @return Whether the retcode field is set.
       */
      @java.lang.Override
      public boolean hasRetcode() {
<span class="nc bnc" id="L1898" title="All 2 branches missed.">        return ((bitField0_ &amp; 0x00000002) != 0);</span>
      }
      /**
       * &lt;code&gt;optional int64 retcode = 102;&lt;/code&gt;
       * @return The retcode.
       */
      @java.lang.Override
      public long getRetcode() {
<span class="nc" id="L1906">        return retcode_;</span>
      }
      /**
       * &lt;code&gt;optional int64 retcode = 102;&lt;/code&gt;
       * @param value The retcode to set.
       * @return This builder for chaining.
       */
      public Builder setRetcode(long value) {

<span class="nc" id="L1915">        retcode_ = value;</span>
<span class="nc" id="L1916">        bitField0_ |= 0x00000002;</span>
<span class="nc" id="L1917">        onChanged();</span>
<span class="nc" id="L1918">        return this;</span>
      }
      /**
       * &lt;code&gt;optional int64 retcode = 102;&lt;/code&gt;
       * @return This builder for chaining.
       */
      public Builder clearRetcode() {
<span class="nc" id="L1925">        bitField0_ = (bitField0_ &amp; ~0x00000002);</span>
<span class="nc" id="L1926">        retcode_ = 0L;</span>
<span class="nc" id="L1927">        onChanged();</span>
<span class="nc" id="L1928">        return this;</span>
      }

<span class="nc" id="L1931">      private java.lang.Object msg_ = &quot;&quot;;</span>
      /**
       * &lt;code&gt;optional string msg = 103;&lt;/code&gt;
       * @return Whether the msg field is set.
       */
      public boolean hasMsg() {
<span class="nc bnc" id="L1937" title="All 2 branches missed.">        return ((bitField0_ &amp; 0x00000004) != 0);</span>
      }
      /**
       * &lt;code&gt;optional string msg = 103;&lt;/code&gt;
       * @return The msg.
       */
      public java.lang.String getMsg() {
<span class="nc" id="L1944">        java.lang.Object ref = msg_;</span>
<span class="nc bnc" id="L1945" title="All 2 branches missed.">        if (!(ref instanceof java.lang.String)) {</span>
<span class="nc" id="L1946">          com.google.protobuf.ByteString bs =</span>
              (com.google.protobuf.ByteString) ref;
<span class="nc" id="L1948">          java.lang.String s = bs.toStringUtf8();</span>
<span class="nc bnc" id="L1949" title="All 2 branches missed.">          if (bs.isValidUtf8()) {</span>
<span class="nc" id="L1950">            msg_ = s;</span>
          }
<span class="nc" id="L1952">          return s;</span>
        } else {
<span class="nc" id="L1954">          return (java.lang.String) ref;</span>
        }
      }
      /**
       * &lt;code&gt;optional string msg = 103;&lt;/code&gt;
       * @return The bytes for msg.
       */
      public com.google.protobuf.ByteString
          getMsgBytes() {
<span class="nc" id="L1963">        java.lang.Object ref = msg_;</span>
<span class="nc bnc" id="L1964" title="All 2 branches missed.">        if (ref instanceof String) {</span>
<span class="nc" id="L1965">          com.google.protobuf.ByteString b = </span>
<span class="nc" id="L1966">              com.google.protobuf.ByteString.copyFromUtf8(</span>
                  (java.lang.String) ref);
<span class="nc" id="L1968">          msg_ = b;</span>
<span class="nc" id="L1969">          return b;</span>
        } else {
<span class="nc" id="L1971">          return (com.google.protobuf.ByteString) ref;</span>
        }
      }
      /**
       * &lt;code&gt;optional string msg = 103;&lt;/code&gt;
       * @param value The msg to set.
       * @return This builder for chaining.
       */
      public Builder setMsg(
          java.lang.String value) {
<span class="nc bnc" id="L1981" title="All 2 branches missed.">        if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L1982">        msg_ = value;</span>
<span class="nc" id="L1983">        bitField0_ |= 0x00000004;</span>
<span class="nc" id="L1984">        onChanged();</span>
<span class="nc" id="L1985">        return this;</span>
      }
      /**
       * &lt;code&gt;optional string msg = 103;&lt;/code&gt;
       * @return This builder for chaining.
       */
      public Builder clearMsg() {
<span class="nc" id="L1992">        msg_ = getDefaultInstance().getMsg();</span>
<span class="nc" id="L1993">        bitField0_ = (bitField0_ &amp; ~0x00000004);</span>
<span class="nc" id="L1994">        onChanged();</span>
<span class="nc" id="L1995">        return this;</span>
      }
      /**
       * &lt;code&gt;optional string msg = 103;&lt;/code&gt;
       * @param value The bytes for msg to set.
       * @return This builder for chaining.
       */
      public Builder setMsgBytes(
          com.google.protobuf.ByteString value) {
<span class="nc bnc" id="L2004" title="All 2 branches missed.">        if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L2005">        msg_ = value;</span>
<span class="nc" id="L2006">        bitField0_ |= 0x00000004;</span>
<span class="nc" id="L2007">        onChanged();</span>
<span class="nc" id="L2008">        return this;</span>
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
<span class="nc" id="L2013">        return super.setUnknownFields(unknownFields);</span>
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
<span class="nc" id="L2019">        return super.mergeUnknownFields(unknownFields);</span>
      }


      // @@protoc_insertion_point(builder_scope:protobuf.TimeSeriesDatas)
    }

    // @@protoc_insertion_point(class_scope:protobuf.TimeSeriesDatas)
    private static final com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas DEFAULT_INSTANCE;
    static {
<span class="nc" id="L2029">      DEFAULT_INSTANCE = new com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas();</span>
    }

    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas getDefaultInstance() {
<span class="nc" id="L2033">      return DEFAULT_INSTANCE;</span>
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser&lt;TimeSeriesDatas&gt;
<span class="nc" id="L2037">        PARSER = new com.google.protobuf.AbstractParser&lt;TimeSeriesDatas&gt;() {</span>
      @java.lang.Override
      public TimeSeriesDatas parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L2043">        Builder builder = newBuilder();</span>
        try {
<span class="nc" id="L2045">          builder.mergeFrom(input, extensionRegistry);</span>
<span class="nc" id="L2046">        } catch (com.google.protobuf.InvalidProtocolBufferException e) {</span>
<span class="nc" id="L2047">          throw e.setUnfinishedMessage(builder.buildPartial());</span>
<span class="nc" id="L2048">        } catch (com.google.protobuf.UninitializedMessageException e) {</span>
<span class="nc" id="L2049">          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());</span>
<span class="nc" id="L2050">        } catch (java.io.IOException e) {</span>
<span class="nc" id="L2051">          throw new com.google.protobuf.InvalidProtocolBufferException(e)</span>
<span class="nc" id="L2052">              .setUnfinishedMessage(builder.buildPartial());</span>
<span class="nc" id="L2053">        }</span>
<span class="nc" id="L2054">        return builder.buildPartial();</span>
      }
    };

    public static com.google.protobuf.Parser&lt;TimeSeriesDatas&gt; parser() {
<span class="nc" id="L2059">      return PARSER;</span>
    }

    @java.lang.Override
    public com.google.protobuf.Parser&lt;TimeSeriesDatas&gt; getParserForType() {
<span class="nc" id="L2064">      return PARSER;</span>
    }

    @java.lang.Override
    public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas getDefaultInstanceForType() {
<span class="nc" id="L2069">      return DEFAULT_INSTANCE;</span>
    }

  }

  public interface TimeSeriesDataOrBuilder extends
      // @@protoc_insertion_point(interface_extends:protobuf.TimeSeriesData)
      com.google.protobuf.MessageOrBuilder {

    /**
     * &lt;code&gt;required string SecId = 1;&lt;/code&gt;
     * @return Whether the secId field is set.
     */
    boolean hasSecId();
    /**
     * &lt;code&gt;required string SecId = 1;&lt;/code&gt;
     * @return The secId.
     */
    java.lang.String getSecId();
    /**
     * &lt;code&gt;required string SecId = 1;&lt;/code&gt;
     * @return The bytes for secId.
     */
    com.google.protobuf.ByteString
        getSecIdBytes();

    /**
     * &lt;code&gt;optional string Universe = 2;&lt;/code&gt;
     * @return Whether the universe field is set.
     */
    boolean hasUniverse();
    /**
     * &lt;code&gt;optional string Universe = 2;&lt;/code&gt;
     * @return The universe.
     */
    java.lang.String getUniverse();
    /**
     * &lt;code&gt;optional string Universe = 2;&lt;/code&gt;
     * @return The bytes for universe.
     */
    com.google.protobuf.ByteString
        getUniverseBytes();

    /**
     * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
     */
    java.util.List&lt;com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair&gt; 
        getValuesList();
    /**
     * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
     */
    com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair getValues(int index);
    /**
     * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
     */
    int getValuesCount();
    /**
     * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
     */
    java.util.List&lt;? extends com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePairOrBuilder&gt; 
        getValuesOrBuilderList();
    /**
     * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
     */
    com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePairOrBuilder getValuesOrBuilder(
        int index);

    /**
     * &lt;code&gt;optional string DataId = 4;&lt;/code&gt;
     * @return Whether the dataId field is set.
     */
    boolean hasDataId();
    /**
     * &lt;code&gt;optional string DataId = 4;&lt;/code&gt;
     * @return The dataId.
     */
    java.lang.String getDataId();
    /**
     * &lt;code&gt;optional string DataId = 4;&lt;/code&gt;
     * @return The bytes for dataId.
     */
    com.google.protobuf.ByteString
        getDataIdBytes();

    /**
     * &lt;code&gt;optional string ErrorCode = 5;&lt;/code&gt;
     * @return Whether the errorCode field is set.
     */
    boolean hasErrorCode();
    /**
     * &lt;code&gt;optional string ErrorCode = 5;&lt;/code&gt;
     * @return The errorCode.
     */
    java.lang.String getErrorCode();
    /**
     * &lt;code&gt;optional string ErrorCode = 5;&lt;/code&gt;
     * @return The bytes for errorCode.
     */
    com.google.protobuf.ByteString
        getErrorCodeBytes();
  }
  /**
   * Protobuf type {@code protobuf.TimeSeriesData}
   */
  public static final class TimeSeriesData extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:protobuf.TimeSeriesData)
      TimeSeriesDataOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use TimeSeriesData.newBuilder() to construct.
    private TimeSeriesData(com.google.protobuf.GeneratedMessageV3.Builder&lt;?&gt; builder) {
<span class="nc" id="L2180">      super(builder);</span>
<span class="nc" id="L2181">    }</span>
<span class="nc" id="L2182">    private TimeSeriesData() {</span>
<span class="nc" id="L2183">      secId_ = &quot;&quot;;</span>
<span class="nc" id="L2184">      universe_ = &quot;&quot;;</span>
<span class="nc" id="L2185">      values_ = java.util.Collections.emptyList();</span>
<span class="nc" id="L2186">      dataId_ = &quot;&quot;;</span>
<span class="nc" id="L2187">      errorCode_ = &quot;&quot;;</span>
<span class="nc" id="L2188">    }</span>

    @java.lang.Override
    @SuppressWarnings({&quot;unused&quot;})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
<span class="nc" id="L2194">      return new TimeSeriesData();</span>
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
<span class="nc" id="L2199">      return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TimeSeriesData_descriptor;</span>
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
<span class="nc" id="L2205">      return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TimeSeriesData_fieldAccessorTable</span>
<span class="nc" id="L2206">          .ensureFieldAccessorsInitialized(</span>
              com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.class, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.Builder.class);
    }

    private int bitField0_;
    public static final int SECID_FIELD_NUMBER = 1;
<span class="nc" id="L2212">    @SuppressWarnings(&quot;serial&quot;)</span>
    private volatile java.lang.Object secId_ = &quot;&quot;;
    /**
     * &lt;code&gt;required string SecId = 1;&lt;/code&gt;
     * @return Whether the secId field is set.
     */
    @java.lang.Override
    public boolean hasSecId() {
<span class="nc bnc" id="L2220" title="All 2 branches missed.">      return ((bitField0_ &amp; 0x00000001) != 0);</span>
    }
    /**
     * &lt;code&gt;required string SecId = 1;&lt;/code&gt;
     * @return The secId.
     */
    @java.lang.Override
    public java.lang.String getSecId() {
<span class="nc" id="L2228">      java.lang.Object ref = secId_;</span>
<span class="nc bnc" id="L2229" title="All 2 branches missed.">      if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L2230">        return (java.lang.String) ref;</span>
      } else {
<span class="nc" id="L2232">        com.google.protobuf.ByteString bs = </span>
            (com.google.protobuf.ByteString) ref;
<span class="nc" id="L2234">        java.lang.String s = bs.toStringUtf8();</span>
<span class="nc bnc" id="L2235" title="All 2 branches missed.">        if (bs.isValidUtf8()) {</span>
<span class="nc" id="L2236">          secId_ = s;</span>
        }
<span class="nc" id="L2238">        return s;</span>
      }
    }
    /**
     * &lt;code&gt;required string SecId = 1;&lt;/code&gt;
     * @return The bytes for secId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getSecIdBytes() {
<span class="nc" id="L2248">      java.lang.Object ref = secId_;</span>
<span class="nc bnc" id="L2249" title="All 2 branches missed.">      if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L2250">        com.google.protobuf.ByteString b = </span>
<span class="nc" id="L2251">            com.google.protobuf.ByteString.copyFromUtf8(</span>
                (java.lang.String) ref);
<span class="nc" id="L2253">        secId_ = b;</span>
<span class="nc" id="L2254">        return b;</span>
      } else {
<span class="nc" id="L2256">        return (com.google.protobuf.ByteString) ref;</span>
      }
    }

    public static final int UNIVERSE_FIELD_NUMBER = 2;
<span class="nc" id="L2261">    @SuppressWarnings(&quot;serial&quot;)</span>
    private volatile java.lang.Object universe_ = &quot;&quot;;
    /**
     * &lt;code&gt;optional string Universe = 2;&lt;/code&gt;
     * @return Whether the universe field is set.
     */
    @java.lang.Override
    public boolean hasUniverse() {
<span class="nc bnc" id="L2269" title="All 2 branches missed.">      return ((bitField0_ &amp; 0x00000002) != 0);</span>
    }
    /**
     * &lt;code&gt;optional string Universe = 2;&lt;/code&gt;
     * @return The universe.
     */
    @java.lang.Override
    public java.lang.String getUniverse() {
<span class="nc" id="L2277">      java.lang.Object ref = universe_;</span>
<span class="nc bnc" id="L2278" title="All 2 branches missed.">      if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L2279">        return (java.lang.String) ref;</span>
      } else {
<span class="nc" id="L2281">        com.google.protobuf.ByteString bs = </span>
            (com.google.protobuf.ByteString) ref;
<span class="nc" id="L2283">        java.lang.String s = bs.toStringUtf8();</span>
<span class="nc bnc" id="L2284" title="All 2 branches missed.">        if (bs.isValidUtf8()) {</span>
<span class="nc" id="L2285">          universe_ = s;</span>
        }
<span class="nc" id="L2287">        return s;</span>
      }
    }
    /**
     * &lt;code&gt;optional string Universe = 2;&lt;/code&gt;
     * @return The bytes for universe.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getUniverseBytes() {
<span class="nc" id="L2297">      java.lang.Object ref = universe_;</span>
<span class="nc bnc" id="L2298" title="All 2 branches missed.">      if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L2299">        com.google.protobuf.ByteString b = </span>
<span class="nc" id="L2300">            com.google.protobuf.ByteString.copyFromUtf8(</span>
                (java.lang.String) ref);
<span class="nc" id="L2302">        universe_ = b;</span>
<span class="nc" id="L2303">        return b;</span>
      } else {
<span class="nc" id="L2305">        return (com.google.protobuf.ByteString) ref;</span>
      }
    }

    public static final int VALUES_FIELD_NUMBER = 3;
    @SuppressWarnings(&quot;serial&quot;)
    private java.util.List&lt;com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair&gt; values_;
    /**
     * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
     */
    @java.lang.Override
    public java.util.List&lt;com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair&gt; getValuesList() {
<span class="nc" id="L2317">      return values_;</span>
    }
    /**
     * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
     */
    @java.lang.Override
    public java.util.List&lt;? extends com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePairOrBuilder&gt; 
        getValuesOrBuilderList() {
<span class="nc" id="L2325">      return values_;</span>
    }
    /**
     * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
     */
    @java.lang.Override
    public int getValuesCount() {
<span class="nc" id="L2332">      return values_.size();</span>
    }
    /**
     * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
     */
    @java.lang.Override
    public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair getValues(int index) {
<span class="nc" id="L2339">      return values_.get(index);</span>
    }
    /**
     * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
     */
    @java.lang.Override
    public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePairOrBuilder getValuesOrBuilder(
        int index) {
<span class="nc" id="L2347">      return values_.get(index);</span>
    }

    public static final int DATAID_FIELD_NUMBER = 4;
<span class="nc" id="L2351">    @SuppressWarnings(&quot;serial&quot;)</span>
    private volatile java.lang.Object dataId_ = &quot;&quot;;
    /**
     * &lt;code&gt;optional string DataId = 4;&lt;/code&gt;
     * @return Whether the dataId field is set.
     */
    @java.lang.Override
    public boolean hasDataId() {
<span class="nc bnc" id="L2359" title="All 2 branches missed.">      return ((bitField0_ &amp; 0x00000004) != 0);</span>
    }
    /**
     * &lt;code&gt;optional string DataId = 4;&lt;/code&gt;
     * @return The dataId.
     */
    @java.lang.Override
    public java.lang.String getDataId() {
<span class="nc" id="L2367">      java.lang.Object ref = dataId_;</span>
<span class="nc bnc" id="L2368" title="All 2 branches missed.">      if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L2369">        return (java.lang.String) ref;</span>
      } else {
<span class="nc" id="L2371">        com.google.protobuf.ByteString bs = </span>
            (com.google.protobuf.ByteString) ref;
<span class="nc" id="L2373">        java.lang.String s = bs.toStringUtf8();</span>
<span class="nc bnc" id="L2374" title="All 2 branches missed.">        if (bs.isValidUtf8()) {</span>
<span class="nc" id="L2375">          dataId_ = s;</span>
        }
<span class="nc" id="L2377">        return s;</span>
      }
    }
    /**
     * &lt;code&gt;optional string DataId = 4;&lt;/code&gt;
     * @return The bytes for dataId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDataIdBytes() {
<span class="nc" id="L2387">      java.lang.Object ref = dataId_;</span>
<span class="nc bnc" id="L2388" title="All 2 branches missed.">      if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L2389">        com.google.protobuf.ByteString b = </span>
<span class="nc" id="L2390">            com.google.protobuf.ByteString.copyFromUtf8(</span>
                (java.lang.String) ref);
<span class="nc" id="L2392">        dataId_ = b;</span>
<span class="nc" id="L2393">        return b;</span>
      } else {
<span class="nc" id="L2395">        return (com.google.protobuf.ByteString) ref;</span>
      }
    }

    public static final int ERRORCODE_FIELD_NUMBER = 5;
<span class="nc" id="L2400">    @SuppressWarnings(&quot;serial&quot;)</span>
    private volatile java.lang.Object errorCode_ = &quot;&quot;;
    /**
     * &lt;code&gt;optional string ErrorCode = 5;&lt;/code&gt;
     * @return Whether the errorCode field is set.
     */
    @java.lang.Override
    public boolean hasErrorCode() {
<span class="nc bnc" id="L2408" title="All 2 branches missed.">      return ((bitField0_ &amp; 0x00000008) != 0);</span>
    }
    /**
     * &lt;code&gt;optional string ErrorCode = 5;&lt;/code&gt;
     * @return The errorCode.
     */
    @java.lang.Override
    public java.lang.String getErrorCode() {
<span class="nc" id="L2416">      java.lang.Object ref = errorCode_;</span>
<span class="nc bnc" id="L2417" title="All 2 branches missed.">      if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L2418">        return (java.lang.String) ref;</span>
      } else {
<span class="nc" id="L2420">        com.google.protobuf.ByteString bs = </span>
            (com.google.protobuf.ByteString) ref;
<span class="nc" id="L2422">        java.lang.String s = bs.toStringUtf8();</span>
<span class="nc bnc" id="L2423" title="All 2 branches missed.">        if (bs.isValidUtf8()) {</span>
<span class="nc" id="L2424">          errorCode_ = s;</span>
        }
<span class="nc" id="L2426">        return s;</span>
      }
    }
    /**
     * &lt;code&gt;optional string ErrorCode = 5;&lt;/code&gt;
     * @return The bytes for errorCode.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getErrorCodeBytes() {
<span class="nc" id="L2436">      java.lang.Object ref = errorCode_;</span>
<span class="nc bnc" id="L2437" title="All 2 branches missed.">      if (ref instanceof java.lang.String) {</span>
<span class="nc" id="L2438">        com.google.protobuf.ByteString b = </span>
<span class="nc" id="L2439">            com.google.protobuf.ByteString.copyFromUtf8(</span>
                (java.lang.String) ref);
<span class="nc" id="L2441">        errorCode_ = b;</span>
<span class="nc" id="L2442">        return b;</span>
      } else {
<span class="nc" id="L2444">        return (com.google.protobuf.ByteString) ref;</span>
      }
    }

<span class="nc" id="L2448">    private byte memoizedIsInitialized = -1;</span>
    @java.lang.Override
    public final boolean isInitialized() {
<span class="nc" id="L2451">      byte isInitialized = memoizedIsInitialized;</span>
<span class="nc bnc" id="L2452" title="All 2 branches missed.">      if (isInitialized == 1) return true;</span>
<span class="nc bnc" id="L2453" title="All 2 branches missed.">      if (isInitialized == 0) return false;</span>

<span class="nc bnc" id="L2455" title="All 2 branches missed.">      if (!hasSecId()) {</span>
<span class="nc" id="L2456">        memoizedIsInitialized = 0;</span>
<span class="nc" id="L2457">        return false;</span>
      }
<span class="nc" id="L2459">      memoizedIsInitialized = 1;</span>
<span class="nc" id="L2460">      return true;</span>
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
<span class="nc bnc" id="L2466" title="All 2 branches missed.">      if (((bitField0_ &amp; 0x00000001) != 0)) {</span>
<span class="nc" id="L2467">        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, secId_);</span>
      }
<span class="nc bnc" id="L2469" title="All 2 branches missed.">      if (((bitField0_ &amp; 0x00000002) != 0)) {</span>
<span class="nc" id="L2470">        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, universe_);</span>
      }
<span class="nc bnc" id="L2472" title="All 2 branches missed.">      for (int i = 0; i &lt; values_.size(); i++) {</span>
<span class="nc" id="L2473">        output.writeMessage(3, values_.get(i));</span>
      }
<span class="nc bnc" id="L2475" title="All 2 branches missed.">      if (((bitField0_ &amp; 0x00000004) != 0)) {</span>
<span class="nc" id="L2476">        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, dataId_);</span>
      }
<span class="nc bnc" id="L2478" title="All 2 branches missed.">      if (((bitField0_ &amp; 0x00000008) != 0)) {</span>
<span class="nc" id="L2479">        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, errorCode_);</span>
      }
<span class="nc" id="L2481">      getUnknownFields().writeTo(output);</span>
<span class="nc" id="L2482">    }</span>

    @java.lang.Override
    public int getSerializedSize() {
<span class="nc" id="L2486">      int size = memoizedSize;</span>
<span class="nc bnc" id="L2487" title="All 2 branches missed.">      if (size != -1) return size;</span>

<span class="nc" id="L2489">      size = 0;</span>
<span class="nc bnc" id="L2490" title="All 2 branches missed.">      if (((bitField0_ &amp; 0x00000001) != 0)) {</span>
<span class="nc" id="L2491">        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, secId_);</span>
      }
<span class="nc bnc" id="L2493" title="All 2 branches missed.">      if (((bitField0_ &amp; 0x00000002) != 0)) {</span>
<span class="nc" id="L2494">        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, universe_);</span>
      }
<span class="nc bnc" id="L2496" title="All 2 branches missed.">      for (int i = 0; i &lt; values_.size(); i++) {</span>
<span class="nc" id="L2497">        size += com.google.protobuf.CodedOutputStream</span>
<span class="nc" id="L2498">          .computeMessageSize(3, values_.get(i));</span>
      }
<span class="nc bnc" id="L2500" title="All 2 branches missed.">      if (((bitField0_ &amp; 0x00000004) != 0)) {</span>
<span class="nc" id="L2501">        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, dataId_);</span>
      }
<span class="nc bnc" id="L2503" title="All 2 branches missed.">      if (((bitField0_ &amp; 0x00000008) != 0)) {</span>
<span class="nc" id="L2504">        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, errorCode_);</span>
      }
<span class="nc" id="L2506">      size += getUnknownFields().getSerializedSize();</span>
<span class="nc" id="L2507">      memoizedSize = size;</span>
<span class="nc" id="L2508">      return size;</span>
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
<span class="nc bnc" id="L2513" title="All 2 branches missed.">      if (obj == this) {</span>
<span class="nc" id="L2514">       return true;</span>
      }
<span class="nc bnc" id="L2516" title="All 2 branches missed.">      if (!(obj instanceof com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData)) {</span>
<span class="nc" id="L2517">        return super.equals(obj);</span>
      }
<span class="nc" id="L2519">      com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData other = (com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData) obj;</span>

<span class="nc bnc" id="L2521" title="All 2 branches missed.">      if (hasSecId() != other.hasSecId()) return false;</span>
<span class="nc bnc" id="L2522" title="All 2 branches missed.">      if (hasSecId()) {</span>
<span class="nc" id="L2523">        if (!getSecId()</span>
<span class="nc bnc" id="L2524" title="All 2 branches missed.">            .equals(other.getSecId())) return false;</span>
      }
<span class="nc bnc" id="L2526" title="All 2 branches missed.">      if (hasUniverse() != other.hasUniverse()) return false;</span>
<span class="nc bnc" id="L2527" title="All 2 branches missed.">      if (hasUniverse()) {</span>
<span class="nc" id="L2528">        if (!getUniverse()</span>
<span class="nc bnc" id="L2529" title="All 2 branches missed.">            .equals(other.getUniverse())) return false;</span>
      }
<span class="nc" id="L2531">      if (!getValuesList()</span>
<span class="nc bnc" id="L2532" title="All 2 branches missed.">          .equals(other.getValuesList())) return false;</span>
<span class="nc bnc" id="L2533" title="All 2 branches missed.">      if (hasDataId() != other.hasDataId()) return false;</span>
<span class="nc bnc" id="L2534" title="All 2 branches missed.">      if (hasDataId()) {</span>
<span class="nc" id="L2535">        if (!getDataId()</span>
<span class="nc bnc" id="L2536" title="All 2 branches missed.">            .equals(other.getDataId())) return false;</span>
      }
<span class="nc bnc" id="L2538" title="All 2 branches missed.">      if (hasErrorCode() != other.hasErrorCode()) return false;</span>
<span class="nc bnc" id="L2539" title="All 2 branches missed.">      if (hasErrorCode()) {</span>
<span class="nc" id="L2540">        if (!getErrorCode()</span>
<span class="nc bnc" id="L2541" title="All 2 branches missed.">            .equals(other.getErrorCode())) return false;</span>
      }
<span class="nc bnc" id="L2543" title="All 2 branches missed.">      if (!getUnknownFields().equals(other.getUnknownFields())) return false;</span>
<span class="nc" id="L2544">      return true;</span>
    }

    @java.lang.Override
    public int hashCode() {
<span class="nc bnc" id="L2549" title="All 2 branches missed.">      if (memoizedHashCode != 0) {</span>
<span class="nc" id="L2550">        return memoizedHashCode;</span>
      }
<span class="nc" id="L2552">      int hash = 41;</span>
<span class="nc" id="L2553">      hash = (19 * hash) + getDescriptor().hashCode();</span>
<span class="nc bnc" id="L2554" title="All 2 branches missed.">      if (hasSecId()) {</span>
<span class="nc" id="L2555">        hash = (37 * hash) + SECID_FIELD_NUMBER;</span>
<span class="nc" id="L2556">        hash = (53 * hash) + getSecId().hashCode();</span>
      }
<span class="nc bnc" id="L2558" title="All 2 branches missed.">      if (hasUniverse()) {</span>
<span class="nc" id="L2559">        hash = (37 * hash) + UNIVERSE_FIELD_NUMBER;</span>
<span class="nc" id="L2560">        hash = (53 * hash) + getUniverse().hashCode();</span>
      }
<span class="nc bnc" id="L2562" title="All 2 branches missed.">      if (getValuesCount() &gt; 0) {</span>
<span class="nc" id="L2563">        hash = (37 * hash) + VALUES_FIELD_NUMBER;</span>
<span class="nc" id="L2564">        hash = (53 * hash) + getValuesList().hashCode();</span>
      }
<span class="nc bnc" id="L2566" title="All 2 branches missed.">      if (hasDataId()) {</span>
<span class="nc" id="L2567">        hash = (37 * hash) + DATAID_FIELD_NUMBER;</span>
<span class="nc" id="L2568">        hash = (53 * hash) + getDataId().hashCode();</span>
      }
<span class="nc bnc" id="L2570" title="All 2 branches missed.">      if (hasErrorCode()) {</span>
<span class="nc" id="L2571">        hash = (37 * hash) + ERRORCODE_FIELD_NUMBER;</span>
<span class="nc" id="L2572">        hash = (53 * hash) + getErrorCode().hashCode();</span>
      }
<span class="nc" id="L2574">      hash = (29 * hash) + getUnknownFields().hashCode();</span>
<span class="nc" id="L2575">      memoizedHashCode = hash;</span>
<span class="nc" id="L2576">      return hash;</span>
    }

    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L2582">      return PARSER.parseFrom(data);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L2588">      return PARSER.parseFrom(data, extensionRegistry);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L2593">      return PARSER.parseFrom(data);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L2599">      return PARSER.parseFrom(data, extensionRegistry);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L2603">      return PARSER.parseFrom(data);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L2609">      return PARSER.parseFrom(data, extensionRegistry);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData parseFrom(java.io.InputStream input)
        throws java.io.IOException {
<span class="nc" id="L2613">      return com.google.protobuf.GeneratedMessageV3</span>
<span class="nc" id="L2614">          .parseWithIOException(PARSER, input);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
<span class="nc" id="L2620">      return com.google.protobuf.GeneratedMessageV3</span>
<span class="nc" id="L2621">          .parseWithIOException(PARSER, input, extensionRegistry);</span>
    }

    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
<span class="nc" id="L2626">      return com.google.protobuf.GeneratedMessageV3</span>
<span class="nc" id="L2627">          .parseDelimitedWithIOException(PARSER, input);</span>
    }

    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
<span class="nc" id="L2634">      return com.google.protobuf.GeneratedMessageV3</span>
<span class="nc" id="L2635">          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
<span class="nc" id="L2640">      return com.google.protobuf.GeneratedMessageV3</span>
<span class="nc" id="L2641">          .parseWithIOException(PARSER, input);</span>
    }
    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
<span class="nc" id="L2647">      return com.google.protobuf.GeneratedMessageV3</span>
<span class="nc" id="L2648">          .parseWithIOException(PARSER, input, extensionRegistry);</span>
    }

    @java.lang.Override
<span class="nc" id="L2652">    public Builder newBuilderForType() { return newBuilder(); }</span>
    public static Builder newBuilder() {
<span class="nc" id="L2654">      return DEFAULT_INSTANCE.toBuilder();</span>
    }
    public static Builder newBuilder(com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData prototype) {
<span class="nc" id="L2657">      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);</span>
    }
    @java.lang.Override
    public Builder toBuilder() {
<span class="nc bnc" id="L2661" title="All 2 branches missed.">      return this == DEFAULT_INSTANCE</span>
<span class="nc" id="L2662">          ? new Builder() : new Builder().mergeFrom(this);</span>
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
<span class="nc" id="L2668">      Builder builder = new Builder(parent);</span>
<span class="nc" id="L2669">      return builder;</span>
    }
    /**
     * Protobuf type {@code protobuf.TimeSeriesData}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder&lt;Builder&gt; implements
        // @@protoc_insertion_point(builder_implements:protobuf.TimeSeriesData)
        com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDataOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
<span class="nc" id="L2680">        return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TimeSeriesData_descriptor;</span>
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
<span class="nc" id="L2686">        return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TimeSeriesData_fieldAccessorTable</span>
<span class="nc" id="L2687">            .ensureFieldAccessorsInitialized(</span>
                com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.class, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.Builder.class);
      }

      // Construct using com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.newBuilder()
<span class="nc" id="L2692">      private Builder() {</span>

<span class="nc" id="L2694">      }</span>

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
<span class="nc" id="L2698">        super(parent);</span>

<span class="nc" id="L2700">      }</span>
      @java.lang.Override
      public Builder clear() {
<span class="nc" id="L2703">        super.clear();</span>
<span class="nc" id="L2704">        bitField0_ = 0;</span>
<span class="nc" id="L2705">        secId_ = &quot;&quot;;</span>
<span class="nc" id="L2706">        universe_ = &quot;&quot;;</span>
<span class="nc bnc" id="L2707" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc" id="L2708">          values_ = java.util.Collections.emptyList();</span>
        } else {
<span class="nc" id="L2710">          values_ = null;</span>
<span class="nc" id="L2711">          valuesBuilder_.clear();</span>
        }
<span class="nc" id="L2713">        bitField0_ = (bitField0_ &amp; ~0x00000004);</span>
<span class="nc" id="L2714">        dataId_ = &quot;&quot;;</span>
<span class="nc" id="L2715">        errorCode_ = &quot;&quot;;</span>
<span class="nc" id="L2716">        return this;</span>
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
<span class="nc" id="L2722">        return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.internal_static_protobuf_TimeSeriesData_descriptor;</span>
      }

      @java.lang.Override
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData getDefaultInstanceForType() {
<span class="nc" id="L2727">        return com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.getDefaultInstance();</span>
      }

      @java.lang.Override
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData build() {
<span class="nc" id="L2732">        com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData result = buildPartial();</span>
<span class="nc bnc" id="L2733" title="All 2 branches missed.">        if (!result.isInitialized()) {</span>
<span class="nc" id="L2734">          throw newUninitializedMessageException(result);</span>
        }
<span class="nc" id="L2736">        return result;</span>
      }

      @java.lang.Override
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData buildPartial() {
<span class="nc" id="L2741">        com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData result = new com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData(this);</span>
<span class="nc" id="L2742">        buildPartialRepeatedFields(result);</span>
<span class="nc bnc" id="L2743" title="All 2 branches missed.">        if (bitField0_ != 0) { buildPartial0(result); }</span>
<span class="nc" id="L2744">        onBuilt();</span>
<span class="nc" id="L2745">        return result;</span>
      }

      private void buildPartialRepeatedFields(com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData result) {
<span class="nc bnc" id="L2749" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc bnc" id="L2750" title="All 2 branches missed.">          if (((bitField0_ &amp; 0x00000004) != 0)) {</span>
<span class="nc" id="L2751">            values_ = java.util.Collections.unmodifiableList(values_);</span>
<span class="nc" id="L2752">            bitField0_ = (bitField0_ &amp; ~0x00000004);</span>
          }
<span class="nc" id="L2754">          result.values_ = values_;</span>
        } else {
<span class="nc" id="L2756">          result.values_ = valuesBuilder_.build();</span>
        }
<span class="nc" id="L2758">      }</span>

      private void buildPartial0(com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData result) {
<span class="nc" id="L2761">        int from_bitField0_ = bitField0_;</span>
<span class="nc" id="L2762">        int to_bitField0_ = 0;</span>
<span class="nc bnc" id="L2763" title="All 2 branches missed.">        if (((from_bitField0_ &amp; 0x00000001) != 0)) {</span>
<span class="nc" id="L2764">          result.secId_ = secId_;</span>
<span class="nc" id="L2765">          to_bitField0_ |= 0x00000001;</span>
        }
<span class="nc bnc" id="L2767" title="All 2 branches missed.">        if (((from_bitField0_ &amp; 0x00000002) != 0)) {</span>
<span class="nc" id="L2768">          result.universe_ = universe_;</span>
<span class="nc" id="L2769">          to_bitField0_ |= 0x00000002;</span>
        }
<span class="nc bnc" id="L2771" title="All 2 branches missed.">        if (((from_bitField0_ &amp; 0x00000008) != 0)) {</span>
<span class="nc" id="L2772">          result.dataId_ = dataId_;</span>
<span class="nc" id="L2773">          to_bitField0_ |= 0x00000004;</span>
        }
<span class="nc bnc" id="L2775" title="All 2 branches missed.">        if (((from_bitField0_ &amp; 0x00000010) != 0)) {</span>
<span class="nc" id="L2776">          result.errorCode_ = errorCode_;</span>
<span class="nc" id="L2777">          to_bitField0_ |= 0x00000008;</span>
        }
<span class="nc" id="L2779">        result.bitField0_ |= to_bitField0_;</span>
<span class="nc" id="L2780">      }</span>

      @java.lang.Override
      public Builder clone() {
<span class="nc" id="L2784">        return super.clone();</span>
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
<span class="nc" id="L2790">        return super.setField(field, value);</span>
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
<span class="nc" id="L2795">        return super.clearField(field);</span>
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
<span class="nc" id="L2800">        return super.clearOneof(oneof);</span>
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
<span class="nc" id="L2806">        return super.setRepeatedField(field, index, value);</span>
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
<span class="nc" id="L2812">        return super.addRepeatedField(field, value);</span>
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
<span class="nc bnc" id="L2816" title="All 2 branches missed.">        if (other instanceof com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData) {</span>
<span class="nc" id="L2817">          return mergeFrom((com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData)other);</span>
        } else {
<span class="nc" id="L2819">          super.mergeFrom(other);</span>
<span class="nc" id="L2820">          return this;</span>
        }
      }

      public Builder mergeFrom(com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData other) {
<span class="nc bnc" id="L2825" title="All 2 branches missed.">        if (other == com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData.getDefaultInstance()) return this;</span>
<span class="nc bnc" id="L2826" title="All 2 branches missed.">        if (other.hasSecId()) {</span>
<span class="nc" id="L2827">          secId_ = other.secId_;</span>
<span class="nc" id="L2828">          bitField0_ |= 0x00000001;</span>
<span class="nc" id="L2829">          onChanged();</span>
        }
<span class="nc bnc" id="L2831" title="All 2 branches missed.">        if (other.hasUniverse()) {</span>
<span class="nc" id="L2832">          universe_ = other.universe_;</span>
<span class="nc" id="L2833">          bitField0_ |= 0x00000002;</span>
<span class="nc" id="L2834">          onChanged();</span>
        }
<span class="nc bnc" id="L2836" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc bnc" id="L2837" title="All 2 branches missed.">          if (!other.values_.isEmpty()) {</span>
<span class="nc bnc" id="L2838" title="All 2 branches missed.">            if (values_.isEmpty()) {</span>
<span class="nc" id="L2839">              values_ = other.values_;</span>
<span class="nc" id="L2840">              bitField0_ = (bitField0_ &amp; ~0x00000004);</span>
            } else {
<span class="nc" id="L2842">              ensureValuesIsMutable();</span>
<span class="nc" id="L2843">              values_.addAll(other.values_);</span>
            }
<span class="nc" id="L2845">            onChanged();</span>
          }
        } else {
<span class="nc bnc" id="L2848" title="All 2 branches missed.">          if (!other.values_.isEmpty()) {</span>
<span class="nc bnc" id="L2849" title="All 2 branches missed.">            if (valuesBuilder_.isEmpty()) {</span>
<span class="nc" id="L2850">              valuesBuilder_.dispose();</span>
<span class="nc" id="L2851">              valuesBuilder_ = null;</span>
<span class="nc" id="L2852">              values_ = other.values_;</span>
<span class="nc" id="L2853">              bitField0_ = (bitField0_ &amp; ~0x00000004);</span>
<span class="nc" id="L2854">              valuesBuilder_ = </span>
<span class="nc bnc" id="L2855" title="All 2 branches missed.">                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?</span>
<span class="nc" id="L2856">                   getValuesFieldBuilder() : null;</span>
            } else {
<span class="nc" id="L2858">              valuesBuilder_.addAllMessages(other.values_);</span>
            }
          }
        }
<span class="nc bnc" id="L2862" title="All 2 branches missed.">        if (other.hasDataId()) {</span>
<span class="nc" id="L2863">          dataId_ = other.dataId_;</span>
<span class="nc" id="L2864">          bitField0_ |= 0x00000008;</span>
<span class="nc" id="L2865">          onChanged();</span>
        }
<span class="nc bnc" id="L2867" title="All 2 branches missed.">        if (other.hasErrorCode()) {</span>
<span class="nc" id="L2868">          errorCode_ = other.errorCode_;</span>
<span class="nc" id="L2869">          bitField0_ |= 0x00000010;</span>
<span class="nc" id="L2870">          onChanged();</span>
        }
<span class="nc" id="L2872">        this.mergeUnknownFields(other.getUnknownFields());</span>
<span class="nc" id="L2873">        onChanged();</span>
<span class="nc" id="L2874">        return this;</span>
      }

      @java.lang.Override
      public final boolean isInitialized() {
<span class="nc bnc" id="L2879" title="All 2 branches missed.">        if (!hasSecId()) {</span>
<span class="nc" id="L2880">          return false;</span>
        }
<span class="nc" id="L2882">        return true;</span>
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
<span class="nc bnc" id="L2890" title="All 2 branches missed.">        if (extensionRegistry == null) {</span>
<span class="nc" id="L2891">          throw new java.lang.NullPointerException();</span>
        }
        try {
<span class="nc" id="L2894">          boolean done = false;</span>
<span class="nc bnc" id="L2895" title="All 2 branches missed.">          while (!done) {</span>
<span class="nc" id="L2896">            int tag = input.readTag();</span>
<span class="nc bnc" id="L2897" title="All 7 branches missed.">            switch (tag) {</span>
              case 0:
<span class="nc" id="L2899">                done = true;</span>
<span class="nc" id="L2900">                break;</span>
              case 10: {
<span class="nc" id="L2902">                secId_ = input.readBytes();</span>
<span class="nc" id="L2903">                bitField0_ |= 0x00000001;</span>
<span class="nc" id="L2904">                break;</span>
              } // case 10
              case 18: {
<span class="nc" id="L2907">                universe_ = input.readBytes();</span>
<span class="nc" id="L2908">                bitField0_ |= 0x00000002;</span>
<span class="nc" id="L2909">                break;</span>
              } // case 18
              case 26: {
<span class="nc" id="L2912">                com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair m =</span>
<span class="nc" id="L2913">                    input.readMessage(</span>
                        com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.PARSER,
                        extensionRegistry);
<span class="nc bnc" id="L2916" title="All 2 branches missed.">                if (valuesBuilder_ == null) {</span>
<span class="nc" id="L2917">                  ensureValuesIsMutable();</span>
<span class="nc" id="L2918">                  values_.add(m);</span>
                } else {
<span class="nc" id="L2920">                  valuesBuilder_.addMessage(m);</span>
                }
<span class="nc" id="L2922">                break;</span>
              } // case 26
              case 34: {
<span class="nc" id="L2925">                dataId_ = input.readBytes();</span>
<span class="nc" id="L2926">                bitField0_ |= 0x00000008;</span>
<span class="nc" id="L2927">                break;</span>
              } // case 34
              case 42: {
<span class="nc" id="L2930">                errorCode_ = input.readBytes();</span>
<span class="nc" id="L2931">                bitField0_ |= 0x00000010;</span>
<span class="nc" id="L2932">                break;</span>
              } // case 42
              default: {
<span class="nc bnc" id="L2935" title="All 2 branches missed.">                if (!super.parseUnknownField(input, extensionRegistry, tag)) {</span>
<span class="nc" id="L2936">                  done = true; // was an endgroup tag</span>
                }
                break;
              } // default:
            } // switch (tag)
<span class="nc" id="L2941">          } // while (!done)</span>
<span class="nc" id="L2942">        } catch (com.google.protobuf.InvalidProtocolBufferException e) {</span>
<span class="nc" id="L2943">          throw e.unwrapIOException();</span>
        } finally {
<span class="nc" id="L2945">          onChanged();</span>
        } // finally
<span class="nc" id="L2947">        return this;</span>
      }
      private int bitField0_;

<span class="nc" id="L2951">      private java.lang.Object secId_ = &quot;&quot;;</span>
      /**
       * &lt;code&gt;required string SecId = 1;&lt;/code&gt;
       * @return Whether the secId field is set.
       */
      public boolean hasSecId() {
<span class="nc bnc" id="L2957" title="All 2 branches missed.">        return ((bitField0_ &amp; 0x00000001) != 0);</span>
      }
      /**
       * &lt;code&gt;required string SecId = 1;&lt;/code&gt;
       * @return The secId.
       */
      public java.lang.String getSecId() {
<span class="nc" id="L2964">        java.lang.Object ref = secId_;</span>
<span class="nc bnc" id="L2965" title="All 2 branches missed.">        if (!(ref instanceof java.lang.String)) {</span>
<span class="nc" id="L2966">          com.google.protobuf.ByteString bs =</span>
              (com.google.protobuf.ByteString) ref;
<span class="nc" id="L2968">          java.lang.String s = bs.toStringUtf8();</span>
<span class="nc bnc" id="L2969" title="All 2 branches missed.">          if (bs.isValidUtf8()) {</span>
<span class="nc" id="L2970">            secId_ = s;</span>
          }
<span class="nc" id="L2972">          return s;</span>
        } else {
<span class="nc" id="L2974">          return (java.lang.String) ref;</span>
        }
      }
      /**
       * &lt;code&gt;required string SecId = 1;&lt;/code&gt;
       * @return The bytes for secId.
       */
      public com.google.protobuf.ByteString
          getSecIdBytes() {
<span class="nc" id="L2983">        java.lang.Object ref = secId_;</span>
<span class="nc bnc" id="L2984" title="All 2 branches missed.">        if (ref instanceof String) {</span>
<span class="nc" id="L2985">          com.google.protobuf.ByteString b = </span>
<span class="nc" id="L2986">              com.google.protobuf.ByteString.copyFromUtf8(</span>
                  (java.lang.String) ref);
<span class="nc" id="L2988">          secId_ = b;</span>
<span class="nc" id="L2989">          return b;</span>
        } else {
<span class="nc" id="L2991">          return (com.google.protobuf.ByteString) ref;</span>
        }
      }
      /**
       * &lt;code&gt;required string SecId = 1;&lt;/code&gt;
       * @param value The secId to set.
       * @return This builder for chaining.
       */
      public Builder setSecId(
          java.lang.String value) {
<span class="nc bnc" id="L3001" title="All 2 branches missed.">        if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L3002">        secId_ = value;</span>
<span class="nc" id="L3003">        bitField0_ |= 0x00000001;</span>
<span class="nc" id="L3004">        onChanged();</span>
<span class="nc" id="L3005">        return this;</span>
      }
      /**
       * &lt;code&gt;required string SecId = 1;&lt;/code&gt;
       * @return This builder for chaining.
       */
      public Builder clearSecId() {
<span class="nc" id="L3012">        secId_ = getDefaultInstance().getSecId();</span>
<span class="nc" id="L3013">        bitField0_ = (bitField0_ &amp; ~0x00000001);</span>
<span class="nc" id="L3014">        onChanged();</span>
<span class="nc" id="L3015">        return this;</span>
      }
      /**
       * &lt;code&gt;required string SecId = 1;&lt;/code&gt;
       * @param value The bytes for secId to set.
       * @return This builder for chaining.
       */
      public Builder setSecIdBytes(
          com.google.protobuf.ByteString value) {
<span class="nc bnc" id="L3024" title="All 2 branches missed.">        if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L3025">        secId_ = value;</span>
<span class="nc" id="L3026">        bitField0_ |= 0x00000001;</span>
<span class="nc" id="L3027">        onChanged();</span>
<span class="nc" id="L3028">        return this;</span>
      }

<span class="nc" id="L3031">      private java.lang.Object universe_ = &quot;&quot;;</span>
      /**
       * &lt;code&gt;optional string Universe = 2;&lt;/code&gt;
       * @return Whether the universe field is set.
       */
      public boolean hasUniverse() {
<span class="nc bnc" id="L3037" title="All 2 branches missed.">        return ((bitField0_ &amp; 0x00000002) != 0);</span>
      }
      /**
       * &lt;code&gt;optional string Universe = 2;&lt;/code&gt;
       * @return The universe.
       */
      public java.lang.String getUniverse() {
<span class="nc" id="L3044">        java.lang.Object ref = universe_;</span>
<span class="nc bnc" id="L3045" title="All 2 branches missed.">        if (!(ref instanceof java.lang.String)) {</span>
<span class="nc" id="L3046">          com.google.protobuf.ByteString bs =</span>
              (com.google.protobuf.ByteString) ref;
<span class="nc" id="L3048">          java.lang.String s = bs.toStringUtf8();</span>
<span class="nc bnc" id="L3049" title="All 2 branches missed.">          if (bs.isValidUtf8()) {</span>
<span class="nc" id="L3050">            universe_ = s;</span>
          }
<span class="nc" id="L3052">          return s;</span>
        } else {
<span class="nc" id="L3054">          return (java.lang.String) ref;</span>
        }
      }
      /**
       * &lt;code&gt;optional string Universe = 2;&lt;/code&gt;
       * @return The bytes for universe.
       */
      public com.google.protobuf.ByteString
          getUniverseBytes() {
<span class="nc" id="L3063">        java.lang.Object ref = universe_;</span>
<span class="nc bnc" id="L3064" title="All 2 branches missed.">        if (ref instanceof String) {</span>
<span class="nc" id="L3065">          com.google.protobuf.ByteString b = </span>
<span class="nc" id="L3066">              com.google.protobuf.ByteString.copyFromUtf8(</span>
                  (java.lang.String) ref);
<span class="nc" id="L3068">          universe_ = b;</span>
<span class="nc" id="L3069">          return b;</span>
        } else {
<span class="nc" id="L3071">          return (com.google.protobuf.ByteString) ref;</span>
        }
      }
      /**
       * &lt;code&gt;optional string Universe = 2;&lt;/code&gt;
       * @param value The universe to set.
       * @return This builder for chaining.
       */
      public Builder setUniverse(
          java.lang.String value) {
<span class="nc bnc" id="L3081" title="All 2 branches missed.">        if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L3082">        universe_ = value;</span>
<span class="nc" id="L3083">        bitField0_ |= 0x00000002;</span>
<span class="nc" id="L3084">        onChanged();</span>
<span class="nc" id="L3085">        return this;</span>
      }
      /**
       * &lt;code&gt;optional string Universe = 2;&lt;/code&gt;
       * @return This builder for chaining.
       */
      public Builder clearUniverse() {
<span class="nc" id="L3092">        universe_ = getDefaultInstance().getUniverse();</span>
<span class="nc" id="L3093">        bitField0_ = (bitField0_ &amp; ~0x00000002);</span>
<span class="nc" id="L3094">        onChanged();</span>
<span class="nc" id="L3095">        return this;</span>
      }
      /**
       * &lt;code&gt;optional string Universe = 2;&lt;/code&gt;
       * @param value The bytes for universe to set.
       * @return This builder for chaining.
       */
      public Builder setUniverseBytes(
          com.google.protobuf.ByteString value) {
<span class="nc bnc" id="L3104" title="All 2 branches missed.">        if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L3105">        universe_ = value;</span>
<span class="nc" id="L3106">        bitField0_ |= 0x00000002;</span>
<span class="nc" id="L3107">        onChanged();</span>
<span class="nc" id="L3108">        return this;</span>
      }

<span class="nc" id="L3111">      private java.util.List&lt;com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair&gt; values_ =</span>
<span class="nc" id="L3112">        java.util.Collections.emptyList();</span>
      private void ensureValuesIsMutable() {
<span class="nc bnc" id="L3114" title="All 2 branches missed.">        if (!((bitField0_ &amp; 0x00000004) != 0)) {</span>
<span class="nc" id="L3115">          values_ = new java.util.ArrayList&lt;com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair&gt;(values_);</span>
<span class="nc" id="L3116">          bitField0_ |= 0x00000004;</span>
         }
<span class="nc" id="L3118">      }</span>

      private com.google.protobuf.RepeatedFieldBuilderV3&lt;
          com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.Builder, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePairOrBuilder&gt; valuesBuilder_;

      /**
       * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
       */
      public java.util.List&lt;com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair&gt; getValuesList() {
<span class="nc bnc" id="L3127" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc" id="L3128">          return java.util.Collections.unmodifiableList(values_);</span>
        } else {
<span class="nc" id="L3130">          return valuesBuilder_.getMessageList();</span>
        }
      }
      /**
       * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
       */
      public int getValuesCount() {
<span class="nc bnc" id="L3137" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc" id="L3138">          return values_.size();</span>
        } else {
<span class="nc" id="L3140">          return valuesBuilder_.getCount();</span>
        }
      }
      /**
       * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
       */
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair getValues(int index) {
<span class="nc bnc" id="L3147" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc" id="L3148">          return values_.get(index);</span>
        } else {
<span class="nc" id="L3150">          return valuesBuilder_.getMessage(index);</span>
        }
      }
      /**
       * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
       */
      public Builder setValues(
          int index, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair value) {
<span class="nc bnc" id="L3158" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc bnc" id="L3159" title="All 2 branches missed.">          if (value == null) {</span>
<span class="nc" id="L3160">            throw new NullPointerException();</span>
          }
<span class="nc" id="L3162">          ensureValuesIsMutable();</span>
<span class="nc" id="L3163">          values_.set(index, value);</span>
<span class="nc" id="L3164">          onChanged();</span>
        } else {
<span class="nc" id="L3166">          valuesBuilder_.setMessage(index, value);</span>
        }
<span class="nc" id="L3168">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
       */
      public Builder setValues(
          int index, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.Builder builderForValue) {
<span class="nc bnc" id="L3175" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc" id="L3176">          ensureValuesIsMutable();</span>
<span class="nc" id="L3177">          values_.set(index, builderForValue.build());</span>
<span class="nc" id="L3178">          onChanged();</span>
        } else {
<span class="nc" id="L3180">          valuesBuilder_.setMessage(index, builderForValue.build());</span>
        }
<span class="nc" id="L3182">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
       */
      public Builder addValues(com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair value) {
<span class="nc bnc" id="L3188" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc bnc" id="L3189" title="All 2 branches missed.">          if (value == null) {</span>
<span class="nc" id="L3190">            throw new NullPointerException();</span>
          }
<span class="nc" id="L3192">          ensureValuesIsMutable();</span>
<span class="nc" id="L3193">          values_.add(value);</span>
<span class="nc" id="L3194">          onChanged();</span>
        } else {
<span class="nc" id="L3196">          valuesBuilder_.addMessage(value);</span>
        }
<span class="nc" id="L3198">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
       */
      public Builder addValues(
          int index, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair value) {
<span class="nc bnc" id="L3205" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc bnc" id="L3206" title="All 2 branches missed.">          if (value == null) {</span>
<span class="nc" id="L3207">            throw new NullPointerException();</span>
          }
<span class="nc" id="L3209">          ensureValuesIsMutable();</span>
<span class="nc" id="L3210">          values_.add(index, value);</span>
<span class="nc" id="L3211">          onChanged();</span>
        } else {
<span class="nc" id="L3213">          valuesBuilder_.addMessage(index, value);</span>
        }
<span class="nc" id="L3215">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
       */
      public Builder addValues(
          com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.Builder builderForValue) {
<span class="nc bnc" id="L3222" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc" id="L3223">          ensureValuesIsMutable();</span>
<span class="nc" id="L3224">          values_.add(builderForValue.build());</span>
<span class="nc" id="L3225">          onChanged();</span>
        } else {
<span class="nc" id="L3227">          valuesBuilder_.addMessage(builderForValue.build());</span>
        }
<span class="nc" id="L3229">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
       */
      public Builder addValues(
          int index, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.Builder builderForValue) {
<span class="nc bnc" id="L3236" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc" id="L3237">          ensureValuesIsMutable();</span>
<span class="nc" id="L3238">          values_.add(index, builderForValue.build());</span>
<span class="nc" id="L3239">          onChanged();</span>
        } else {
<span class="nc" id="L3241">          valuesBuilder_.addMessage(index, builderForValue.build());</span>
        }
<span class="nc" id="L3243">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
       */
      public Builder addAllValues(
          java.lang.Iterable&lt;? extends com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair&gt; values) {
<span class="nc bnc" id="L3250" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc" id="L3251">          ensureValuesIsMutable();</span>
<span class="nc" id="L3252">          com.google.protobuf.AbstractMessageLite.Builder.addAll(</span>
              values, values_);
<span class="nc" id="L3254">          onChanged();</span>
        } else {
<span class="nc" id="L3256">          valuesBuilder_.addAllMessages(values);</span>
        }
<span class="nc" id="L3258">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
       */
      public Builder clearValues() {
<span class="nc bnc" id="L3264" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc" id="L3265">          values_ = java.util.Collections.emptyList();</span>
<span class="nc" id="L3266">          bitField0_ = (bitField0_ &amp; ~0x00000004);</span>
<span class="nc" id="L3267">          onChanged();</span>
        } else {
<span class="nc" id="L3269">          valuesBuilder_.clear();</span>
        }
<span class="nc" id="L3271">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
       */
      public Builder removeValues(int index) {
<span class="nc bnc" id="L3277" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc" id="L3278">          ensureValuesIsMutable();</span>
<span class="nc" id="L3279">          values_.remove(index);</span>
<span class="nc" id="L3280">          onChanged();</span>
        } else {
<span class="nc" id="L3282">          valuesBuilder_.remove(index);</span>
        }
<span class="nc" id="L3284">        return this;</span>
      }
      /**
       * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
       */
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.Builder getValuesBuilder(
          int index) {
<span class="nc" id="L3291">        return getValuesFieldBuilder().getBuilder(index);</span>
      }
      /**
       * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
       */
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePairOrBuilder getValuesOrBuilder(
          int index) {
<span class="nc bnc" id="L3298" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc" id="L3299">          return values_.get(index);  } else {</span>
<span class="nc" id="L3300">          return valuesBuilder_.getMessageOrBuilder(index);</span>
        }
      }
      /**
       * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
       */
      public java.util.List&lt;? extends com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePairOrBuilder&gt; 
           getValuesOrBuilderList() {
<span class="nc bnc" id="L3308" title="All 2 branches missed.">        if (valuesBuilder_ != null) {</span>
<span class="nc" id="L3309">          return valuesBuilder_.getMessageOrBuilderList();</span>
        } else {
<span class="nc" id="L3311">          return java.util.Collections.unmodifiableList(values_);</span>
        }
      }
      /**
       * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
       */
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.Builder addValuesBuilder() {
<span class="nc" id="L3318">        return getValuesFieldBuilder().addBuilder(</span>
<span class="nc" id="L3319">            com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.getDefaultInstance());</span>
      }
      /**
       * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
       */
      public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.Builder addValuesBuilder(
          int index) {
<span class="nc" id="L3326">        return getValuesFieldBuilder().addBuilder(</span>
<span class="nc" id="L3327">            index, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.getDefaultInstance());</span>
      }
      /**
       * &lt;code&gt;repeated .protobuf.TSValuePair Values = 3;&lt;/code&gt;
       */
      public java.util.List&lt;com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.Builder&gt; 
           getValuesBuilderList() {
<span class="nc" id="L3334">        return getValuesFieldBuilder().getBuilderList();</span>
      }
      private com.google.protobuf.RepeatedFieldBuilderV3&lt;
          com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.Builder, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePairOrBuilder&gt; 
          getValuesFieldBuilder() {
<span class="nc bnc" id="L3339" title="All 2 branches missed.">        if (valuesBuilder_ == null) {</span>
<span class="nc bnc" id="L3340" title="All 2 branches missed.">          valuesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3&lt;</span>
              com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePair.Builder, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TSValuePairOrBuilder&gt;(
                  values_,
                  ((bitField0_ &amp; 0x00000004) != 0),
<span class="nc" id="L3344">                  getParentForChildren(),</span>
<span class="nc" id="L3345">                  isClean());</span>
<span class="nc" id="L3346">          values_ = null;</span>
        }
<span class="nc" id="L3348">        return valuesBuilder_;</span>
      }

<span class="nc" id="L3351">      private java.lang.Object dataId_ = &quot;&quot;;</span>
      /**
       * &lt;code&gt;optional string DataId = 4;&lt;/code&gt;
       * @return Whether the dataId field is set.
       */
      public boolean hasDataId() {
<span class="nc bnc" id="L3357" title="All 2 branches missed.">        return ((bitField0_ &amp; 0x00000008) != 0);</span>
      }
      /**
       * &lt;code&gt;optional string DataId = 4;&lt;/code&gt;
       * @return The dataId.
       */
      public java.lang.String getDataId() {
<span class="nc" id="L3364">        java.lang.Object ref = dataId_;</span>
<span class="nc bnc" id="L3365" title="All 2 branches missed.">        if (!(ref instanceof java.lang.String)) {</span>
<span class="nc" id="L3366">          com.google.protobuf.ByteString bs =</span>
              (com.google.protobuf.ByteString) ref;
<span class="nc" id="L3368">          java.lang.String s = bs.toStringUtf8();</span>
<span class="nc bnc" id="L3369" title="All 2 branches missed.">          if (bs.isValidUtf8()) {</span>
<span class="nc" id="L3370">            dataId_ = s;</span>
          }
<span class="nc" id="L3372">          return s;</span>
        } else {
<span class="nc" id="L3374">          return (java.lang.String) ref;</span>
        }
      }
      /**
       * &lt;code&gt;optional string DataId = 4;&lt;/code&gt;
       * @return The bytes for dataId.
       */
      public com.google.protobuf.ByteString
          getDataIdBytes() {
<span class="nc" id="L3383">        java.lang.Object ref = dataId_;</span>
<span class="nc bnc" id="L3384" title="All 2 branches missed.">        if (ref instanceof String) {</span>
<span class="nc" id="L3385">          com.google.protobuf.ByteString b = </span>
<span class="nc" id="L3386">              com.google.protobuf.ByteString.copyFromUtf8(</span>
                  (java.lang.String) ref);
<span class="nc" id="L3388">          dataId_ = b;</span>
<span class="nc" id="L3389">          return b;</span>
        } else {
<span class="nc" id="L3391">          return (com.google.protobuf.ByteString) ref;</span>
        }
      }
      /**
       * &lt;code&gt;optional string DataId = 4;&lt;/code&gt;
       * @param value The dataId to set.
       * @return This builder for chaining.
       */
      public Builder setDataId(
          java.lang.String value) {
<span class="nc bnc" id="L3401" title="All 2 branches missed.">        if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L3402">        dataId_ = value;</span>
<span class="nc" id="L3403">        bitField0_ |= 0x00000008;</span>
<span class="nc" id="L3404">        onChanged();</span>
<span class="nc" id="L3405">        return this;</span>
      }
      /**
       * &lt;code&gt;optional string DataId = 4;&lt;/code&gt;
       * @return This builder for chaining.
       */
      public Builder clearDataId() {
<span class="nc" id="L3412">        dataId_ = getDefaultInstance().getDataId();</span>
<span class="nc" id="L3413">        bitField0_ = (bitField0_ &amp; ~0x00000008);</span>
<span class="nc" id="L3414">        onChanged();</span>
<span class="nc" id="L3415">        return this;</span>
      }
      /**
       * &lt;code&gt;optional string DataId = 4;&lt;/code&gt;
       * @param value The bytes for dataId to set.
       * @return This builder for chaining.
       */
      public Builder setDataIdBytes(
          com.google.protobuf.ByteString value) {
<span class="nc bnc" id="L3424" title="All 2 branches missed.">        if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L3425">        dataId_ = value;</span>
<span class="nc" id="L3426">        bitField0_ |= 0x00000008;</span>
<span class="nc" id="L3427">        onChanged();</span>
<span class="nc" id="L3428">        return this;</span>
      }

<span class="nc" id="L3431">      private java.lang.Object errorCode_ = &quot;&quot;;</span>
      /**
       * &lt;code&gt;optional string ErrorCode = 5;&lt;/code&gt;
       * @return Whether the errorCode field is set.
       */
      public boolean hasErrorCode() {
<span class="nc bnc" id="L3437" title="All 2 branches missed.">        return ((bitField0_ &amp; 0x00000010) != 0);</span>
      }
      /**
       * &lt;code&gt;optional string ErrorCode = 5;&lt;/code&gt;
       * @return The errorCode.
       */
      public java.lang.String getErrorCode() {
<span class="nc" id="L3444">        java.lang.Object ref = errorCode_;</span>
<span class="nc bnc" id="L3445" title="All 2 branches missed.">        if (!(ref instanceof java.lang.String)) {</span>
<span class="nc" id="L3446">          com.google.protobuf.ByteString bs =</span>
              (com.google.protobuf.ByteString) ref;
<span class="nc" id="L3448">          java.lang.String s = bs.toStringUtf8();</span>
<span class="nc bnc" id="L3449" title="All 2 branches missed.">          if (bs.isValidUtf8()) {</span>
<span class="nc" id="L3450">            errorCode_ = s;</span>
          }
<span class="nc" id="L3452">          return s;</span>
        } else {
<span class="nc" id="L3454">          return (java.lang.String) ref;</span>
        }
      }
      /**
       * &lt;code&gt;optional string ErrorCode = 5;&lt;/code&gt;
       * @return The bytes for errorCode.
       */
      public com.google.protobuf.ByteString
          getErrorCodeBytes() {
<span class="nc" id="L3463">        java.lang.Object ref = errorCode_;</span>
<span class="nc bnc" id="L3464" title="All 2 branches missed.">        if (ref instanceof String) {</span>
<span class="nc" id="L3465">          com.google.protobuf.ByteString b = </span>
<span class="nc" id="L3466">              com.google.protobuf.ByteString.copyFromUtf8(</span>
                  (java.lang.String) ref);
<span class="nc" id="L3468">          errorCode_ = b;</span>
<span class="nc" id="L3469">          return b;</span>
        } else {
<span class="nc" id="L3471">          return (com.google.protobuf.ByteString) ref;</span>
        }
      }
      /**
       * &lt;code&gt;optional string ErrorCode = 5;&lt;/code&gt;
       * @param value The errorCode to set.
       * @return This builder for chaining.
       */
      public Builder setErrorCode(
          java.lang.String value) {
<span class="nc bnc" id="L3481" title="All 2 branches missed.">        if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L3482">        errorCode_ = value;</span>
<span class="nc" id="L3483">        bitField0_ |= 0x00000010;</span>
<span class="nc" id="L3484">        onChanged();</span>
<span class="nc" id="L3485">        return this;</span>
      }
      /**
       * &lt;code&gt;optional string ErrorCode = 5;&lt;/code&gt;
       * @return This builder for chaining.
       */
      public Builder clearErrorCode() {
<span class="nc" id="L3492">        errorCode_ = getDefaultInstance().getErrorCode();</span>
<span class="nc" id="L3493">        bitField0_ = (bitField0_ &amp; ~0x00000010);</span>
<span class="nc" id="L3494">        onChanged();</span>
<span class="nc" id="L3495">        return this;</span>
      }
      /**
       * &lt;code&gt;optional string ErrorCode = 5;&lt;/code&gt;
       * @param value The bytes for errorCode to set.
       * @return This builder for chaining.
       */
      public Builder setErrorCodeBytes(
          com.google.protobuf.ByteString value) {
<span class="nc bnc" id="L3504" title="All 2 branches missed.">        if (value == null) { throw new NullPointerException(); }</span>
<span class="nc" id="L3505">        errorCode_ = value;</span>
<span class="nc" id="L3506">        bitField0_ |= 0x00000010;</span>
<span class="nc" id="L3507">        onChanged();</span>
<span class="nc" id="L3508">        return this;</span>
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
<span class="nc" id="L3513">        return super.setUnknownFields(unknownFields);</span>
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
<span class="nc" id="L3519">        return super.mergeUnknownFields(unknownFields);</span>
      }


      // @@protoc_insertion_point(builder_scope:protobuf.TimeSeriesData)
    }

    // @@protoc_insertion_point(class_scope:protobuf.TimeSeriesData)
    private static final com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData DEFAULT_INSTANCE;
    static {
<span class="nc" id="L3529">      DEFAULT_INSTANCE = new com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData();</span>
    }

    public static com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData getDefaultInstance() {
<span class="nc" id="L3533">      return DEFAULT_INSTANCE;</span>
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser&lt;TimeSeriesData&gt;
<span class="nc" id="L3537">        PARSER = new com.google.protobuf.AbstractParser&lt;TimeSeriesData&gt;() {</span>
      @java.lang.Override
      public TimeSeriesData parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
<span class="nc" id="L3543">        Builder builder = newBuilder();</span>
        try {
<span class="nc" id="L3545">          builder.mergeFrom(input, extensionRegistry);</span>
<span class="nc" id="L3546">        } catch (com.google.protobuf.InvalidProtocolBufferException e) {</span>
<span class="nc" id="L3547">          throw e.setUnfinishedMessage(builder.buildPartial());</span>
<span class="nc" id="L3548">        } catch (com.google.protobuf.UninitializedMessageException e) {</span>
<span class="nc" id="L3549">          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());</span>
<span class="nc" id="L3550">        } catch (java.io.IOException e) {</span>
<span class="nc" id="L3551">          throw new com.google.protobuf.InvalidProtocolBufferException(e)</span>
<span class="nc" id="L3552">              .setUnfinishedMessage(builder.buildPartial());</span>
<span class="nc" id="L3553">        }</span>
<span class="nc" id="L3554">        return builder.buildPartial();</span>
      }
    };

    public static com.google.protobuf.Parser&lt;TimeSeriesData&gt; parser() {
<span class="nc" id="L3559">      return PARSER;</span>
    }

    @java.lang.Override
    public com.google.protobuf.Parser&lt;TimeSeriesData&gt; getParserForType() {
<span class="nc" id="L3564">      return PARSER;</span>
    }

    @java.lang.Override
    public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesData getDefaultInstanceForType() {
<span class="nc" id="L3569">      return DEFAULT_INSTANCE;</span>
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_protobuf_TSValuePair_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_protobuf_TSValuePair_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_protobuf_TimeSeriesDatas_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_protobuf_TimeSeriesDatas_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_protobuf_TimeSeriesData_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_protobuf_TimeSeriesData_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
<span class="nc" id="L3592">    return descriptor;</span>
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
<span class="nc" id="L3597">    java.lang.String[] descriptorData = {</span>
      &quot;\n\021TsCacheData.proto\022\010protobuf\&quot;=\n\013TSValue&quot; +
      &quot;Pair\022\r\n\005dates\030\001 \003(\003\022\016\n\006values\030\002 \003(\001\022\017\n\007s&quot; +
      &quot;trings\030\003 \003(\t\&quot;Y\n\017TimeSeriesDatas\022(\n\006Value&quot; +
      &quot;s\030e \003(\0132\030.protobuf.TimeSeriesData\022\017\n\007ret&quot; +
      &quot;code\030f \001(\003\022\013\n\003msg\030g \001(\t\&quot;{\n\016TimeSeriesDat&quot; +
      &quot;a\022\r\n\005SecId\030\001 \002(\t\022\020\n\010Universe\030\002 \001(\t\022%\n\006Va&quot; +
      &quot;lues\030\003 \003(\0132\025.protobuf.TSValuePair\022\016\n\006Dat&quot; +
      &quot;aId\030\004 \001(\t\022\021\n\tErrorCode\030\005 \001(\tB\\\<EMAIL>&quot; +
      &quot;ingstar.martgateway.domains.tscacheproxy&quot; +
      &quot;.entity.protobufB\026TsCacheDataForProtoBuf&quot; +
      &quot;H\001&quot;
    };
<span class="nc" id="L3610">    descriptor = com.google.protobuf.Descriptors.FileDescriptor</span>
<span class="nc" id="L3611">      .internalBuildGeneratedFileFrom(descriptorData,</span>
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_protobuf_TSValuePair_descriptor =
<span class="nc" id="L3615">      getDescriptor().getMessageTypes().get(0);</span>
<span class="nc" id="L3616">    internal_static_protobuf_TSValuePair_fieldAccessorTable = new</span>
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_protobuf_TSValuePair_descriptor,
        new java.lang.String[] { &quot;Dates&quot;, &quot;Values&quot;, &quot;Strings&quot;, });
    internal_static_protobuf_TimeSeriesDatas_descriptor =
<span class="nc" id="L3621">      getDescriptor().getMessageTypes().get(1);</span>
<span class="nc" id="L3622">    internal_static_protobuf_TimeSeriesDatas_fieldAccessorTable = new</span>
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_protobuf_TimeSeriesDatas_descriptor,
        new java.lang.String[] { &quot;Values&quot;, &quot;Retcode&quot;, &quot;Msg&quot;, });
    internal_static_protobuf_TimeSeriesData_descriptor =
<span class="nc" id="L3627">      getDescriptor().getMessageTypes().get(2);</span>
<span class="nc" id="L3628">    internal_static_protobuf_TimeSeriesData_fieldAccessorTable = new</span>
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_protobuf_TimeSeriesData_descriptor,
        new java.lang.String[] { &quot;SecId&quot;, &quot;Universe&quot;, &quot;Values&quot;, &quot;DataId&quot;, &quot;ErrorCode&quot;, });
<span class="nc" id="L3632">  }</span>

  // @@protoc_insertion_point(outer_class_scope)
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>