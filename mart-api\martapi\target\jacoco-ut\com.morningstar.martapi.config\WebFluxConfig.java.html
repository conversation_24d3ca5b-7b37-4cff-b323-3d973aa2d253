<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>WebFluxConfig.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <a href="index.source.html" class="el_package">com.morningstar.martapi.config</a> &gt; <span class="el_source">WebFluxConfig.java</span></div><h1>WebFluxConfig.java</h1><pre class="source lang-java linenums">package com.morningstar.martapi.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.accept.RequestedContentTypeResolverBuilder;
import org.springframework.web.reactive.config.WebFluxConfigurer;

@Configuration
<span class="nc" id="L9">public class WebFluxConfig implements WebFluxConfigurer {</span>

    @Override
    public void configureContentTypeResolver(RequestedContentTypeResolverBuilder builder) {
<span class="nc" id="L13">        builder.parameterResolver().</span>
<span class="nc" id="L14">                parameterName(&quot;format&quot;)</span>
<span class="nc" id="L15">                .mediaType(&quot;xml&quot;, MediaType.APPLICATION_XML)</span>
<span class="nc" id="L16">                .mediaType(&quot;json&quot;, MediaType.APPLICATION_JSON)</span>
<span class="nc" id="L17">                .mediaType(&quot;0&quot;, MediaType.parseMediaType(&quot;application/x-protobuf&quot;))</span>
<span class="nc" id="L18">                .mediaType(&quot;1&quot;, MediaType.APPLICATION_JSON)</span>
<span class="nc" id="L19">                .mediaType(&quot;2&quot;, MediaType.APPLICATION_XML);</span>
<span class="nc" id="L20">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>