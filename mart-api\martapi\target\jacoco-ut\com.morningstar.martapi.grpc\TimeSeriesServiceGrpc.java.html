<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TimeSeriesServiceGrpc.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <a href="index.source.html" class="el_package">com.morningstar.martapi.grpc</a> &gt; <span class="el_source">TimeSeriesServiceGrpc.java</span></div><h1>TimeSeriesServiceGrpc.java</h1><pre class="source lang-java linenums">package com.morningstar.martapi.grpc;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 * &lt;pre&gt;
 * Time Series Service Definition
 * &lt;/pre&gt;
 */
@javax.annotation.Generated(
    value = &quot;by gRPC proto compiler (version 1.58.0)&quot;,
    comments = &quot;Source: timeseries_service.proto&quot;)
@io.grpc.stub.annotations.GrpcGenerated
public final class TimeSeriesServiceGrpc {

  private TimeSeriesServiceGrpc() {}

  public static final java.lang.String SERVICE_NAME = &quot;timeseries.TimeSeriesService&quot;;

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor&lt;com.morningstar.martapi.grpc.TimeSeriesRequest,
      com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas&gt; getGetTimeSeriesDataMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + &quot;GetTimeSeriesData&quot;,
      requestType = com.morningstar.martapi.grpc.TimeSeriesRequest.class,
      responseType = com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor&lt;com.morningstar.martapi.grpc.TimeSeriesRequest,
      com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas&gt; getGetTimeSeriesDataMethod() {
    io.grpc.MethodDescriptor&lt;com.morningstar.martapi.grpc.TimeSeriesRequest, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas&gt; getGetTimeSeriesDataMethod;
    if ((getGetTimeSeriesDataMethod = TimeSeriesServiceGrpc.getGetTimeSeriesDataMethod) == null) {
      synchronized (TimeSeriesServiceGrpc.class) {
        if ((getGetTimeSeriesDataMethod = TimeSeriesServiceGrpc.getGetTimeSeriesDataMethod) == null) {
          TimeSeriesServiceGrpc.getGetTimeSeriesDataMethod = getGetTimeSeriesDataMethod =
              io.grpc.MethodDescriptor.&lt;com.morningstar.martapi.grpc.TimeSeriesRequest, com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas&gt;newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, &quot;GetTimeSeriesData&quot;))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.morningstar.martapi.grpc.TimeSeriesRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas.getDefaultInstance()))
              .setSchemaDescriptor(new TimeSeriesServiceMethodDescriptorSupplier(&quot;GetTimeSeriesData&quot;))
              .build();
        }
      }
    }
    return getGetTimeSeriesDataMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static TimeSeriesServiceStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory&lt;TimeSeriesServiceStub&gt; factory =
<span class="nc" id="L57">      new io.grpc.stub.AbstractStub.StubFactory&lt;TimeSeriesServiceStub&gt;() {</span>
        @java.lang.Override
        public TimeSeriesServiceStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
<span class="nc" id="L60">          return new TimeSeriesServiceStub(channel, callOptions);</span>
        }
      };
    return TimeSeriesServiceStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static TimeSeriesServiceBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory&lt;TimeSeriesServiceBlockingStub&gt; factory =
<span class="nc" id="L72">      new io.grpc.stub.AbstractStub.StubFactory&lt;TimeSeriesServiceBlockingStub&gt;() {</span>
        @java.lang.Override
        public TimeSeriesServiceBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
<span class="nc" id="L75">          return new TimeSeriesServiceBlockingStub(channel, callOptions);</span>
        }
      };
    return TimeSeriesServiceBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static TimeSeriesServiceFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory&lt;TimeSeriesServiceFutureStub&gt; factory =
<span class="nc" id="L87">      new io.grpc.stub.AbstractStub.StubFactory&lt;TimeSeriesServiceFutureStub&gt;() {</span>
        @java.lang.Override
        public TimeSeriesServiceFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
<span class="nc" id="L90">          return new TimeSeriesServiceFutureStub(channel, callOptions);</span>
        }
      };
    return TimeSeriesServiceFutureStub.newStub(factory, channel);
  }

  /**
   * &lt;pre&gt;
   * Time Series Service Definition
   * &lt;/pre&gt;
   */
  public interface AsyncService {

    /**
     * &lt;pre&gt;
     * Retrieve time series data as protobuf
     * &lt;/pre&gt;
     */
    default void getTimeSeriesData(com.morningstar.martapi.grpc.TimeSeriesRequest request,
        io.grpc.stub.StreamObserver&lt;com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas&gt; responseObserver) {
<span class="nc" id="L110">      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetTimeSeriesDataMethod(), responseObserver);</span>
<span class="nc" id="L111">    }</span>
  }

  /**
   * Base class for the server implementation of the service TimeSeriesService.
   * &lt;pre&gt;
   * Time Series Service Definition
   * &lt;/pre&gt;
   */
<span class="nc" id="L120">  public static abstract class TimeSeriesServiceImplBase</span>
      implements io.grpc.BindableService, AsyncService {

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
<span class="nc" id="L124">      return TimeSeriesServiceGrpc.bindService(this);</span>
    }
  }

  /**
   * A stub to allow clients to do asynchronous rpc calls to service TimeSeriesService.
   * &lt;pre&gt;
   * Time Series Service Definition
   * &lt;/pre&gt;
   */
  public static final class TimeSeriesServiceStub
      extends io.grpc.stub.AbstractAsyncStub&lt;TimeSeriesServiceStub&gt; {
    private TimeSeriesServiceStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
<span class="nc" id="L138">      super(channel, callOptions);</span>
<span class="nc" id="L139">    }</span>

    @java.lang.Override
    protected TimeSeriesServiceStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
<span class="nc" id="L144">      return new TimeSeriesServiceStub(channel, callOptions);</span>
    }

    /**
     * &lt;pre&gt;
     * Retrieve time series data as protobuf
     * &lt;/pre&gt;
     */
    public void getTimeSeriesData(com.morningstar.martapi.grpc.TimeSeriesRequest request,
        io.grpc.stub.StreamObserver&lt;com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas&gt; responseObserver) {
<span class="nc" id="L154">      io.grpc.stub.ClientCalls.asyncUnaryCall(</span>
<span class="nc" id="L155">          getChannel().newCall(getGetTimeSeriesDataMethod(), getCallOptions()), request, responseObserver);</span>
<span class="nc" id="L156">    }</span>
  }

  /**
   * A stub to allow clients to do synchronous rpc calls to service TimeSeriesService.
   * &lt;pre&gt;
   * Time Series Service Definition
   * &lt;/pre&gt;
   */
  public static final class TimeSeriesServiceBlockingStub
      extends io.grpc.stub.AbstractBlockingStub&lt;TimeSeriesServiceBlockingStub&gt; {
    private TimeSeriesServiceBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
<span class="nc" id="L169">      super(channel, callOptions);</span>
<span class="nc" id="L170">    }</span>

    @java.lang.Override
    protected TimeSeriesServiceBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
<span class="nc" id="L175">      return new TimeSeriesServiceBlockingStub(channel, callOptions);</span>
    }

    /**
     * &lt;pre&gt;
     * Retrieve time series data as protobuf
     * &lt;/pre&gt;
     */
    public com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas getTimeSeriesData(com.morningstar.martapi.grpc.TimeSeriesRequest request) {
<span class="nc" id="L184">      return io.grpc.stub.ClientCalls.blockingUnaryCall(</span>
<span class="nc" id="L185">          getChannel(), getGetTimeSeriesDataMethod(), getCallOptions(), request);</span>
    }
  }

  /**
   * A stub to allow clients to do ListenableFuture-style rpc calls to service TimeSeriesService.
   * &lt;pre&gt;
   * Time Series Service Definition
   * &lt;/pre&gt;
   */
  public static final class TimeSeriesServiceFutureStub
      extends io.grpc.stub.AbstractFutureStub&lt;TimeSeriesServiceFutureStub&gt; {
    private TimeSeriesServiceFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
<span class="nc" id="L199">      super(channel, callOptions);</span>
<span class="nc" id="L200">    }</span>

    @java.lang.Override
    protected TimeSeriesServiceFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
<span class="nc" id="L205">      return new TimeSeriesServiceFutureStub(channel, callOptions);</span>
    }

    /**
     * &lt;pre&gt;
     * Retrieve time series data as protobuf
     * &lt;/pre&gt;
     */
    public com.google.common.util.concurrent.ListenableFuture&lt;com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas&gt; getTimeSeriesData(
        com.morningstar.martapi.grpc.TimeSeriesRequest request) {
<span class="nc" id="L215">      return io.grpc.stub.ClientCalls.futureUnaryCall(</span>
<span class="nc" id="L216">          getChannel().newCall(getGetTimeSeriesDataMethod(), getCallOptions()), request);</span>
    }
  }

  private static final int METHODID_GET_TIME_SERIES_DATA = 0;

  private static final class MethodHandlers&lt;Req, Resp&gt; implements
      io.grpc.stub.ServerCalls.UnaryMethod&lt;Req, Resp&gt;,
      io.grpc.stub.ServerCalls.ServerStreamingMethod&lt;Req, Resp&gt;,
      io.grpc.stub.ServerCalls.ClientStreamingMethod&lt;Req, Resp&gt;,
      io.grpc.stub.ServerCalls.BidiStreamingMethod&lt;Req, Resp&gt; {
    private final AsyncService serviceImpl;
    private final int methodId;

<span class="nc" id="L230">    MethodHandlers(AsyncService serviceImpl, int methodId) {</span>
<span class="nc" id="L231">      this.serviceImpl = serviceImpl;</span>
<span class="nc" id="L232">      this.methodId = methodId;</span>
<span class="nc" id="L233">    }</span>

    @java.lang.Override
    @java.lang.SuppressWarnings(&quot;unchecked&quot;)
    public void invoke(Req request, io.grpc.stub.StreamObserver&lt;Resp&gt; responseObserver) {
<span class="nc bnc" id="L238" title="All 2 branches missed.">      switch (methodId) {</span>
        case METHODID_GET_TIME_SERIES_DATA:
<span class="nc" id="L240">          serviceImpl.getTimeSeriesData((com.morningstar.martapi.grpc.TimeSeriesRequest) request,</span>
              (io.grpc.stub.StreamObserver&lt;com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas&gt;) responseObserver);
<span class="nc" id="L242">          break;</span>
        default:
<span class="nc" id="L244">          throw new AssertionError();</span>
      }
<span class="nc" id="L246">    }</span>

    @java.lang.Override
    @java.lang.SuppressWarnings(&quot;unchecked&quot;)
    public io.grpc.stub.StreamObserver&lt;Req&gt; invoke(
        io.grpc.stub.StreamObserver&lt;Resp&gt; responseObserver) {
<span class="nc" id="L252">      switch (methodId) {</span>
        default:
<span class="nc" id="L254">          throw new AssertionError();</span>
      }
    }
  }

  public static final io.grpc.ServerServiceDefinition bindService(AsyncService service) {
    return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
        .addMethod(
          getGetTimeSeriesDataMethod(),
          io.grpc.stub.ServerCalls.asyncUnaryCall(
            new MethodHandlers&lt;
              com.morningstar.martapi.grpc.TimeSeriesRequest,
              com.morningstar.martgateway.domains.tscacheproxy.entity.protobuf.TsCacheDataForProtoBuf.TimeSeriesDatas&gt;(
                service, METHODID_GET_TIME_SERIES_DATA)))
        .build();
  }

  private static abstract class TimeSeriesServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
<span class="nc" id="L273">    TimeSeriesServiceBaseDescriptorSupplier() {}</span>

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
<span class="nc" id="L277">      return com.morningstar.martapi.grpc.TimeSeriesServiceProto.getDescriptor();</span>
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
<span class="nc" id="L282">      return getFileDescriptor().findServiceByName(&quot;TimeSeriesService&quot;);</span>
    }
  }

  private static final class TimeSeriesServiceFileDescriptorSupplier
      extends TimeSeriesServiceBaseDescriptorSupplier {
<span class="nc" id="L288">    TimeSeriesServiceFileDescriptorSupplier() {}</span>
  }

  private static final class TimeSeriesServiceMethodDescriptorSupplier
      extends TimeSeriesServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final java.lang.String methodName;

<span class="nc" id="L296">    TimeSeriesServiceMethodDescriptorSupplier(java.lang.String methodName) {</span>
<span class="nc" id="L297">      this.methodName = methodName;</span>
<span class="nc" id="L298">    }</span>

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
<span class="nc" id="L302">      return getServiceDescriptor().findMethodByName(methodName);</span>
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (TimeSeriesServiceGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new TimeSeriesServiceFileDescriptorSupplier())
              .addMethod(getGetTimeSeriesDataMethod())
              .build();
        }
      }
    }
    return result;
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>