<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DataPointUpdateController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <a href="index.source.html" class="el_package">com.morningstar.martapi.controller</a> &gt; <span class="el_source">DataPointUpdateController.java</span></div><h1>DataPointUpdateController.java</h1><pre class="source lang-java linenums">package com.morningstar.martapi.controller;

import com.morningstar.martapi.service.RedisMessagePublisher;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import javax.inject.Inject;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import springfox.documentation.annotations.ApiIgnore;

@RestController
@RequestMapping(value = {&quot;/v1/data-point&quot;,&quot;/investment-api/v1/data-point&quot;},
        produces = {MediaType.APPLICATION_JSON_VALUE})
@ApiIgnore
public class DataPointUpdateController {

    private final RedisMessagePublisher redisMessagePublisher;

    @Inject
<span class="nc" id="L24">    public DataPointUpdateController(RedisMessagePublisher redisMessagePublisher) {</span>
<span class="nc" id="L25">        this.redisMessagePublisher = redisMessagePublisher;</span>
<span class="nc" id="L26">    }</span>

    @GetMapping(&quot;/publish&quot;)
    public Mono&lt;String&gt; publish() {
<span class="nc" id="L30">        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern(&quot;yyyyMMdd-HHmmss&quot;));</span>
<span class="nc" id="L31">        String message = &quot;config-update-&quot; + timestamp;</span>
<span class="nc" id="L32">        redisMessagePublisher.publishSync(message);</span>
<span class="nc" id="L33">        return Mono.just(&quot;Success&quot;);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>