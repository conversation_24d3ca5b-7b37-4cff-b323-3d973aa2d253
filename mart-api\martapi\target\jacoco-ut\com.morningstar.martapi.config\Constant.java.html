<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Constant.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">martapi</a> &gt; <a href="index.source.html" class="el_package">com.morningstar.martapi.config</a> &gt; <span class="el_source">Constant.java</span></div><h1>Constant.java</h1><pre class="source lang-java linenums">package com.morningstar.martapi.config;

import java.util.Set;

<span class="nc" id="L5">public final class Constant {</span>
    public static final String X_API_PRODUCT_ID = &quot;X-Api-ProductId&quot;;
    public static final String X_API_REQUEST_ID = &quot;X-Api-RequestId&quot;;
    public static final String RESPONSE = &quot;response&quot;;
    public static final String SUCCESSFUL_RESPONSE = &quot;Successful response&quot;;
    public static final String RESPONSE_ERROR = &quot;response error&quot;;
    // used for async api
    public static final String SUCCESS = &quot;Success&quot;;
    public static final String FAIL = &quot;Fail&quot;;
    public static final String RUNNING = &quot;Running&quot;;
    public static final String SUBMITTED = &quot;Submitted&quot;;

    public static final long LOG_REQUEST_PAYLOAD_LIMIT_MILLISECONDS = 1000;

    // used for transcript api
    public static final String COMPANY_ID_DATAPOINT = &quot;EQOO4&quot;;
    public static final String JOB_ID_DATAPOINT = &quot;EQN3D&quot;;
    public static final String EVENT_DATETIME_DATAPOINT = &quot;EQ56B&quot;;
    public static final String DOCUMENT_TYPE = &quot;EQ5NQ&quot;;
    public static final String PACKAGE_ID = &quot;941&quot;;

<span class="nc" id="L26">    public static final Set&lt;String&gt; VALID_USE_CASES = Set.of(&quot;view&quot;, &quot;export&quot;, &quot;feed&quot;);</span>

<span class="nc" id="L28">    public static final Set&lt;String&gt; EXCLUDED_PRODUCT_IDS = Set.of(&quot;DWS&quot;,&quot;EQAPI&quot;);</span>

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.10.202304240956</span></div></body></html>